<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.FavoritesQstMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="favoritesQstResultMap" type="org.springcrazy.modules.exam.vo.FavoritesQstVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="qst_id" property="qstId"/>
        <result column="add_date" property="addDate"/>
        <result column="qst_content" property="qstContent"/>
        <result column="isAsr" property="isAsr"/>
        <result column="qstType" property="qstType"/>
        <result column="option_list" property="optionList"/>
        <result column="qst_analyze" property="qstAnalyze"/>
    </resultMap>


    <select id="selectFavoritesQstPage" resultMap="favoritesQstResultMap">
        select exam_favorites_qst.id,exam_favorites_qst.user_id,exam_favorites_qst.qst_id,
        exam_question.`qst_content`, exam_question.`isAsr`, exam_question.`qstType`,exam_question.`option_list`,exam_question.`qst_analyze`
        from exam_favorites_qst left join exam_question on exam_favorites_qst.qst_id = exam_question.id where 1=1
        <if test="ew.userId != null and ew.userId > 0">
            and exam_favorites_qst.user_id = #{ew.userId}
        </if>
        order by exam_favorites_qst.add_date desc
    </select>



    <select id="selectFavoritesQstList" resultMap="favoritesQstResultMap">
        select exam_favorites_qst.id,exam_favorites_qst.user_id,exam_favorites_qst.qst_id,
        exam_question.`qst_content`, exam_question.`isAsr`, exam_question.`qstType`,exam_question.`option_list`,exam_question.`qst_analyze`
        from exam_favorites_qst left join exam_question on exam_favorites_qst.qst_id = exam_question.id where 1=1
        <if test="userId != null and userId > 0">
            and exam_favorites_qst.user_id = #{userId}
        </if>
        order by exam_favorites_qst.add_date desc
    </select>

    <select id="getFavoriteNums" resultType="java.lang.Integer">
        SELECT
        IFNULL(count(*),0)
        FROM exam_favorites_qst
        LEFT JOIN exam_question on exam_favorites_qst.qst_id = exam_question.id
        <where>
--             DATE_SUB(CURDATE(), INTERVAL 7 DAY) <![CDATA[ <= ]]> DATE(add_date)
            exam_favorites_qst.user_id=#{userId}
            AND exam_question.subject_id = #{subjectId}
        </where>
    </select>

</mapper>
