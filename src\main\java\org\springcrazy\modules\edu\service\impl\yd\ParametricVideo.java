package org.springcrazy.modules.edu.service.impl.yd;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springcrazy.core.tool.utils.Func;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * https://ecloud.10086.cn/op-help-center/request-api/service-api/resource/file/d6dbb5f16585f70eb669c3594c02c9ae
 * <p>
 * 移动视频播放文档
 */

@Component
public class ParametricVideo {
    private static final Logger log = LoggerFactory.getLogger(ParametricVideo.class);

    @Value("${yd.uid}")
    private String uid;
    @Value("${yd.secretId}")
    private String secretId;
    @Value("${yd.secretKey}")
    private String secretKey;
    @Value("${yd.getUrl}")
    private String getUrl;
    @Value("${yd.postUrl}")
    private String postUrl;
    @Value("${yd.vagentUrl}")
    private String vagentUrl;
    @Value("${yd.ydyDomain}")
    private String ydyDomain;


    public String getVurl(String vid) {
        String vurl = "";
        //get请求(获取视频点播媒资播放地址接口)
        Map<String, Object> param = new HashMap<>();
        //GET请求必须传业务相关参数,用于拼接url
        param.put("vid", vid);
        String reslut = ydApiRequest(param, "", uid, secretId, secretKey,
                "GET", getUrl);
        JSONObject jo = null;
        try {
            jo = JSONUtil.parseObj(reslut);
            if ("0".equals(jo.get("ret", String.class))) {
                JSONObject result = jo.get("result", JSONObject.class);
                JSONArray list = result.get("list", JSONArray.class);
                if (!list.isEmpty()) {
                    JSONObject jsonObject1 = list.get(0, JSONObject.class);
                    vurl = jsonObject1.get("vurl", String.class);
                    // 替换http为https
                    vurl = vurl.replace("http://", "https://");
                    log.info("{}的播放地址为：{}", vid, vurl);
                }
            }
        } catch (Exception ex) {
            log.info(vid + "获取视频点播媒资播放地址失败_" + ex.getMessage(), ex);
        }
       /* if(Func.isNotEmpty(vurl)){
            //因为移动云视频地址是http协议，所以需要在nginx里面配置反向代理
            vurl = vurl.replace(ydyDomain,vagentUrl);
        }*/
        return vurl;
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
        Map<String, Object> param = new HashMap<>();
        //post请求(例如获取视频点播媒资列表接口)
        String body = "{\"vid\":\"0EQe9JEC19crFx94sWjr8y\"}";
        ydApiRequest(param, body, "200013235", "911c22d25c503ba0082307fad0a75836", "d28120dca80f572e22885685f54ef92e",
                "POST", "https://mgspy-api-beijing-3.cmecloud.cn/vod2/v0/getVideoList");

        //get请求(获取视频点播媒资播放地址接口)
        param.clear();
        //GET请求必须传业务相关参数,用于拼接url
        param.put("vid","0EQe9JEC19crFx94sWjr8y");
        ydApiRequest(param, body, "200013235", "911c22d25c503ba0082307fad0a75836", "d28120dca80f572e22885685f54ef92e",
                "GET", "https://mgspy-api-beijing-3.cmecloud.cn/vod2/v1/getUrl");
    }


    public static String ydApiRequest(Map<String, Object> param, String reqJson, String uid,
                                      String secretId, String secretKey, String reqType, String url) {
        //接口响应结果
        String reslut = "";
        if (Func.equals("POST", reqType)) {
            try {
                param.put("body", URLEncoder.encode(reqJson, "utf-8"));
            } catch (Exception e) {
                log.info(reqJson + "移动云URLEncoder失败_" + e.getMessage(), e);
            }
        } else {
            //固定值
            param.put("body", "%5B%5D");
        }
        //为能力开发者在移动云平台上，视频点播产品的用户 id
        param.put("uid", uid);
        //为能力开发者在移动云平台上，视频点播产品的密钥 id
        param.put("secretId", secretId);
        //请求发起时的时间戳毫秒级
        long currentTimeStamp = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 时间戳转换成时间
        String sd = sdf.format(new Date(Long.parseLong(String.valueOf(currentTimeStamp))));
        log.info("移动云业务发起时间: {}", sd);
        param.put("currentTimeStamp", currentTimeStamp);
        //签名过期时间,毫秒级时间戳
        //currentTimeStamp 和 expired 之间时间差值不可以超过 1 天
        param.put("expired", currentTimeStamp + 24 * 60 * 60 * 1000);
        log.info("移动云签名过期时间: {}", sdf.format(new Date(Long.parseLong(String.valueOf(currentTimeStamp + 24 * 60 * 60 * 1000)))));
        //8位随机字符串（纯数字）
        param.put("random", RandomUtil.randomNumbers(8));
        //签名所拥有的权限 id 固定值：200001
        param.put("apiId", "200001");
        //版本号 固定值 v2.0
        param.put("grant_type", "v2.0");
        String[] allKeys = param.keySet().toArray(new String[0]);
        Arrays.sort(allKeys);
        //拼接有序的参数串
        StringBuilder stringBuilder = new StringBuilder();
        for (String key : allKeys) {
            Object value = param.get(key);
            if (value != null && !"".equals(value))
                stringBuilder.append(key).append("=").append(value).append("&");
        }
        String $data = stringBuilder.toString();
        String $token = sha256_HMAC($data.substring(0, $data.length() - 1), secretKey);
        param.put("token", $token);
        //body不需要拼接在url中
        param.remove("body");
        allKeys = param.keySet().toArray(new String[0]);
        Arrays.sort(allKeys);
        //拼接有序的参数串
        StringBuilder sbUrlParam = new StringBuilder();
        for (String key : allKeys) {
            Object value = param.get(key);
            if (value != null && !"".equals(value))
                sbUrlParam.append(key).append("=").append(value).append("&");
        }
        String urlParam = sbUrlParam.substring(0, sbUrlParam.length() - 1);
        url = url + "?" + urlParam;
        log.info("移动云请求地址: {}", url);

        try {
            if (Func.equals("POST", reqType)) {
                log.info("移动云请求BODY(POST): {}", reqJson);
                reslut = HttpUtil.createPost(url)
                        .contentType("application/json")
                        .body(reqJson).execute().body();
            } else {
                reslut = HttpUtil.get(url);
            }
        } catch (Exception e) {
            log.info(reqJson + "移动云获取视频点播地址失败_" + e.getMessage(), e);
        }
        log.info("移动云请求响应结果： {}", reslut);
        return reslut;
    }

    /**
     * 将加密后的字节数组转换成字符串
     *
     * @param b 字节数组
     * @return 字符串
     */
    private static String byteArrayToHexString(byte[] b) {
        StringBuilder hs = new StringBuilder();
        String stmp;
        for (int n = 0; b != null && n < b.length; n++) {
            stmp = Integer.toHexString(b[n] & 0XFF);
            if (stmp.length() == 1)
                hs.append('0');
            hs.append(stmp);
        }
        return hs.toString().toLowerCase();
    }

    /**
     * sha256_HMAC加密
     *
     * @param message 消息
     * @param secret  秘钥
     * @return 加密后字符串
     */
    private static String sha256_HMAC(String message, String secret) {
        String hash = "";
        try {
            log.info("移动云签名原文： {}", message);
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            byte[] bytes = sha256_HMAC.doFinal(message.getBytes());
            hash = byteArrayToHexString(bytes);
            log.info("移动云签名密文： {}", hash);
        } catch (Exception e) {
            log.info(message + "移动云sha256_HMAC加密失败_" + e.getMessage(), e);
        }
        return hash;
    }
}
