<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.SensitiveWordsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sensitiveWordsResultMap" type="org.springcrazy.modules.web.entity.SensitiveWords">
        <id column="id" property="id"/>
        <result column="sensitiveWord" property="sensitiveWord"/>
    </resultMap>


    <select id="selectSensitiveWordsPage" resultMap="sensitiveWordsResultMap">
        select * from web_sensitive_words where is_deleted = 0
    </select>

    <select id="selectSensitiveWordsPageList" resultMap="sensitiveWordsResultMap">
        select * from web_sensitive_words
        <where>
            <if test="sensitiveWords.sensitiveWord!=null and sensitiveWords.sensitiveWord!= ''">
                and web_sensitive_words.sensitiveWord like concat('%',#{sensitiveWords.sensitiveWord},'%')
            </if>
        </where>
    </select>

</mapper>
