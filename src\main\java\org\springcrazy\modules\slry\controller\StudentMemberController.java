package org.springcrazy.modules.slry.controller;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.springcrazy.core.secure.CrazyUser;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.slry.entity.CompanyWhitelist;
import org.springcrazy.modules.slry.service.StudentMemberService;
import org.springcrazy.modules.slry.entity.StudentMember;
import org.springcrazy.modules.system.excel.*;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 员工账号管理
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/edu/member")
public class StudentMemberController {
    private StudentMemberService studentMemberService;

    /**
     * 分页 员工账号
     *
     * @param studentMember 员工账号
     * @param currentPage   当前页
     * @param pageSize      每页条数
     * @return
     */
    @GetMapping("/list")
    public R<IPage<StudentMember>> list(StudentMember studentMember, @RequestParam Integer currentPage, @RequestParam Integer pageSize) {
        //分页构造器
        Page<StudentMember> pages = new Page<>(currentPage, pageSize);
        //条件查询器
        LambdaQueryWrapper<StudentMember> queryWrapper = new LambdaQueryWrapper<>();
        //添加条件
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getRealName()), StudentMember::getRealName, studentMember.getRealName());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getQymc()), StudentMember::getQymc, studentMember.getQymc());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getIdCardNo()), StudentMember::getIdCardNo, studentMember.getIdCardNo());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getMobile()), StudentMember::getMobile, studentMember.getMobile());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getAgentName()), StudentMember::getAgentName, studentMember.getAgentName());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getRylb()), StudentMember::getRylb, studentMember.getRylb());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getZxtype()), StudentMember::getZxtype, studentMember.getZxtype());
        queryWrapper.eq(studentMember.getPaymentType() != null, StudentMember::getPaymentType, studentMember.getPaymentType());
        // 添加时间区间查询
        if (ArrayUtil.isNotEmpty(studentMember.getTimeArr())) {
            queryWrapper.gt(StudentMember::getCreateTime, studentMember.getTimeArr()[0]);
            queryWrapper.lt(StudentMember::getCreateTime, studentMember.getTimeArr()[1]);
        }
        queryWrapper.orderByDesc(StudentMember::getCreateTime);
        //分页查询
        studentMemberService.page(pages, queryWrapper);
        return R.data(pages);
    }


    /**
     * 删除 员工账号
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(studentMemberService.removeByIds(Func.toIntList(ids)));
    }


    /**
     * 导入员工账号
     */
    @PostMapping("import-member")
    @ApiOperationSupport(order = 12)
    @ApiOperation(value = "导入员工账号", notes = "传入excel")
    public R importUser(MultipartFile file) {
        String filename = file.getOriginalFilename();
        if (StringUtils.isEmpty(filename)) {
            throw new RuntimeException("请上传文件!");
        }
        if ((!StringUtils.endsWithIgnoreCase(filename, ".xls") && !StringUtils.endsWithIgnoreCase(filename, ".xlsx"))) {
            throw new RuntimeException("请上传正确的excel文件!");
        }
        InputStream inputStream;
        try {
            StudentMemberImportListener importListener = new StudentMemberImportListener(studentMemberService);
            inputStream = new BufferedInputStream(file.getInputStream());
            ExcelReaderBuilder builder = EasyExcel.read(inputStream, StudentMemberExcel.class, importListener);
            builder.doReadAll();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return R.success("操作成功");
    }


    /**
     * 导出员工账号
     */
    @SneakyThrows
    @GetMapping("export-member")
    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "导出账号", notes = "传入parameter")
    public void exportMember(@ApiIgnore @RequestParam Map<String, Object> parameter, StudentMember studentMember, HttpServletResponse response) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String date = simpleDateFormat.format(new Date());
        LambdaQueryWrapper<StudentMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getRealName()) && !StrUtil.equals("undefined", studentMember.getRealName()), StudentMember::getRealName, studentMember.getRealName());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getQymc()) && !StrUtil.equals("undefined", studentMember.getQymc()), StudentMember::getQymc, studentMember.getQymc());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getIdCardNo()) && !StrUtil.equals("undefined", studentMember.getIdCardNo()), StudentMember::getIdCardNo, studentMember.getIdCardNo());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getMobile()) && !StrUtil.equals("undefined", studentMember.getMobile()), StudentMember::getMobile, studentMember.getMobile());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getAgentName()) && !StrUtil.equals("undefined", studentMember.getAgentName()), StudentMember::getAgentName, studentMember.getAgentName());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getRylb()) && !StrUtil.equals("undefined", studentMember.getRylb()), StudentMember::getRylb, studentMember.getRylb());
        queryWrapper.like(StrUtil.isNotBlank(studentMember.getZxtype()) && !StrUtil.equals("undefined", studentMember.getZxtype()), StudentMember::getZxtype, studentMember.getZxtype());
        queryWrapper.eq(StrUtil.isNotBlank(studentMember.getPaymentTypeStr()) && !StrUtil.equals("undefined", studentMember.getPaymentTypeStr()), StudentMember::getPaymentType, studentMember.getPaymentTypeStr());
        queryWrapper.eq(StudentMember::getIsDeleted, 0);
        // 添加时间区间查询
        if (ArrayUtil.isNotEmpty(studentMember.getTimeArr())) {
            queryWrapper.gt(StudentMember::getCreateTime, studentMember.getTimeArr()[0]);
            queryWrapper.lt(StudentMember::getCreateTime, studentMember.getTimeArr()[1]);
        }
        queryWrapper.orderByDesc(StudentMember::getCreateTime);
        List<StudentMemberExcel> list = studentMemberService.exportMember(queryWrapper);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(Charsets.UTF_8.name());
        String fileName = URLEncoder.encode("账号数据导出", Charsets.UTF_8.name());
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + date + ".xlsx");
        EasyExcel.write(response.getOutputStream(), StudentMemberExcel.class).sheet("账号数据表").doWrite(list);
    }
}
