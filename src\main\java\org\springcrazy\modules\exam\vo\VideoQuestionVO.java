
package org.springcrazy.modules.exam.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.springcrazy.core.tool.node.INode;
import org.springcrazy.modules.exam.entity.VideoQuestion;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频课程对应弹出题目视图实体类
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "VideoQuestionVO对象", description = "视频课程对应弹出题目")
public class VideoQuestionVO extends VideoQuestion {
    private static final long serialVersionUID = 1L;
}
