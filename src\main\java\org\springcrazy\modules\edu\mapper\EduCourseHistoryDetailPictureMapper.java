package org.springcrazy.modules.edu.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;

/**
 * <AUTHOR>
 * @description 针对表【edu_course_history_detail_picture】的数据库操作Mapper
 * @createDate 2023-06-05 11:35:02
 * @Entity generator.domain.EduCourseHistoryDetailPicture
 */
public interface EduCourseHistoryDetailPictureMapper extends BaseMapper<EduCourseHistoryDetailPicture> {

    IPage<EduCourseHistoryDetailPicture> getAbnormalIPVOs(
            IPage<EduCourseHistoryDetailPicture> page, @Param(Constants.WRAPPER) QueryWrapper<EduCourseHistoryDetailPicture> wrapper);
}




