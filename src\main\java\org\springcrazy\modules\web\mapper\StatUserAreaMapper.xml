<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.StatUserAreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="statUserAreaResultMap" type="org.springcrazy.modules.web.entity.StatUserArea">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="ip" property="ip"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectStatUserAreaPage" resultMap="statUserAreaResultMap">
        select * from web_stat_user_area where is_deleted = 0
    </select>

</mapper>
