<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.agent.mapper.AgentAttendanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcrazy.modules.agent.entity.AgentAttendance">
        <id column="id" property="id" />
    <result column="create_user" property="createUser" />
    <result column="create_time" property="createTime" />
    <result column="update_user" property="updateUser" />
    <result column="update_time" property="updateTime" />
    <result column="status" property="status" />
    <result column="is_deleted" property="isDeleted" />
        <result column="agent_id" property="agentId" />
        <result column="name" property="name" />
        <result column="codeNum" property="codeNum" />
        <result column="ip" property="ip" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_user,
        create_time,
        update_user,
        update_time,
        status,
        is_deleted,
        id, agent_id, name, codeNum, ip
    </sql>

</mapper>
