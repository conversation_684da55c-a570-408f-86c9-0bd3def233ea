<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.user.mapper.TeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="teacherResultMap" type="org.springcrazy.modules.user.entity.Teacher">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="education" property="education"/>
        <result column="profile" property="profile"/>
        <result column="tea_title" property="teaTitle"/>
        <result column="head_img" property="headImg"/>
        <result column="type" property="type"/>
        <result column="subject_id" property="subjectId"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="agent_id" property="agentId"/>
    </resultMap>


    <select id="selectTeacherPage" resultMap="teacherResultMap">
        select * from edu_teacher
        where is_deleted = 0
        <if test="teacher.agentId !=null">
            and agent_id = #{teacher.agentId}
        </if>
        order by sort desc, id desc
    </select>

</mapper>
