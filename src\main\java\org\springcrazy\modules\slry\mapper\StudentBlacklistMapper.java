package org.springcrazy.modules.slry.mapper;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springcrazy.modules.slry.entity.StudentBlacklist;

public interface StudentBlacklistMapper extends BaseMapper<StudentBlacklist> {

    Integer countByCompanyName(@Param("companyName")String companyName);

    Integer countByStudentIdCardNo(@Param("studentIdCardNo")String studentIdCardNo);

}