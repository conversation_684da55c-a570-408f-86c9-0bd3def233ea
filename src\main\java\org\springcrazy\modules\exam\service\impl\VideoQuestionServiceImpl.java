package org.springcrazy.modules.exam.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springcrazy.modules.exam.entity.VideoQuestion;
import org.springcrazy.modules.exam.mapper.VideoQuestionMapper;
import org.springcrazy.modules.exam.service.IVideoQuestionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.modules.exam.vo.PointVO;
import org.springcrazy.modules.exam.vo.VideoQuestionVO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 视频课程对应弹出题目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Service
public class VideoQuestionServiceImpl extends ServiceImpl<VideoQuestionMapper, VideoQuestion> implements IVideoQuestionService {

    @Override
    public IPage<VideoQuestionVO> selectVideoQuestionPage(IPage<VideoQuestionVO> page, VideoQuestionVO videoQuestion) {
        return page.setRecords(baseMapper.selectVideoQuestionPage(page, videoQuestion));
    }
}
