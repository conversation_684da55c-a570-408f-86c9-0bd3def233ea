<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.system.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="deptResultMap" type="org.springcrazy.modules.system.entity.Dept">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="dept_name" property="deptName"/>
        <result column="full_name" property="fullName"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springcrazy.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select
        id, parent_id, dept_name, full_name, sort, remark, is_deleted
    </sql>

    <select id="selectDeptPage" resultMap="deptResultMap">
        select * from crazy_dept where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dept_name as title, id as 'value', id as 'key' from crazy_dept where is_deleted = 0
        <if test="_parameter!=null">
            and tenant_id = #{_parameter}
        </if>
    </select>

    <select id="getDeptNames" resultType="java.lang.String">
        SELECT
        dept_name
        FROM
        crazy_dept
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <update id="updateDeptParentid" >
        UPDATE  crazy_dept
        SET parent_id=
        (SELECT parent_id FROM
            (SELECT * FROM crazy_dept WHERE id=#{id})
            a)
        WHERE parent_id=#{id};
    </update>

    <delete id="delectDeptIds" >
         DELETE FROM `crazy_dept` WHERE id=#{id}
    </delete>
</mapper>
