<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.cms.mapper.CmsSubjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="subjectResultMap" type="org.springcrazy.modules.cms.entity.CmsSubject">
        <id column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="subject_name" property="subjectName"/>
        <result column="parent_id" property="parentId"/>
        <result column="sort" property="sort"/>
        <result column="type" property="type"/>
        <result column="courseSubjectId" property="courseSubjectId"/>
        <result column="agent_id" property="agentId"/>
    </resultMap>


    <select id="selectSubjectPage" resultMap="subjectResultMap">
        select * from cms_subject where is_deleted = 0
    </select>

</mapper>
