package org.springcrazy.modules.user.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.facebody20191230.models.CompareFaceRequest;
import com.aliyun.facebody20191230.models.DetectLivingFaceResponse;
import com.aliyun.tea.TeaException;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.exam.vo.FaceResult;
import org.springframework.stereotype.Component;

import static org.springcrazy.common.tool.Base64Util.convertImageToBase64Str;


/**
 * <AUTHOR>
 * <p>调用阿里云人脸识别接口测试</p>
 **/
@Component
@Slf4j
public class AliFaceTest {
    public static String alifaceapi_secretId = "LTAI5tALPXrZ9VZH4PXGXKpv";
    private static String alifaceapi_secretKey = "******************************";
    private static String alifaceapi_qualityscorethreshold = "0.0";
    private static String alifaceapi_confidence = "61";

//    public static void main(String[] args) {
//        // String date = DateUtil.format(new Date(), "yyyyMMddHHmmss");
//        // log.info(date);
//
//    /*    FaceResult result = detectLivingFace(netImageToBase64("https://agry.gsjtsz.com/upload/student/face_recognition_1.png"));*/
//        /*FaceResult result = detectLivingFace(netImageToBase64("https://agry.gsjtsz.com/upload/student/de4f7fab9c02f0618679b755594177e.jpg"));*/
//        // FaceResult result = detectLivingFace(netImageToBase64("https://agry.gsjtsz.com/upload/student/Face.jpg"));
//        // log.info(result.toString());
//        // result = faceContrast("",netImageToBase64("https://edu.gsjtsz.com/upload/article/20230713/1045780216530222.png"),"https://edu.gsjtsz.com/upload/article/20230713/1045780216530222.png","");
//        // log.info(result.toString());
//        //FaceResult result = faceContrast("",Base64Util.convertImageToBase64Str("F:\\test\\20230911131922.png"),"https://agry.gsjtsz.com/upload/student/620123199004183710.png","");
//        FaceResult result = faceContrast("",Base64Util.convertImageToBase64Str("F:\\test\\3a5fb968f18144c181d3cea09c73cc8c.jpg"),"https://agry.gsjtsz.com/upload/student/620123199004183710.png","");
//        log.info(result.toString());
//    }


    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.facebody20191230.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "facebody.cn-shanghai.aliyuncs.com";
        return new com.aliyun.facebody20191230.Client(config);
    }

    /**
     * 图片要求:
     * 请求格式：JPEG、JPG、PNG、BMP。
     * 图像大小：图像大小不超过3M。
     * 图像分辨率：图片大小要求5x5像素以上，人脸的尺寸建议大于64x64像素。
     * 截取imageB(摄像头抓拍到的人脸照片)、urlA(学生照片)
     */
    public static FaceResult faceContrast(String imageA, String imageB, String urlA, String urlB) {
        FaceResult faceResult = new FaceResult();
        try {
            com.aliyun.facebody20191230.Client client = createClient(alifaceapi_secretId, alifaceapi_secretKey);
            com.aliyun.facebody20191230.models.CompareFaceRequest compareFaceRequest = new CompareFaceRequest()
                    .setQualityScoreThreshold(Float.parseFloat(alifaceapi_qualityscorethreshold))
                    // .setImageDataA(netImageToBase64(urlA))   //网络图片转base64
                    .setImageDataA(convertImageToBase64Str(urlA.replace("https://agry.gsjtsz.com", "F:/test"))) //本地图片转base64
                    .setImageDataB(imageB.substring(imageB.indexOf(",") + 1));
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            com.aliyun.facebody20191230.models.CompareFaceResponse resp = client.compareFaceWithOptions(compareFaceRequest, runtime);
            com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(resp));
            if (resp.getStatusCode() == 200) {
                if (resp.getBody().getData().getConfidence() >= Float.parseFloat(alifaceapi_confidence)) {
                    faceResult.setCode(FaceResult.SUCCESS_CODE);
                    log.info("阿里云图片1:1比对成功：" + com.aliyun.teautil.Common.toJSONString(resp.getBody()));
                } else {
                    faceResult.setCode(FaceResult.FACE_ERROR);
                    log.info("阿里云图片1:1比对失败,返回阈值为：" + com.aliyun.teautil.Common.toJSONString(resp.getBody().getData().getConfidence()));
                }
            } else {
                faceResult.setCode(FaceResult.FACE_ERROR);
                log.info("阿里云图片1:1比对失败,失败原因为：" + com.aliyun.teautil.Common.toJSONString(resp.getBody().getData().getMessageTips()));
            }
        } catch (TeaException error) {
            // 如有需要，请打印 error
            log.info("阿里云TeaException：" + error.message);
            com.aliyun.teautil.Common.assertAsString(error.message);
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (Exception _error) {
            log.info("阿里云TeaException：" + _error.getMessage());
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            com.aliyun.teautil.Common.assertAsString(error.message);
            faceResult.setCode(FaceResult.FACE_ERROR);
        }
        return faceResult;
    }


    /**
     * DetectLivingFace API 人脸活体识别
     *
     * @param imageData 人脸活体识别
     */
    public static FaceResult detectLivingFace(String imageData) {
        FaceResult faceResult = new FaceResult();
        try {
            com.aliyun.facebody20191230.Client client = createClient(alifaceapi_secretId, alifaceapi_secretKey);
            com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks tasks0 = new com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks()
                    .setImageData(imageData.substring(imageData.indexOf(",") + 1));
            com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest advanceRequest = new com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest()
                    .setTasks(java.util.Arrays.asList(
                            tasks0
                    ));
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            DetectLivingFaceResponse resp = client.detectLivingFaceAdvance(advanceRequest, runtime);
            log.info("人脸活体识别：" + new Gson().toJson(resp));
            if (resp.getStatusCode() == 200) {
                String str = new Gson().toJson(resp.getBody()).toString();
                if (Func.isNotEmpty(str)) {
                    JSONObject jsonObject = JSONObject.parseObject(str);
                    jsonObject = JSONObject.parseObject((jsonObject.get("data").toString()));
                    //result_pass为-1时，人脸活体识别不通过pass
                    int result_pass = jsonObject.toString().indexOf("pass");
                    int result_block = jsonObject.toString().indexOf("block");
                    //判断是否通过活体
                    if (result_pass != -1 && result_block == -1) {
                        faceResult.setCode(FaceResult.SUCCESS_CODE);
                        log.info("人脸活体识别成功");
                    } else {
                        faceResult.setCode(FaceResult.FACE_ERROR);
                        log.info("人脸活体识别失败");
                    }
                }
            } else {
                faceResult.setCode(FaceResult.FACE_ERROR);
            }
        } catch (ServerException e) {
            log.info("人脸活体识别异常：" + e.getErrMsg());
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (ClientException e) {
            log.info("人脸活体识别异常ErrCode:" + e.getErrCode());
            log.info("人脸活体识别异常ErrMsg:" + e.getErrMsg());
            log.info("人脸活体识别异常RequestId:" + e.getRequestId());
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (Exception e) {
            log.info("人脸活体识别异常：" + e.getMessage());
        }
        return faceResult;
    }
}
