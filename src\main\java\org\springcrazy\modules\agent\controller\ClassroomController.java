package org.springcrazy.modules.agent.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springcrazy.core.mp.support.Condition;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.agent.entity.Classroom;
import org.springcrazy.modules.agent.service.IClassroomService;
import org.springcrazy.modules.agent.vo.ClassroomVO;
import org.springcrazy.modules.agent.wrapper.ClassroomWrapper;
import org.springframework.web.bind.annotation.*;
import org.springcrazy.core.boot.ctrl.CrazyController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 教室信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/agent/classroom")
@Api(value = "教室信息，记录代理商所拥有的教室信息", tags = "教室信息，记录代理商所拥有的教室信息")
public class ClassroomController extends CrazyController {
    private IClassroomService classroomService;

    /**
     * 分页 教室信息
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入Classroom")
    public R<IPage<ClassroomVO>> list(Classroom classroom, Query query) {
        //培训机构登录时只能查自己
        if (!SecureUtil.isAdministrator()){
            classroom.setAgentId(SecureUtil.getUserId());
        }
        String name = classroom.getName();
        String codeNum = classroom.getCodeNum();
        classroom.setCodeNum(null);
        classroom.setName(null);
        QueryWrapper<Classroom> queryWrapper =  Condition.getQueryWrapper(classroom);
        queryWrapper.lambda().like(Func.isNotBlank(name),Classroom::getName,name)
                .like(Func.isNotEmpty(codeNum),Classroom::getCodeNum,codeNum);
        queryWrapper.lambda().orderByDesc(Classroom::getCreateTime);
        IPage<Classroom> pages = classroomService.page(Condition.getPage(query), queryWrapper);
        return R.data(ClassroomWrapper.build().pageVO(pages));
    }


    /**
     * 查询单条
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查看详情", notes = "传入id")
    @GetMapping("/detail")
    public R<ClassroomVO> detail(Classroom classroom) {
        Classroom detail = classroomService.getOne(Condition.getQueryWrapper(classroom));
        return R.data(ClassroomWrapper.build().entityVO(detail));
    }


    /**
     * 新增或修改 教室信息
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入Classroom")
    public R submit(@Valid @RequestBody Classroom classroom) {
        return R.status(classroomService.saveOrUpdate(classroom));
    }

    /**
     * 删除 教室信息
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(classroomService.deleteLogic(Func.toIntList(ids)));
    }

    /**
     * 班级列表
     *
     * @param
     * @return
     */
    @GetMapping("/getClassroomList")
    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "获取班级列表", notes = "传入Classroom")
    public R<List<Classroom>> getClassroomList(Classroom classroom) {
        List<Classroom> list = classroomService.list(Condition.getQueryWrapper(classroom));
        return R.data(list);
    }
}

