<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.WebsiteNavigateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="websiteNavigateResultMap" type="org.springcrazy.modules.web.entity.WebsiteNavigate">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="url" property="url"/>
        <result column="newpage" property="newpage"/>
        <result column="type" property="type"/>
        <result column="image" property="image"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="selectWebsiteNavigatePage" resultMap="websiteNavigateResultMap">
        select * from web_website_navigate where is_deleted = 0
    </select>

</mapper>
