package org.springcrazy.modules.front.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springcrazy.modules.edu.entity.CourseStudyhistory;

@EqualsAndHashCode(callSuper = true)
@Data
public class VefVO extends CourseStudyhistory {

    // 人脸识别照片
    String imageBase;
    // 试卷id
    Integer examRecordId;
    // 摄像头驱动列表
    String deviceVideo;

    /**
     * 新增字段，小程序端解密相关数据
     */
    // 小程序code
    String wxCode;
    // 小程序加密sign
    String wxSign;
    // 小程序加密key的vision
    Integer wxVersion;

}
