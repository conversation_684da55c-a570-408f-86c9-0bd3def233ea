package org.springcrazy.modules.slry.vo.backend;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springcrazy.modules.edu.entity.TrxorderDetail;

@EqualsAndHashCode(callSuper = true)
@Data
public class StudentListVO extends TrxorderDetail {


    /**
     * 学员姓名
     */
    private String userName;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 应学课时数
     */
    private Integer studyHours;

    /**
     * 已学课时数
     */
    private Integer studyHour;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 参加考试次数
     */
    private Integer examCount;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 证书名称，审核窗口使用
     */
    private String zhengShuName;

    /**
     * 证书查看链接，审核窗口使用
     */
    private String zhengShuUrl;

    /**
     * 审核管理员姓名
     */
    private String examineUserName;

    /**
     * 清除管理员姓名
     */
    private String clearUserName;

    /**
     * 当前课程所有章节的课时数
     */
    private String classAllHours;

    /**
     * 观看课时数
     */
    private String classHours;

    /**
     * 需要观看课时数
     */
    private String needClassHours;

    /**
     * 人员类别
     */
    private String rylb;

}
