package org.springcrazy.modules.slry.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcrazy.modules.slry.entity.StudentMember;
import org.springcrazy.modules.system.entity.User;
import org.springcrazy.modules.system.excel.ExportUserExcel;
import org.springcrazy.modules.system.excel.StudentMemberExcel;

import java.util.List;
import java.util.Map;

public interface StudentMemberMapper extends BaseMapper<StudentMember> {
    Integer countByidCardNoAndNewzsbh(@Param("idCardNo") String ryid, @Param("newzsbh") String newzsbh);

    Map<String, Object> getZhengshuByZxtypeAndIdCardNoAndZsyxq(@Param("idCardNo") String idCardNo, @Param("zxtype") String zxtype, @Param("qytyshxydm") String qytyshxydm);

    Integer countOnlineByQYTYSHXYDM(@Param("qytyshxydm") String qytyshxydm);

    List<StudentMemberExcel> exportMember(@Param("ew") Wrapper<StudentMember> queryWrapper);
}