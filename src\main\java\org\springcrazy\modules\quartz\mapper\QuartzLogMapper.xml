<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.quartz.mapper.QuartzLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="quartzLogResultMap" type="org.springcrazy.modules.quartz.entity.QuartzLog">
        <id column="id" property="id"/>
        <result column="bean_name" property="beanName"/>
        <result column="create_time" property="createTime"/>
        <result column="cron_expression" property="cronExpression"/>
        <result column="exception_detail" property="exceptionDetail"/>
        <result column="is_success" property="isSuccess"/>
        <result column="job_name" property="jobName"/>
        <result column="method_name" property="methodName"/>
        <result column="params" property="params"/>
        <result column="time" property="time"/>
    </resultMap>


    <select id="selectQuartzLogPage" resultMap="quartzLogResultMap">
        select * from crazy_quartz_log where is_deleted = 0
    </select>

</mapper>
