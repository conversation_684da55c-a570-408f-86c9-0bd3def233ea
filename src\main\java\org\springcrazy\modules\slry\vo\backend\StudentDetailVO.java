package org.springcrazy.modules.slry.vo.backend;

import lombok.Data;
import org.springcrazy.modules.edu.entity.CourseStudyhistory;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.exam.entity.ExampaperRecord;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.user.entity.Student;

import java.util.List;

@Data
public class StudentDetailVO {
    /**
     * 学员信息
     */
    Student student;

    /**
     * 学员课程信息
     */
    TrxorderDetail trxorderDetail;

    /**
     * 证书信息
     */
    StudentZhengshu studentZhengshu;

    /**
     * 考试成绩
     */
    List<ExampaperRecord> exampaperRecords;

    /**
     * 学习记录
     */
    List<CourseStudyhistory> courseStudyhistories;

    /**
     * 学员地域信息
     */
    List<StudentAddressInfoVO> studentAddressInfos;

    /**
     * 课程所有的学习抓拍照片
     */
    List<StudentDetailPictureVO> detailPictures;

}
