package org.springcrazy.modules.slry.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.DateUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.edu.service.ICourseStudyhistoryService;
import org.springcrazy.modules.slry.entity.CompanyWhitelist;
import org.springcrazy.modules.slry.entity.StudentBlacklist;
import org.springcrazy.modules.slry.entity.StudentWhitelist;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.service.*;
import org.springcrazy.modules.slry.vo.backend.StudentDetailInfoVO;
import org.springcrazy.modules.slry.vo.backend.StudentDetailPictureVO;
import org.springcrazy.modules.slry.vo.backend.StudentDetailVO;
import org.springcrazy.modules.slry.vo.backend.StudentListVO;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 三类人员新增接口(后台管理相关)
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/slryBackend")
public class SlryBackendController {

    private SlryBackendService slryBackendService;
    private SlryService slryService;
    private ICourseStudyhistoryService courseStudyhistoryService;
    private StudentBlacklistService blacklistService;
    private StudentZhengshuService zhengshuService;
    private IStudentService studentService;
    private CompanyWhitelistService companyWhitelistService;
    private StudentWhitelistService studentWhitelistService;

    /**
     * 查询学员学习课程列表
     *
     * @param trxorderDetail 学员课程记录
     * @param currentPage    当前页
     * @param pageSize       每页条数
     * @return 学员学习课程列表
     */
    @GetMapping("/student/list")
    public R<IPage<StudentListVO>> list(TrxorderDetail trxorderDetail, @RequestParam Integer currentPage, @RequestParam Integer pageSize) {
        IPage<StudentListVO> studentListVOList = slryBackendService.getStudentList(trxorderDetail, currentPage, pageSize);
        return R.data(studentListVOList);
    }

    /**
     * 查询学员学习课程详情
     *
     * @param id 学员课程记录id
     * @return 学员学习课程详情
     */
    @GetMapping("/student/detail")
    public R<StudentDetailVO> detail(@RequestParam Integer id) {
        StudentDetailVO studentDetailVO = slryBackendService.getStudentDetail(id);
        return R.data(studentDetailVO);
    }

    /**
     * 查询学员学习课程详情图片
     *
     * @param historyId 学员课程记录id
     * @return 学员学习课程详情图片
     */
    @GetMapping("/student/detailPicture")
    public R<List<StudentDetailPictureVO>> detailPicture(@RequestParam Integer historyId) {
        return R.data(courseStudyhistoryService.getStudentDetailPicture(historyId));
    }

    /**
     * 更新学员课程状态
     *
     * @param trxorderDetail 学员课程记录
     * @param type           1：审核 2：清除课时 3:设置标红状态
     * @return 更新学员课程状态
     */
    @PostMapping("/student/update")
    public R<String> update(@RequestBody TrxorderDetail trxorderDetail, @RequestParam Integer type) {
        slryBackendService.updateStudent(trxorderDetail, type);
        return R.success("审核成功");
    }

    /**
     * 批量审核
     *
     * @param status
     * @param ids
     * @return
     */
    @PostMapping("/student/batchAudit/{status}")
    public R<String> batchAudit(@PathVariable String status, @RequestParam List<Long> ids) {
        slryBackendService.batchAudit(status, ids);
        return R.success("批量审核成功");
    }


    /**
     * 查询证书
     *
     * @param ryid 身份证号码
     * @return 证书
     */
    @GetMapping("/zhengshu/list")
    public R<IPage<StudentZhengshu>> getZhengshuInfo(@RequestParam String ryid, @RequestParam Integer current, @RequestParam Integer size) {
        IPage<StudentZhengshu> pageInfo = new Page<>(current, size);
        pageInfo.setRecords(slryService.getZhengShuInfo(ryid));
        return R.data(pageInfo);
    }

    /**
     * 学员学习详情
     *
     * @param userId
     * @return
     */
    @GetMapping("/student/detailInfo")
    public R<StudentDetailInfoVO> detailInfo(@RequestParam Integer userId) {
        StudentDetailInfoVO studentDetailInfoVO = slryBackendService.detailInfo(userId);
        return R.data(studentDetailInfoVO);
    }


    /**
     * 黑名单列表
     *
     * @param blacklist   黑名单
     * @param currentPage 当前页
     * @param pageSize    每页条数
     * @return
     */
    @GetMapping("/blackList/list")
    public R<IPage<StudentBlacklist>> blackListList(StudentBlacklist blacklist, @RequestParam Integer currentPage, @RequestParam Integer pageSize) {
        //分页构造器
        Page<StudentBlacklist> pages = new Page<>(currentPage, pageSize);
        //条件查询器
        LambdaQueryWrapper<StudentBlacklist> queryWrapper = new LambdaQueryWrapper<>();
        //添加条件
        queryWrapper.eq(StudentBlacklist::getType, blacklist.getType());
        queryWrapper.eq(StudentBlacklist::getType, blacklist.getType());
        queryWrapper.eq(blacklist.getStatus() != null, StudentBlacklist::getStatus, blacklist.getStatus());
        queryWrapper.like(StrUtil.isNotBlank(blacklist.getStudentIdCardNo()), StudentBlacklist::getStudentIdCardNo, blacklist.getStudentIdCardNo());
        queryWrapper.like(StrUtil.isNotBlank(blacklist.getStudentName()), StudentBlacklist::getStudentName, blacklist.getStudentName());
        queryWrapper.like(StrUtil.isNotBlank(blacklist.getCompanyName()), StudentBlacklist::getCompanyName, blacklist.getCompanyName());
        queryWrapper.orderByDesc(StudentBlacklist::getUpdateTime);
        //分页查询
        blacklistService.page(pages, queryWrapper);
        return R.data(pages);
    }

    /**
     * 黑名单详情
     *
     * @param id 黑名单id
     * @return 黑名单详情
     */
    @GetMapping("/blackList/detail")
    public R<StudentBlacklist> blackListDetail(@RequestParam Integer id) {
        StudentBlacklist studentBlacklist = blacklistService.getById(id);
        return R.data(studentBlacklist);
    }

    /**
     * 黑名单更新
     *
     * @param blacklist 黑名单
     * @return 更新结果
     */
    @PostMapping("/blackList/update")
    public R<String> blackListUpdate(@RequestBody StudentBlacklist blacklist) {
        Integer id = blacklist.getId();
        StudentBlacklist studentBlacklist = blacklistService.getById(id);
        // 企业
        if (blacklist.getType() == 2) {
            String companyName = studentBlacklist.getCompanyName();
            // 校验企业名称是否存在
            Integer i = zhengshuService.countByQymc(companyName);
            if (i <= 0) {
                return R.fail("不存在当前企业!");
            }
            List<String> ryid = zhengshuService.findRyidByQymc(companyName);
            studentService.update(new LambdaUpdateWrapper<Student>()
                    .in(Student::getIdCardNo, ryid)
                    .set(Student::getIsAvalible, blacklist.getStatus() == 1 ? 1 : 2)
                    .set(StrUtil.isNotBlank(blacklist.getMsg()), Student::getIsAvalibleMsg, blacklist.getMsg()));
        }
        // 学员
        if (blacklist.getType() == 1) {
            String studentIdCardNo = studentBlacklist.getStudentIdCardNo();
            // 校验身份证号码是否存在
            Integer i = zhengshuService.countByRyid(studentIdCardNo);
            if (i <= 0) {
                return R.fail("学员库中身份证号码不存在!");
            }
            studentService.update(new LambdaUpdateWrapper<Student>()
                    .eq(Student::getIdCardNo, studentIdCardNo)
                    .set(Student::getIsAvalible, blacklist.getStatus() == 1 ? 1 : 2)
                    .set(StrUtil.isNotBlank(blacklist.getMsg()), Student::getIsAvalibleMsg, blacklist.getMsg()));
        }
        blacklist.setUpdateTime(DateUtil.now());
        blacklistService.updateById(blacklist);
        return R.success("更新成功");
    }

    /**
     * 黑名单保存
     *
     * @param blacklist 黑名单
     * @return 保存结果
     */
    @PostMapping("/blackList/save")
    public R<String> blackListSave(@RequestBody StudentBlacklist blacklist) {
        // 企业
        if (blacklist.getType() == 2) {
            String companyName = blacklist.getCompanyName();
            // 校验企业名称是否存在
            Integer i1 = blacklistService.countByCompanyName(companyName);
            if (i1 > 0) {
                return R.fail("当前企业已存在黑名单!");
            }
            Integer i = zhengshuService.countByQymc(companyName);
            if (i <= 0) {
                return R.fail("不存在当前企业!");
            }
            // 冻结账号
            if (blacklist.getStatus() == 1) {
                List<String> ryid = zhengshuService.findRyidByQymc(companyName);
                studentService.update(new LambdaUpdateWrapper<Student>()
                        .in(Student::getIdCardNo, ryid)
                        .set(Student::getIsAvalible, 1)
                        .set(Student::getIsAvalibleMsg, Convert.toStr(blacklist.getMsg(), "")));
            }
        }
        // 学员
        if (blacklist.getType() == 1) {
            // 校验身份证号码是否存在
            String studentIdCardNo = blacklist.getStudentIdCardNo();
            Integer i1 = blacklistService.countByStudentIdCardNo(studentIdCardNo);
            if (i1 > 0) {
                return R.fail("当前学员已存在黑名单!");
            }
            Integer i = zhengshuService.countByRyid(studentIdCardNo);
            if (i <= 0) {
                return R.fail("学员库中身份证号码不存在!");
            }
            // 冻结账号
            if (blacklist.getStatus() == 1) {
                studentService.update(new LambdaUpdateWrapper<Student>()
                        .eq(Student::getIdCardNo, studentIdCardNo)
                        .set(Student::getIsAvalible, 1)
                        .set(Student::getIsAvalibleMsg, Convert.toStr(blacklist.getMsg(), "")));
            }
            StudentZhengshu zhengshu = zhengshuService.findByRyid(studentIdCardNo);
            blacklist.setStudentName(zhengshu.getRyxm());
        }
        blacklist.setCreateTime(DateUtil.now());
        blacklistService.save(blacklist);
        return R.success("保存成功");
    }

    @GetMapping("/student/syncPicture")
    public R<String> syncPicture(@RequestParam Integer userId, @RequestParam Integer syncPictureStatus) {
        Student student = studentService.getById(userId);
        if (Func.isEmpty(student)) {
            return R.fail("学员不存在");
        }
        if (Func.isEmpty(student.getProfilePhoto())) {
            return R.fail("学员头像不存在");
        }
//        if (student.getIsReal() == 2 || student.getIsReal() == 3) {
//            boolean isWhiteList = studentService.checkWhiteList(student);
//            if (isWhiteList) {
//                return R.fail("注册时通过人脸身份证比对实名(小程序注册)，无法同步照片");
//            }
//        }
        student.setSyncPictureStatus(syncPictureStatus);
        studentService.updateById(student);
        return R.success("同步成功");
    }

    @GetMapping("/companywhitelist/list")
    public R<Page> companywhitelist(CompanyWhitelist companyWhitelist, Query query) {
        Page<CompanyWhitelist> pageInfo = new Page<>(query.getCurrent(), query.getSize());
        LambdaQueryWrapper<CompanyWhitelist> queryWrapper = new LambdaQueryWrapper<>();
        //添加条件
        queryWrapper.like(StrUtil.isNotBlank(companyWhitelist.getCompanyName()), CompanyWhitelist::getCompanyName, companyWhitelist.getCompanyName());
        // 添加时间区间查询
        if (ArrayUtil.isNotEmpty(companyWhitelist.getTimeArr())) {
            queryWrapper.gt(CompanyWhitelist::getCreateTime, companyWhitelist.getTimeArr()[0]);
            queryWrapper.lt(CompanyWhitelist::getCreateTime, companyWhitelist.getTimeArr()[1]);
        }
        queryWrapper.orderByDesc(CompanyWhitelist::getCreateTime);
        //分页查询
        companyWhitelistService.page(pageInfo, queryWrapper);
        return R.data(pageInfo);
    }

    @PostMapping("/companywhitelist/submit")
    public R companywhitelistSubmit(@RequestBody CompanyWhitelist companyWhitelist) {
        String companyName = companyWhitelist.getCompanyName();
        // 校验企业名称是否存在
        int i1 = companyWhitelistService.count(new LambdaQueryWrapper<CompanyWhitelist>().eq(CompanyWhitelist::getCompanyName, companyName));
        if (Func.isEmpty(companyWhitelist.getId()) && i1 > 0) {
            return R.fail("当前企业已存在!");
        }
        Integer i = zhengshuService.countByQymc(companyName);
        if (i <= 0) {
            return R.fail("不存在当前企业!");
        }
        companyWhitelist.setCreateUser(SecureUtil.getUserId());
        return R.status(companyWhitelistService.saveOrUpdate(companyWhitelist));
    }

    @GetMapping("/companywhitelist/detail")
    public R<CompanyWhitelist> companywhitelistDetail(@RequestParam Integer id) {
        CompanyWhitelist detail = companyWhitelistService.getById(id);
        return R.data(detail);
    }

    @PostMapping("/companywhitelist/remove")
    public R companywhitelistRemove(@RequestParam String ids) {
        return R.status(companyWhitelistService.removeByIds(Func.toIntList(ids)));
    }


    @GetMapping("/studentwhitelist/list")
    public R<Page> studentwhitelist(StudentWhitelist studentWhitelist, Query query) {
        Page<StudentWhitelist> pageInfo = new Page<>(query.getCurrent(), query.getSize());
        LambdaQueryWrapper<StudentWhitelist> queryWrapper = new LambdaQueryWrapper<>();
        //添加条件
        queryWrapper.eq(StrUtil.isNotBlank(studentWhitelist.getIdCard()), StudentWhitelist::getIdCard, studentWhitelist.getIdCard());
        queryWrapper.like(StrUtil.isNotBlank(studentWhitelist.getMobile()), StudentWhitelist::getMobile, studentWhitelist.getMobile());
        queryWrapper.like(StrUtil.isNotBlank(studentWhitelist.getRealName()), StudentWhitelist::getRealName, studentWhitelist.getRealName());
        queryWrapper.like(StrUtil.isNotBlank(studentWhitelist.getCompanyName()), StudentWhitelist::getCompanyName, studentWhitelist.getCompanyName());
        // 添加时间区间查询
        if (ArrayUtil.isNotEmpty(studentWhitelist.getTimeArr())) {
            queryWrapper.gt(StudentWhitelist::getCreateTime, studentWhitelist.getTimeArr()[0]);
            queryWrapper.lt(StudentWhitelist::getCreateTime, studentWhitelist.getTimeArr()[1]);
        }
        queryWrapper.orderByDesc(StudentWhitelist::getCreateTime);
        //分页查询
        studentWhitelistService.page(pageInfo, queryWrapper);
        return R.data(pageInfo);
    }

    @PostMapping("/studentwhitelist/submit")
    public R studentwhitelistSubmit(@RequestBody StudentWhitelist studentWhitelist) {
        String idCard = studentWhitelist.getIdCard();
        // 校验身份证号码是否存在
        int i1 = studentWhitelistService.count(new LambdaQueryWrapper<StudentWhitelist>().eq(StudentWhitelist::getIdCard, idCard));
        if (Func.isEmpty(studentWhitelist.getId()) && i1 > 0) {
            return R.fail("当前学员已存在!");
        }
        StudentZhengshu zhengshu = zhengshuService.findByRyid(idCard);
        if (Func.isEmpty(zhengshu)) {
            return R.fail("不存在当前学员!");
        }
        Student student = studentService.getOne(new LambdaQueryWrapper<Student>().eq(Student::getIdCardNo, idCard).last("limit 1"));
        studentWhitelist.setRealName(zhengshu.getRyxm());
        studentWhitelist.setCreateUser(SecureUtil.getUserId());
        studentWhitelist.setMobile(student.getMobile());
        return R.status(studentWhitelistService.saveOrUpdate(studentWhitelist));
    }

    @GetMapping("/studentwhitelist/detail")
    public R<StudentWhitelist> studentwhitelistDetail(@RequestParam Integer id) {
        StudentWhitelist detail = studentWhitelistService.getById(id);
        return R.data(detail);
    }

    @PostMapping("/studentwhitelist/remove")
    public R studentwhitelistRemove(@RequestParam String ids) {
        return R.status(studentWhitelistService.removeByIds(Func.toIntList(ids)));
    }


    /**
     * 展示当天学习时人脸识别失败的用户
     *
     * @param faceErrorType 人脸识别失败类型 (studyFace:人脸对比失败 studyliving5:活体检测失败（超过五次） studyliving:活体检测失败)
     * @param date          日期
     * @return 人脸识别失败的用户
     */
    @GetMapping("/faceErrorImg/list")
    public R<List<String>> faceErrorImgList(@RequestParam Integer currentPage, @RequestParam Integer pageSize, @RequestParam String faceErrorType, @RequestParam String date) {
        List<String> faceErrorImgVOList = slryBackendService.faceErrorImgList(faceErrorType, date);
        return R.data(faceErrorImgVOList);
    }

    /**
     * 根据手机号码查询当天的图片
     *
     * @param mobile        手机号码
     * @param faceErrorType 人脸识别失败类型 (studyFace:人脸对比失败 studyliving5:活体检测失败（超过五次） studyliving:活体检测失败)
     * @param date          日期
     * @return 人脸识别失败的用户
     */
    @GetMapping("/faceErrorImg/detail")
    public R<List<String>> faceErrorImgDetail(@RequestParam String mobile, @RequestParam String faceErrorType, @RequestParam String date) {
        List<String> faceErrorImgVOList = slryBackendService.faceErrorImgDetail(mobile, faceErrorType, date);
        return R.data(faceErrorImgVOList);
    }

    /**
     * 异常ip监控
     */
    @GetMapping("/abnormalIp/list")
    public R<IPage<EduCourseHistoryDetailPicture>> abnormalIpList(EduCourseHistoryDetailPicture eduCourseHistoryDetailPicture, Query query) {
        IPage<EduCourseHistoryDetailPicture> studentListVOList = slryBackendService.getAbnormalIpList(eduCourseHistoryDetailPicture, query);
        return R.data(studentListVOList);
    }

//    public static void main(String[] args) {
//        /*
//          SET "school_mater_1_1_5_lqx12405_DetectLivingFace_***********" "\"Y\""
//          EXPIRE school_mater_1_1_5_lqx12405_DetectLivingFace_*********** 172800
//         */
//        String key = "school_mater_1_1_5_lqx{}_DetectLivingFace_{}";
//        String userMobile = "***********";
//        Integer userId = 11212;
//        String formatKey = StrUtil.format(key, userMobile, userId);
//        System.out.println("formatKey = " + formatKey);
//    }
}
