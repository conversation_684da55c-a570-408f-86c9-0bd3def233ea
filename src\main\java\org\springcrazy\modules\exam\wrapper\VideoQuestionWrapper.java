
package org.springcrazy.modules.exam.wrapper;

import org.springcrazy.core.mp.support.BaseEntityWrapper;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.modules.exam.entity.VideoQuestion;
import org.springcrazy.modules.exam.vo.VideoQuestionVO;

/**
 * 视频课程对应弹出题目包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
public class VideoQuestionWrapper extends BaseEntityWrapper<VideoQuestion, VideoQuestionVO>  {

    public static VideoQuestionWrapper build() {
        return new VideoQuestionWrapper();
    }

	@Override
	public VideoQuestionVO entityVO(VideoQuestion videoQuestion) {
		VideoQuestionVO videoQuestionVO = BeanUtil.copy(videoQuestion, VideoQuestionVO.class);

		return videoQuestionVO;
	}

}
