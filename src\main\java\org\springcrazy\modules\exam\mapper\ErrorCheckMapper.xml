<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.ErrorCheckMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="errorCheckResultMap" type="org.springcrazy.modules.exam.entity.ErrorCheck">
        <id column="id" property="id"/>
        <result column="paperid" property="paperid"/>
        <result column="questionid" property="questionid"/>
        <result column="content" property="content"/>
        <result column="addtime" property="addtime"/>
    </resultMap>


    <select id="selectErrorCheckPage" resultMap="errorCheckResultMap">
        select * from exam_error_check where is_deleted = 0
    </select>

</mapper>
