package org.springcrazy.modules.edu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;
import org.springcrazy.modules.edu.mapper.EduCourseHistoryDetailPictureMapper;
import org.springcrazy.modules.edu.service.EduCourseHistoryDetailPictureService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【edu_course_history_detail_picture】的数据库操作Service实现
* @createDate 2023-06-05 11:35:02
*/
@Service
public class EduCourseHistoryDetailPictureServiceImpl extends ServiceImpl<EduCourseHistoryDetailPictureMapper, EduCourseHistoryDetailPicture>
    implements EduCourseHistoryDetailPictureService {

    public IPage<EduCourseHistoryDetailPicture> getAbnormalIPVOs(IPage<EduCourseHistoryDetailPicture> page, QueryWrapper<EduCourseHistoryDetailPicture> wrapper) {
        return baseMapper.getAbnormalIPVOs(page, wrapper);
    }

}




