
package org.springcrazy.modules.agent.wrapper;

import org.springcrazy.core.mp.support.BaseEntityWrapper;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.modules.agent.entity.Classroom;
import org.springcrazy.modules.agent.vo.ClassroomVO;

/**
 * 教室信息包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
public class ClassroomWrapper extends BaseEntityWrapper<Classroom, ClassroomVO>  {

    public static ClassroomWrapper build() {
        return new ClassroomWrapper();
    }

	@Override
	public ClassroomVO entityVO(Classroom classroom) {
		ClassroomVO classroomVO = BeanUtil.copy(classroom, ClassroomVO.class);

		return classroomVO;
	}

}
