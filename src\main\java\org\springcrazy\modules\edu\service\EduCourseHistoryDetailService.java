package org.springcrazy.modules.edu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetail;

/**
* <AUTHOR>
* @description 针对表【edu_course_history_detail(课程播放记录详情(学习记录详情))】的数据库操作Service
* @createDate 2023-06-05 11:33:48
*/
public interface EduCourseHistoryDetailService extends IService<EduCourseHistoryDetail> {

    /**
     * 查询学习记录详情及照片信息
     * @param historyId 学习记录id
     * @return
     */
    R<?> getDetail(Integer historyId);
}
