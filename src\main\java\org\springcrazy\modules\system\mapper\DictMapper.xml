<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.system.mapper.DictMapper">

    <!-- 通用查询映射结果 -->
    <sql id="Base_Column_List">
        id,
        parent_id,
        code,
        dict_key,
        dict_value,
        sort,
        remark,
        is_deleted
    </sql>
    <resultMap id="dictResultMap" type="org.springcrazy.modules.system.entity.Dict">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="dict_key" property="dictKey"/>
        <result column="dict_value" property="dictValue"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springcrazy.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select
        id, parent_id, code, dict_key, dict_value, sort, remark, is_deleted
    </sql>

    <select id="selectDictPage" resultMap="dictResultMap">
        select * from crazy_dict where is_deleted = 0 order by sort DESC
    </select>

    <select id="getValue" resultType="java.lang.String">
        select
            dict_value
        from crazy_dict where code = #{param1} and dict_key = #{param2} and is_deleted = 0 limit 1
    </select>

    <!-- oracle 版本 -->
    <!--<select id="getValue" resultType="java.lang.String">
        select
            dict_value
        from crazy_dict where code = #{param1, jdbcType=VARCHAR} and dict_key = #{param2} rownum 1
    </select>-->

    <select id="getList" resultMap="dictResultMap">
        select code, dict_key, dict_value, sort, remark from crazy_dict where code = #{param1} and dict_key >= 0 and is_deleted = 0 order by sort DESC
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, dict_value as title, id as 'value', id as 'key' from crazy_dict where is_deleted = 0
    </select>

<!--auto generated by MybatisCodeHelper on 2023-07-19-->
    <select id="getDictValueByDictKeylikeAndCode" resultType="java.lang.String">
        select dict_value
        from crazy_dict
        where dict_key like concat('%',#{likeDictKey},'%') and code=#{code}
    </select>
</mapper>
