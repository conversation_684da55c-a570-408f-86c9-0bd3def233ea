
package org.springcrazy.modules.agent.wrapper;

import org.springcrazy.core.mp.support.BaseEntityWrapper;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.modules.agent.entity.AgentAttendance;
import org.springcrazy.modules.agent.vo.AgentAttendanceVO;

/**
 * 考勤机设备包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
public class AgentAttendanceWrapper extends BaseEntityWrapper<AgentAttendance, AgentAttendanceVO>  {

    public static AgentAttendanceWrapper build() {
        return new AgentAttendanceWrapper();
    }

	@Override
	public AgentAttendanceVO entityVO(AgentAttendance agentAttendance) {
		AgentAttendanceVO agentAttendanceVO = BeanUtil.copy(agentAttendance, AgentAttendanceVO.class);

		return agentAttendanceVO;
	}

}
