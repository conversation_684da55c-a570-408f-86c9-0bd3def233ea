package org.springcrazy.modules.user.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.aliyun.facebody20191230.models.CompareFaceRequest;
import com.aliyun.facebody20191230.models.DetectBodyCountResponse;
import com.aliyun.facebody20191230.models.DetectLivingFaceResponse;
import com.aliyun.tea.TeaException;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.exceptions.ServerException;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.exam.vo.FaceResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URL;

import static org.springcrazy.common.tool.Base64Util.convertImageToBase64Str;


/**
 * <AUTHOR>
 * <p>调用阿里云人脸识别接口</p>
 **/
@Component
@Slf4j
public class AliFaceRecognitionUtil {

    @Value("${alifaceapi.secretId}")
    private String alifaceapi_secretId;
    @Value("${alifaceapi.secretKey}")
    private String alifaceapi_secretKey;
    @Value("${alifaceapi.qualityscorethreshold}")
    private String alifaceapi_qualityscorethreshold;
    @Value("${alifaceapi.confidence}")
    private String alifaceapi_confidence;
    @Value("${crazy.prop.remote-path}")
    private String remotePath;
    @Value("${crazy.prop.upload-domain}")
    private String uploadDomain;

    /**
     * 使用AK&SK初始化账号Client
     *
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.facebody20191230.Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，您的 AccessKey ID
                .setAccessKeyId(accessKeyId)
                // 必填，您的 AccessKey Secret
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "facebody.cn-shanghai.aliyuncs.com";
        return new com.aliyun.facebody20191230.Client(config);
    }

    /**
     * 图片要求:
     * 请求格式：JPEG、JPG、PNG、BMP。
     * 图像大小：图像大小不超过3M。
     * 图像分辨率：图片大小要求5x5像素以上，人脸的尺寸建议大于64x64像素。
     * 截取imageB(摄像头抓拍到的人脸照片)、urlA(学生照片)
     */
    public FaceResult faceContrast(String imageA, String imageB, String urlA, String urlB) {
        FaceResult faceResult = new FaceResult();
        try {
            com.aliyun.facebody20191230.Client client = createClient(alifaceapi_secretId, alifaceapi_secretKey);
            com.aliyun.facebody20191230.models.CompareFaceRequest compareFaceRequest = new CompareFaceRequest()
                    .setQualityScoreThreshold(Float.parseFloat(alifaceapi_qualityscorethreshold))
                    // .setImageDataA(netImageToBase64(urlA))   //网络图片转base64
                    .setImageDataA(convertImageToBase64Str(urlA.replace(uploadDomain, remotePath))) //本地图片转base64
                    .setImageDataB(imageB.substring(imageB.indexOf(",") + 1));
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            com.aliyun.facebody20191230.models.CompareFaceResponse resp = client.compareFaceWithOptions(compareFaceRequest, runtime);
            // com.aliyun.teaconsole.Client.log(com.aliyun.teautil.Common.toJSONString(resp));
            if (resp.getStatusCode() == 200) {
                if (resp.getBody().getData().getConfidence() >= Float.parseFloat(alifaceapi_confidence)) {
                    faceResult.setCode(FaceResult.SUCCESS_CODE);
                    log.info("阿里云图片1:1比对成功：{}", com.aliyun.teautil.Common.toJSONString(resp.getBody()));
                } else {
                    faceResult.setCode(FaceResult.FACE_ERROR);
                    log.info("阿里云图片1:1比对失败,返回阈值为：{}", com.aliyun.teautil.Common.toJSONString(resp.getBody().getData().getConfidence()));
                }
            } else {
                faceResult.setCode(FaceResult.FACE_ERROR);
                log.info("阿里云图片1:1比对失败,失败原因为：{}", com.aliyun.teautil.Common.toJSONString(resp.getBody().getData().getMessageTips()));
            }
        } catch (TeaException error) {
            // 如有需要，请打印 error
            log.error("阿里云faceContrast_TeaException", error);
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 如有需要，请打印 error
            log.error("阿里云faceContrast_Exception", error);
            faceResult.setCode(FaceResult.FACE_ERROR);
        }
        return faceResult;
    }


    /**
     * DetectLivingFace API 人脸活体识别
     *
     * @param imageData 人脸活体识别
     */
    public FaceResult detectLivingFace(String imageData) {
        FaceResult faceResult = new FaceResult();
        try {
            com.aliyun.facebody20191230.Client client = createClient(alifaceapi_secretId, alifaceapi_secretKey);
            com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks tasks0 = new com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest.DetectLivingFaceAdvanceRequestTasks()
                    .setImageData(imageData.substring(imageData.indexOf(",") + 1));
            com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest advanceRequest = new com.aliyun.facebody20191230.models.DetectLivingFaceAdvanceRequest()
                    .setTasks(java.util.Arrays.asList(
                            tasks0
                    ));
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            DetectLivingFaceResponse resp = client.detectLivingFaceAdvance(advanceRequest, runtime);
            log.info("人脸活体识别阿里云返回报文:{}", new Gson().toJson(resp));
            if (resp.getStatusCode() == 200) {
                String str = new Gson().toJson(resp.getBody()).toString();
                if (Func.isNotEmpty(str)) {
                    //判断是否通过活体
                    String label = resp.getBody().getData().getElements().get(0).getResults().get(0).getLabel();
                    Float rate = resp.getBody().getData().getElements().get(0).getResults().get(0).getRate();
                    //https://help.aliyun.com/zh/viapi/developer-reference/api-j81gjl?spm=a2c4g.11186623.0.0.4be6ed58wrfSu6
                    if ("normal".equals(label) || ("liveness".equals(label) && NumberUtil.compare(rate, 50f) <= 0)) {
                        faceResult.setCode(FaceResult.SUCCESS_CODE);
                    } else {
                        faceResult.setCode(FaceResult.FACE_ERROR);
                        faceResult.setMsg(new Gson().toJson(resp));
                        log.info("人脸活体识别失败,返回报文:{}", new Gson().toJson(resp));
                    }
                }
            } else {
                faceResult.setCode(FaceResult.FACE_ERROR);
                log.info("人脸活体识别失败");
            }
        } catch (ServerException e) {
            log.info("ServerException人脸活体识别异常：{}", e.getErrMsg());
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (ClientException e) {
            log.info("ClientException人脸活体识别异常ErrCode:{}", e.getErrCode());
            log.info("ClientException人脸活体识别异常ErrMsg:{}", e.getErrMsg());
            log.info("ClientException人脸活体识别异常RequestId:{}", e.getRequestId());
            faceResult.setCode(FaceResult.FACE_ERROR);
        } catch (Exception e) {
            faceResult.setCode(FaceResult.FACE_ERROR);
            log.info("Exception人脸活体识别异常：{}", e.getMessage());
        }
        return faceResult;
    }


    /**
     * 人体计数
     *
     * @param imageUrl 图片地址
     * @return 人体的个数
     */
    public Integer detectBodyCount(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            com.aliyun.facebody20191230.Client client = createClient(alifaceapi_secretId, alifaceapi_secretKey);
            com.aliyun.facebody20191230.models.DetectBodyCountAdvanceRequest detectBodyCountAdvanceRequest = new com.aliyun.facebody20191230.models.DetectBodyCountAdvanceRequest()
                    .setImageURLObject(url.openConnection().getInputStream());
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            DetectBodyCountResponse detectBodyCountResponse = client.detectBodyCountAdvance(detectBodyCountAdvanceRequest, runtime);
            log.info("人体计数url:{},response:{}", imageUrl, new Gson().toJson(detectBodyCountResponse));
            if (detectBodyCountResponse.getStatusCode() == 200) {
                return detectBodyCountResponse.getBody().getData().getPersonNumber();
            }
        } catch (ServerException e) {
            log.info("人体计数ErrMsg:" + e.getErrMsg());
        } catch (ClientException e) {
            log.info("人体计数ErrCode:" + e.getErrCode());
            log.info("人体计数ErrMsg:" + e.getErrMsg());
            log.info("人体计数RequestId:" + e.getRequestId());
        } catch (Exception e) {
            log.info("人体计数Exception:" + e.getMessage());
        }
        return 0;
    }

}
