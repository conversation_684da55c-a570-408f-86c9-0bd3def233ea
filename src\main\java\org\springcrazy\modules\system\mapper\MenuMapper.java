
package org.springcrazy.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springcrazy.core.tool.node.TreeNode;
import org.springcrazy.modules.system.dto.MenuDTO;
import org.springcrazy.modules.system.entity.Menu;
import org.springcrazy.modules.system.vo.MenuVO;

import java.util.List;

/**
 * Mapper 接口

 */
public interface MenuMapper extends BaseMapper<Menu> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param menu
	 * @return
	 */
	List<MenuVO> selectMenuPage(IPage page, MenuVO menu);

	/**
	 * 树形结构
	 *
	 * @return
	 */
	List<TreeNode> tree();

	/**
	 * 授权树形结构
	 *
	 * @return
	 */
	List<MenuVO> grantTree();

	/**
	 * 授权树形结构
	 *
	 * @return
	 */
	List<MenuVO> grantTreeByRole(List<Integer> roleId);

	/**
	 * 所有菜单
	 *
	 * @return
	 */
	List<Menu> allMenu();

	/**
	 * 权限配置菜单
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> roleMenu(List<Integer> roleId);

	/**
	 * 菜单树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> routes(List<Integer> roleId);

	/**
	 * 按钮树形结构
	 *
	 * @param roleId
	 * @return
	 */
	List<Menu> buttons(List<Integer> roleId);

	/**
	 * 获取配置的角色权限
	 * @param roleIds
	 * @return
	 */
	List<MenuDTO> authRoutes(List<Integer> roleIds);
}
