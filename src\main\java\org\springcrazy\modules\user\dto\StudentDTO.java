
package org.springcrazy.modules.user.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springcrazy.modules.user.entity.Student;

/**
 * 学员表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StudentDTO extends Student {
	private static final long serialVersionUID = 1L;

	/**
	 * 企业名称
	 */
	private String qymc;

	/**
	 * 应学课时数
	 */
	private Integer studyHours;

	/**
	 * 已学课时数
	 */
	private Integer studyHour;

	/**
	 * 当前课程所有章节的课时数
	 */
	private String classAllHours;

	/**
	 * 观看课时数
	 */
	private String classHours;

	/**
	 * 需要观看课时数
	 */
	private String needClassHours;

	/**
	 * 观看课时数的百分率
	 */
	private String classHoursPercent;

	/**
	 * 每节课时的时长
	 */
	private String classHoursTime;

	/**
	 * 证书编号
	 */
	private String newzsbh;

	/**
	 * 发证日期
	 */
	private String fzrq;

	/**
	 * 证书有效期
	 */
	private String zsyxq;

	/**
	 * 人员类别
	 */
	private String rylb;


}
