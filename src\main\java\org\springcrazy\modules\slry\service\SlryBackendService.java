package org.springcrazy.modules.slry.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.log.exception.ServiceException;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.utils.DateUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.edu.entity.CourseStudyhistory;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.edu.service.*;
import org.springcrazy.modules.exam.entity.Exampaper;
import org.springcrazy.modules.exam.entity.ExampaperRecord;
import org.springcrazy.modules.exam.service.IExampaperRecordService;
import org.springcrazy.modules.exam.service.IExampaperService;
import org.springcrazy.modules.msg.entity.MsgReceive;
import org.springcrazy.modules.msg.service.IMsgReceiveService;
import org.springcrazy.modules.slry.entity.CompanyWhitelist;
import org.springcrazy.modules.slry.entity.StudentWhitelist;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.vo.backend.StudentDetailInfoVO;
import org.springcrazy.modules.slry.vo.backend.StudentDetailPictureVO;
import org.springcrazy.modules.slry.vo.backend.StudentDetailVO;
import org.springcrazy.modules.slry.vo.backend.StudentListVO;
import org.springcrazy.modules.system.entity.User;
import org.springcrazy.modules.system.service.IUserService;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlryBackendService {

    private final ITrxorderDetailService trxorderDetailService;
    private final StudentZhengshuService zhengshuService;
    private final ICourseStudyhistoryService courseStudyhistoryService;
    private final ICourseService courseService;
    private final ISubjectService subjectService;
    private final IStudentService studentService;
    private final IExampaperRecordService exampaperRecordService;
    private final IExampaperService exampaperService;
    private final IMsgReceiveService msgReceiveService;
    private final IUserService userService;
    private final CompanyWhitelistService companyWhitelistService;
    private final StudentWhitelistService studentWhitelistService;
    private final EduCourseHistoryDetailService eduCourseHistoryDetailService;
    private final EduCourseHistoryDetailPictureService eduCourseHistoryDetailPictureService;

    @Value("${face.picture-url}")
    private String facePictureUrl;

    @Value("${face.local-url}")
    private String localUrl;

    @Value("${slry.needWatchTime}")
    private int needWatchTime;// 需要观看的课时时长（秒）

    @Value("${slry.classHourTime}")
    private int classHourTime;// 每节课时的时长（分钟）

    public static final String EXAM_SUCCESS = "尊敬学员您好：您的【{}】学习记录和考试记录已审核通过，请联系企业负责人前往甘肃省住房和城乡建设厅政务服务申报平台中及时办理延期！";

    public static final String EXAMINE_FAIL = "您的【{}】课程已学完，系统检测您在学习过程中存在违规行为，根据《住建行业安全生产管理人员继续教育》要求：将对您提交的继续信息记录退回，请企业负责人与我公司联系，核验时间为周一至周五早上9:00点至下午5:00。 联系方式：18152067968";


    public IPage<StudentListVO> getStudentList(TrxorderDetail trxorderDetail, Integer currentPage, Integer pageSize) {
        IPage<StudentListVO> dishDtoPage = new Page<>(currentPage, pageSize);
        IPage<TrxorderDetail> pageInfo = new Page<>(currentPage, pageSize);
        List<StudentListVO> studentListVOList;
        // 查询学员课程记录
        QueryWrapper<TrxorderDetail> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<TrxorderDetail> lqw = queryWrapper.lambda();
        lqw.gt(TrxorderDetail::getAuthTime, DateUtil.now());
        lqw.eq(TrxorderDetail::getAuthStatus, TrxorderDetail.STATUS_SUCCESS);
        lqw.eq(Func.isNotEmpty(trxorderDetail.getExamineType()), TrxorderDetail::getExamineType, trxorderDetail.getExamineType());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getStudySuccess()), TrxorderDetail::getStudySuccess, trxorderDetail.getStudySuccess());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getExamSucces()), TrxorderDetail::getExamSucces, trxorderDetail.getExamSucces());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getClearType()), TrxorderDetail::getClearType, trxorderDetail.getClearType());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getClearedType()), TrxorderDetail::getClearedType, trxorderDetail.getClearedType());

        lqw.eq(Func.isNotEmpty(trxorderDetail.getIdCardNo()), TrxorderDetail::getIdCardNo, trxorderDetail.getIdCardNo());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getSex()), TrxorderDetail::getSex, trxorderDetail.getSex());
        lqw.eq(Func.isNotEmpty(trxorderDetail.getSubjectName()), TrxorderDetail::getSubjectName, trxorderDetail.getSubjectName());
        lqw.eq((!SecureUtil.isAdministrator() && !SecureUtil.isGansu()) && Func.isNotEmpty(trxorderDetail.getExamineType()) && trxorderDetail.getExamineType() == 1 && Func.isEmpty(trxorderDetail.getClearType()), TrxorderDetail::getExamineUserId, SecureUtil.getUserId());// 培训机构登录时只能查自己
//        lqw.eq(!SecureUtil.isAdministrator() && Func.isNotEmpty(trxorderDetail.getClearType()), TrxorderDetail::getClearUserId, SecureUtil.getUserId());// 培训机构登录时只能查自己
        lqw.like(Func.isNotEmpty(trxorderDetail.getUserName()), TrxorderDetail::getUserName, trxorderDetail.getUserName());
        lqw.like(Func.isNotEmpty(trxorderDetail.getCompanyName()), TrxorderDetail::getCompanyName, trxorderDetail.getCompanyName());

        queryWrapper.eq(Func.isNotEmpty(trxorderDetail.getRylb()), "b.rylb", trxorderDetail.getRylb());

        lqw.orderByAsc(Func.isNotEmpty(trxorderDetail.getExamineType()) && trxorderDetail.getExamineType() == 0, TrxorderDetail::getExamSuccessTime);// 待审核页面按照考试时间正序排列
        lqw.orderByDesc(Func.isNotEmpty(trxorderDetail.getExamineType()) && trxorderDetail.getExamineType() != 0, TrxorderDetail::getExamineTime);// 已审核或已拒绝页面按照审核时间倒序排列
        lqw.orderByDesc(Func.isNotEmpty(trxorderDetail.getClearType()) && trxorderDetail.getClearType() == 1, TrxorderDetail::getClearTime);// 被清空学员页面按照清空时间倒序排列
        // 添加时间区间查询 如果是审核页面则查询审核时间区间，如果是清空页面则查询清空时间区间
        if (ArrayUtil.isNotEmpty(trxorderDetail.getTimeArr())) {
            if (Func.isNotEmpty(trxorderDetail.getExamineType())) {
                lqw.gt(TrxorderDetail::getExamineTime, trxorderDetail.getTimeArr()[0]);
                lqw.lt(TrxorderDetail::getExamineTime, trxorderDetail.getTimeArr()[1]);
            }
            if (Func.isNotEmpty(trxorderDetail.getClearType()) && trxorderDetail.getClearType() == 1) {
                lqw.gt(TrxorderDetail::getClearTime, trxorderDetail.getTimeArr()[0]);
                lqw.lt(TrxorderDetail::getClearTime, trxorderDetail.getTimeArr()[1]);
            }
        }
        if (ArrayUtil.isNotEmpty(trxorderDetail.getTimeArrYxq())) {
            lqw.apply("str_to_date(zhengshu_yxq, '%Y年%m月%d日') BETWEEN {0} and {1}", trxorderDetail.getTimeArrYxq()[0], trxorderDetail.getTimeArrYxq()[1]);
        }
        // 根据操作人查询
        String userNameLike = trxorderDetail.getUserNameLike();
        if (Func.isNotEmpty(userNameLike)) {
            LambdaQueryWrapper<User> lqwUser = new LambdaQueryWrapper<>();
            lqwUser.like(User::getRealName, userNameLike);
            List<User> userList = userService.list(lqwUser);
            if (Func.isEmpty(userList)) {
                return dishDtoPage;
            }
            List<Integer> userIdList = userList.stream().map(User::getId).collect(Collectors.toList());
            if (Func.isNotEmpty(trxorderDetail.getExamineType())) {
                lqw.in(TrxorderDetail::getExamineUserId, userIdList);
            }
            if (Func.isNotEmpty(trxorderDetail.getClearType()) && trxorderDetail.getClearType() == 1) {
                lqw.in(TrxorderDetail::getClearUserId, userIdList);
            }
        }
        if (Func.isNotEmpty(trxorderDetail.getExamineType()) && trxorderDetail.getExamineType() == 0) {
            Set<Integer> whiteUseridList = new HashSet<>();
            List<CompanyWhitelist> companyWhitelists = companyWhitelistService.list(new LambdaQueryWrapper<CompanyWhitelist>().eq(CompanyWhitelist::getStatus, 1));
            List<StudentWhitelist> studentWhitelists = studentWhitelistService.list(new LambdaQueryWrapper<StudentWhitelist>().eq(StudentWhitelist::getStatus, 1));
            if (Func.isNotEmpty(companyWhitelists)) {
                /*for (CompanyWhitelist companyWhitelist : companyWhitelists) {
                    List<Student> students_company = studentService.list(new LambdaQueryWrapper<Student>().eq(Student::getCompanyName, companyWhitelist.getCompanyName()));
                    if (Func.isNotEmpty(students_company)) {
                        List<Integer> collect_company = students_company.stream().map(Student::getId).collect(Collectors.toList());
                        whiteUseridList.addAll(collect_company);
                    }
                }*/
                List<Student> students_company = studentService.list(new LambdaQueryWrapper<Student>().in(Student::getCompanyName, companyWhitelists.stream().map(CompanyWhitelist::getCompanyName).collect(Collectors.toList())));
                List<Integer> collect_company = students_company.stream().map(Student::getId).collect(Collectors.toList());
                whiteUseridList.addAll(collect_company);
            }
            if (Func.isNotEmpty(studentWhitelists)) {
                /*for (StudentWhitelist studentWhitelist : studentWhitelists) {
                    // 根据身份证号查询学员信息
                    List<Student> students_student = studentService.list(new LambdaQueryWrapper<Student>().eq(Student::getIdCardNo, studentWhitelist.getIdCard()));
                    if (Func.isNotEmpty(students_student)) {
                        List<Integer> collect_student = students_student.stream().map(Student::getId).collect(Collectors.toList());
                        whiteUseridList.addAll(collect_student);
                    }
                }*/
                List<Student> students_student = studentService.list(new LambdaQueryWrapper<Student>().in(Student::getIdCardNo, studentWhitelists.stream().map(StudentWhitelist::getIdCard).collect(Collectors.toList())));
                List<Integer> collect_student = students_student.stream().map(Student::getId).collect(Collectors.toList());
                whiteUseridList.addAll(collect_student);
            }
            if (SecureUtil.isAudit()) {
                if (Func.isNotEmpty(whiteUseridList)) {
                    lqw.in(TrxorderDetail::getUserId, whiteUseridList);
                } else {
                    return dishDtoPage;
                }
            } else if (!SecureUtil.isAdministrator()) {
                if (Func.isNotEmpty(whiteUseridList)) {
                    lqw.notIn(TrxorderDetail::getUserId, whiteUseridList);
                }
            }
        }

        Page<TrxorderDetail> page = trxorderDetailService.selectByAll(currentPage, pageSize, queryWrapper);
//        trxorderDetailService.page(pageInfo, lqw);
        List<TrxorderDetail> trxorderDetailList = page.getRecords();
        if (Func.isEmpty(trxorderDetailList)) {
            return dishDtoPage;
        }
        BeanUtil.copyProperties(page, dishDtoPage, "records");
        studentListVOList = trxorderDetailList.stream().map((item) -> {
            StudentListVO studentListVO = new StudentListVO();
            BeanUtil.copyProperties(item, studentListVO);
            Integer userId = item.getUserId();
            Integer courseId = item.getCourseId();
            Student student = studentService.getById(userId);// 学员信息
            // 查询证书信息
            StudentZhengshu zhengshu = zhengshuService.getOne(new LambdaQueryWrapper<StudentZhengshu>()
                    .eq(StudentZhengshu::getStatus, 1)
                    .eq(StudentZhengshu::getNewzsbh, item.getZhengshuCode())
            );
            // 查询课程学习记录
            Integer studyHours = courseStudyhistoryService.getstudykpoint(courseId);// 当前证书有效期内应学课时
            Integer studyHour = courseStudyhistoryService.getstudystudyisok(userId, courseId, 0, item.getId());// 当前证书有效期内已学课时

            // 学员只需在小程序中选择相应课程，累计学习时间达到24课时（每课时45分钟，总计1080分钟）即可参加继续教育在线考试。
            // 观看课时数
            int watchTime = courseStudyhistoryService.getstudystudyisokWatchTime(userId, courseId, 0);
            float classHour = (float) watchTime / 60 / classHourTime;
            DecimalFormat classHour_df = new DecimalFormat("0.00");//格式化小数
            classHour_df.setRoundingMode(RoundingMode.DOWN);
            String classHours = classHour_df.format(classHour);
            // 需要观看课时数
            float needClassHour = (float) needWatchTime / 60 / classHourTime;
            DecimalFormat needClassHour_df = new DecimalFormat("0");//格式化小数
            String needClassHours = needClassHour_df.format(needClassHour);
            // 当前课程所有章节的时长（秒）
            int classAllHours_int = courseStudyhistoryService.getClassAllHours(courseId);
            float classAllHours_df = (float) classAllHours_int / 60 / classHourTime;
            DecimalFormat classAllHours_df_df = new DecimalFormat("0");//格式化小数
            String classAllHours = classAllHours_df_df.format(classAllHours_df);

            // 查询考试次数
            Exampaper exampaper = exampaperService.getOne(new QueryWrapper<Exampaper>().lambda().eq(Exampaper::getCourseId, courseId));
            int examCount = exampaperRecordService.count(new LambdaQueryWrapper<ExampaperRecord>()
                    .eq(ExampaperRecord::getUserId, userId)
                    .eq(ExampaperRecord::getEpId, exampaper.getId())
                    .eq(ExampaperRecord::getTrxorderDetailId, item.getId())
            );
            // 获取管理员姓名
            if (Func.isNotEmpty(trxorderDetail.getExamineType()) && trxorderDetail.getExamineType() != 0) {
                User adminUser = userService.getById(item.getExamineUserId());
                if (Func.isNotEmpty(adminUser)) {
                    studentListVO.setExamineUserName(adminUser.getRealName());
                }
            }
            if (Func.isNotEmpty(trxorderDetail.getClearType())) {
                User adminUser = userService.getById(item.getClearUserId());
                if (Func.isNotEmpty(adminUser)) {
                    studentListVO.setClearUserName(adminUser.getRealName());
                }
            }
            studentListVO.setUserName(student.getRealName());
            studentListVO.setIdCardNo(student.getIdCardNo());
            studentListVO.setCompanyName(zhengshu.getQymc());
            studentListVO.setStudyHours(studyHours);
            studentListVO.setStudyHour(studyHour);
            studentListVO.setSubjectName(item.getSubjectName());
            studentListVO.setExamCount(examCount);
            studentListVO.setSex(student.getSex());
            studentListVO.setZhengShuName(zhengshu.getRylb());
            studentListVO.setZhengShuUrl(zhengshu.getZsxx());
            studentListVO.setClassAllHours(classAllHours);
            studentListVO.setClassHours(classHours);
            studentListVO.setNeedClassHours(needClassHours);
            return studentListVO;
        }).collect(Collectors.toList());
        dishDtoPage.setRecords(studentListVOList);
        return dishDtoPage;
    }

    public StudentDetailVO getStudentDetail(Integer id) {
        StudentDetailVO studentDetailVO = new StudentDetailVO();
        // 对应的课程信息
        TrxorderDetail trxorderDetail = trxorderDetailService.getById(id);
        Integer userId = trxorderDetail.getUserId();
        Integer courseId = trxorderDetail.getCourseId();
        String zhengshuCode = trxorderDetail.getZhengshuCode();
        // 获取管理员姓名
        if (trxorderDetail.getExamineType() != 0) {
            User adminUser = userService.getById(trxorderDetail.getExamineUserId());
            if (Func.isNotEmpty(adminUser)) {
                trxorderDetail.setExamineUserName(adminUser.getRealName());
            }
        }
        if (trxorderDetail.getClearType() == 1) {
            User adminUser = userService.getById(trxorderDetail.getClearUserId());
            if (Func.isNotEmpty(adminUser)) {
                trxorderDetail.setClearUserName(adminUser.getRealName());
            }
        }
        // 学员信息
        Student student = studentService.getById(userId);
        // 该课程对应的学员考试信息
        Exampaper exampaper = exampaperService.getOne(new QueryWrapper<Exampaper>().lambda().eq(Exampaper::getCourseId, courseId));
        List<ExampaperRecord> exampaperRecords = exampaperRecordService.list(new LambdaQueryWrapper<ExampaperRecord>()
                .eq(ExampaperRecord::getEpId, exampaper.getId())
                .eq(ExampaperRecord::getUserId, userId)
                .eq(ExampaperRecord::getTrxorderDetailId, id)
        );
        // 给考试信息添加考试开始时间
        exampaperRecords.forEach(exampaperRecord -> {
            // 考试结束时间
            LocalDateTime addTime = exampaperRecord.getAddTime();
            // 考试时长单位是秒
            Integer testTime = exampaperRecord.getTestTime();
            // 考试开始时间 = 考试结束时间 - 考试时长
            LocalDateTime startTime = addTime.minusSeconds(testTime);
            exampaperRecord.setStartTime(startTime);
        });
        // 该课程对应的学员证书信息
        StudentZhengshu studentZhengshu = zhengshuService.getOne(new LambdaQueryWrapper<StudentZhengshu>()
                .eq(StudentZhengshu::getNewzsbh, zhengshuCode)
                .eq(StudentZhengshu::getStatus, 1)
        );
        studentZhengshu.setSubjectName(trxorderDetail.getSubjectName());
        // 该课程对应学员的学习记录
        List<CourseStudyhistory> courseStudyhistories = courseStudyhistoryService.getCourseList(courseId);
        for (CourseStudyhistory history : courseStudyhistories) {
            history.setId(-1);
            CourseStudyhistory studyHistory = courseStudyhistoryService.getStudyHistory(userId, history.getKpointId(), 0, 0, id);
            if (history.getParentId() != 0) {
                if (studyHistory != null) {
                    history.setId(studyHistory.getId());
                    /*累计学习时长*/
                    if (studyHistory.getWatchTime() > 0) {
                        history.setWatchStingTime(courseStudyhistoryService.gitTime(studyHistory.getWatchTime()));
                    } else {
                        history.setWatchStingTime("暂未学习时长");
                    }
                    /*学习进度*/
                    history.setStudyLearning(studyHistory.getStudyLearning());
                    /*是否完成*/
                    history.setComplete(studyHistory.getComplete());
                    /*最后学习时长*/
                    history.setUpdateTime(studyHistory.getUpdateTime());
                    history.setRemark(studyHistory.getRemark());
                } else {
                    /*累计学习时长*/
                    history.setWatchStingTime("暂未学习时长");
                    /*学习进度*/
                    history.setStudyLearning("暂未学习进度");
                    /*是否完成*/
                    history.setComplete("1");
                }
            }
        }
        studentDetailVO.setStudent(student);
        studentDetailVO.setTrxorderDetail(trxorderDetail);
        studentDetailVO.setExampaperRecords(exampaperRecords);
        studentDetailVO.setStudentZhengshu(studentZhengshu);
        studentDetailVO.setCourseStudyhistories(courseStudyhistories);
        studentDetailVO.setStudentAddressInfos(courseStudyhistoryService.getStudentAddressInfos(userId));
        studentDetailVO.setDetailPictures(courseStudyhistoryService.getStudentDetailPictureAll(id));
        return studentDetailVO;
    }

    public void updateStudent(TrxorderDetail trxorderDetail, Integer type) {
        log.info("updateStudent:{},type:{}", trxorderDetail, type);
        Integer id = trxorderDetail.getId();
        Integer userId = SecureUtil.getUserId();
        TrxorderDetail detail = new TrxorderDetail();
        if (id == null) {
            throw new ServiceException("参数错误");
        }
        TrxorderDetail trxorderDetail1 = trxorderDetailService.getById(id);
        if (type == 1 || type == 2) {
            if (Func.isNotEmpty(trxorderDetail1.getExamineType()) && trxorderDetail1.getExamineType() == 2) {
                if (!SecureUtil.isAdministrator() && !Objects.equals(SecureUtil.getUserId(), trxorderDetail1.getExamineUserId())) {
                    throw new ServiceException("您没有权限操作");
                }
            }
        }
        // 1：审核 2：清除课时  3:设置标红状态
        if (type == 1) {
            detail.setExamineType(trxorderDetail.getExamineType());
            detail.setExamineMsg(trxorderDetail.getExamineType() != 1 ? trxorderDetail.getExamineMsg() : "审核通过");
            detail.setExamineUserId(userId);
            detail.setExamineTime(DateUtil.now());
            MsgReceive msgReceive = new MsgReceive();
            msgReceive.setCreateTime(new Date());
            msgReceive.setCusId(1);
            msgReceive.setReceivingCusid(trxorderDetail1.getUserId());
            msgReceive.setContent(trxorderDetail.getExamineType() == 2 ? trxorderDetail.getExamineMsg() : StrUtil.format(EXAM_SUCCESS, trxorderDetail1.getSubjectName()));
            msgReceive.setType("1");
            msgReceive.setStatus("1");
            msgReceiveService.save(msgReceive);

            if (trxorderDetail.getExamineType() == 1 && StrUtil.isNotBlank(trxorderDetail1.getIdCardNo())) {
                // 审核通过删除企业员工表的数据
                LambdaUpdateWrapper<StudentWhitelist> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(StudentWhitelist::getIdCard, trxorderDetail1.getIdCardNo());
                updateWrapper.set(StudentWhitelist::getIsDeleted, 1);
                updateWrapper.set(StudentWhitelist::getRemark, "审核通过,删除企业员工表数据");
                studentWhitelistService.update(updateWrapper);
            }
        } else if (type == 2) {
            detail.setClearType(1);
            detail.setClearedType(1);
            detail.setClearUserId(userId);
            detail.setClearTime(DateUtil.now());
            // 新增一条新的学员课程记录
            Integer courseId = trxorderDetail1.getCourseId();
            String zhengshuCode = trxorderDetail1.getZhengshuCode();
            StudentZhengshu zhengshu = zhengshuService.getOne(new LambdaQueryWrapper<StudentZhengshu>()
                    .eq(StudentZhengshu::getStatus, 1)
                    .eq(StudentZhengshu::getNewzsbh, zhengshuCode)
            );
            courseService.giveCourse(Convert.toStr(trxorderDetail1.getUserId()), Convert.toStr(courseId), zhengshu);
            MsgReceive msgReceive = new MsgReceive();
            msgReceive.setCreateTime(new Date());
            msgReceive.setCusId(1);
            msgReceive.setReceivingCusid(trxorderDetail1.getUserId());
            msgReceive.setContent(trxorderDetail1.getExamineMsg());
            msgReceive.setType("1");
            msgReceive.setStatus("1");
            msgReceiveService.save(msgReceive);
        } else if (type == 3) {
            detail.setMarkType(trxorderDetail.getMarkType());
        } else {
            throw new ServiceException("参数错误");
        }
        detail.setId(id);
        detail.setLastUpdateTime(DateUtil.now());
        trxorderDetailService.updateById(detail);
    }

    public void batchAudit(String status, List<Long> ids) {
        List<TrxorderDetail> trxorderDetails = trxorderDetailService.listByIds(ids);
        for (TrxorderDetail trxorderDetail : trxorderDetails) {
            LambdaUpdateWrapper<TrxorderDetail> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TrxorderDetail::getId, trxorderDetail.getId());
            updateWrapper.set(TrxorderDetail::getExamineType, status);
            updateWrapper.set(TrxorderDetail::getExamineMsg, "批量审核");
            updateWrapper.set(TrxorderDetail::getExamineUserId, SecureUtil.getUserId());
            updateWrapper.set(TrxorderDetail::getExamineTime, DateUtil.now());
            updateWrapper.set(TrxorderDetail::getLastUpdateTime, DateUtil.now());
            trxorderDetailService.update(updateWrapper);

            if (StrUtil.equals(status, "1") && StrUtil.isNotBlank(trxorderDetail.getIdCardNo())) {
                // 审核通过删除企业员工表的数据
                LambdaUpdateWrapper<StudentWhitelist> whitelistUpdateWrapper = new LambdaUpdateWrapper<>();
                whitelistUpdateWrapper.eq(StudentWhitelist::getIdCard, trxorderDetail.getIdCardNo());
                whitelistUpdateWrapper.set(StudentWhitelist::getIsDeleted, 1);
                whitelistUpdateWrapper.set(StudentWhitelist::getRemark, "批量审核通过,删除企业员工表数据");
                studentWhitelistService.update(whitelistUpdateWrapper);
            }

            MsgReceive msgReceive = new MsgReceive();
            msgReceive.setCreateTime(new Date());
            msgReceive.setCusId(1);
            msgReceive.setReceivingCusid(trxorderDetail.getUserId());
            msgReceive.setContent(StrUtil.equals(status, "2") ? StrUtil.format(EXAMINE_FAIL, trxorderDetail.getSubjectName()) : StrUtil.format(EXAM_SUCCESS, trxorderDetail.getSubjectName()));
            msgReceive.setType("1");
            msgReceive.setStatus("1");
            msgReceiveService.save(msgReceive);
        }
    }

    public StudentDetailInfoVO detailInfo(Integer userId) {
        StudentDetailInfoVO studentDetailInfoVO = new StudentDetailInfoVO();
        // 学员信息
        Student student = studentService.getById(userId);
        // 根据userId查询学员课程记录
        List<TrxorderDetail> trxorderDetailList = trxorderDetailService.list(new LambdaQueryWrapper<TrxorderDetail>()
                .eq(TrxorderDetail::getUserId, userId)
                .eq(TrxorderDetail::getClearType, 0));
        if (Func.isEmpty(trxorderDetailList)) {
            throw new ServiceException("该学员没有学习课程");
        }
        List<TrxorderDetail> trxorderDetails = new ArrayList<>();
        List<StudentDetailPictureVO> detailPictures = new ArrayList<>();
        for (TrxorderDetail trxorderDetail : trxorderDetailList) {
            Integer courseId = trxorderDetail.getCourseId();
            List<CourseStudyhistory> courseStudyhistories = courseStudyhistoryService.getCourseList(courseId);
            for (CourseStudyhistory history : courseStudyhistories) {
                history.setId(-1);
                CourseStudyhistory studyHistory = courseStudyhistoryService.getStudyHistory(userId, history.getKpointId(), 0, 0);
                if (history.getParentId() != 0) {
                    if (studyHistory != null) {
                        history.setId(studyHistory.getId());
                        /*累计学习时长*/
                        if (studyHistory.getWatchTime() > 0) {
                            history.setWatchStingTime(courseStudyhistoryService.gitTime(studyHistory.getWatchTime()));
                        } else {
                            history.setWatchStingTime("暂未学习时长");
                        }
                        /*学习进度*/
                        history.setStudyLearning(studyHistory.getStudyLearning());
                        /*是否完成*/
                        history.setComplete(studyHistory.getComplete());
                        /*最后学习时长*/
                        history.setUpdateTime(studyHistory.getUpdateTime());
                        history.setRemark(studyHistory.getRemark());
                    } else {
                        /*累计学习时长*/
                        history.setWatchStingTime("暂未学习时长");
                        /*学习进度*/
                        history.setStudyLearning("暂未学习进度");
                        /*是否完成*/
                        history.setComplete("1");
                    }
                }
                history.setSubjectName(trxorderDetail.getSubjectName());
            }
            trxorderDetail.setCourseStudyhistories(courseStudyhistories);
            trxorderDetails.add(trxorderDetail);

            List<StudentDetailPictureVO> studentDetailPictureAll = courseStudyhistoryService.getStudentDetailPictureAll(trxorderDetail.getId());
            detailPictures.addAll(studentDetailPictureAll);
        }
        studentDetailInfoVO.setTrxorderDetails(trxorderDetails);
        studentDetailInfoVO.setStudent(student);
        studentDetailInfoVO.setStudentAddressInfos(courseStudyhistoryService.getStudentAddressInfos(userId));
        studentDetailInfoVO.setDetailPictures(detailPictures);
        return studentDetailInfoVO;
    }

    public List<String> faceErrorImgList(String faceErrorType, String date) {
        String path = localUrl + "/errorImg/" + date + "/" + faceErrorType + "/";
        // 获取路径下的所有目录名称
        File file = new File(path);
        File[] files = file.listFiles();
        List<String> imgList = new ArrayList<>();
        if (ArrayUtil.isNotEmpty(files)) {
            for (File user : files) {
                imgList.add(FileUtil.getName(user));
            }
        }
        return imgList;
    }

    public List<String> faceErrorImgDetail(String mobile, String faceErrorType, String date) {
        String path = localUrl + "/errorImg/" + date + "/" + faceErrorType + "/" + mobile + "/";
        // 获取路径下的所有用户文件夹
        List<String> imgNames = FileUtil.listFileNames(path);
        List<String> imgList = new ArrayList<>();
        for (String imgName : imgNames) {
            imgList.add(facePictureUrl + "errorImg/" + date + "/" + faceErrorType + "/" + mobile + "/" + imgName);
        }
        return imgList;
    }

    public IPage<EduCourseHistoryDetailPicture> getAbnormalIpList(EduCourseHistoryDetailPicture eduCourseHistoryDetailPicture, Query query) {
        Page<EduCourseHistoryDetailPicture> page = new Page<>(query.getCurrent(), query.getSize());
        // 注意：一定要手动关闭 SQL 优化，不然查询总数的时候只会查询主表
//        page.setOptimizeCountSql(false);
        String[] times = eduCourseHistoryDetailPicture.getTimes();
        QueryWrapper<EduCourseHistoryDetailPicture> wrapper = new QueryWrapper<>();
        if (ArrayUtil.isNotEmpty(times)) {
            wrapper.gt("main.end_time", times[0]);
            wrapper.lt("main.end_time", times[1]);
        }
        return eduCourseHistoryDetailPictureService.getAbnormalIPVOs(page, wrapper);
    }

//    public Page<AbnormalIPVO> getAbnormalIpList_bak(AbnormalIPVO abnormalIPVO, Query query) {
//        Page<AbnormalIPVO> page = new Page<>(query.getCurrent(), query.getSize());
//        // 获取时间区间
//        String[] times = abnormalIPVO.getTimes();
//        // 根据时间区间查询学习记录详情
//        List<EduCourseHistoryDetail> eduCourseHistoryDetails = new ArrayList<>();
//        if (ArrayUtil.isEmpty(times)) {
//            eduCourseHistoryDetails = eduCourseHistoryDetailService.list();
//        } else {
//            eduCourseHistoryDetails = eduCourseHistoryDetailService.list(
//                    new LambdaQueryWrapper<EduCourseHistoryDetail>()
//                            .ge(EduCourseHistoryDetail::getEndTime, times[0])
//                            .le(EduCourseHistoryDetail::getEndTime, times[1])
//            );
//        }
//        // 根据查询到的学习记录详情查询子表，子表存储了学习记录的抓拍图片信息和Ip信息
//        List<EduCourseHistoryDetailPicture> eduCourseHistoryDetailPictures = eduCourseHistoryDetailPictureService.list(
//                new LambdaQueryWrapper<EduCourseHistoryDetailPicture>()
//                        .in(EduCourseHistoryDetailPicture::getHistoryDetailId, eduCourseHistoryDetails.stream().map(EduCourseHistoryDetail::getId).collect(Collectors.toList()))
//        );
//        // 根据查询到的学习记录详情查询学员学习记录
//        List<CourseStudyhistory> courseStudyhistories = courseStudyhistoryService.list(
//                new LambdaQueryWrapper<CourseStudyhistory>()
//                        .in(CourseStudyhistory::getId, eduCourseHistoryDetails.stream().map(EduCourseHistoryDetail::getStudyhistoryId).collect(Collectors.toList()))
//        );
//        // 根据学员学习记录查询学员订单详情，包含了学员的所属公司
//        List<TrxorderDetail> trxorderDetails = trxorderDetailService.list(
//                new LambdaQueryWrapper<TrxorderDetail>()
//                        .in(TrxorderDetail::getId, courseStudyhistories.stream().map(CourseStudyhistory::getTrxorderDetailId).collect(Collectors.toList()))
//        );
//        // 根据学习记录详情查询子表的数据对ip地址分组
//        Map<String, List<EduCourseHistoryDetailPicture>> collect = eduCourseHistoryDetailPictures.stream().collect(Collectors.groupingBy(EduCourseHistoryDetailPicture::getIp));
//        // 检查每个IP是否被不同的公司使用，trxorderDetails中包含了对于学习记录的公司信息
//        List<AbnormalIPVO> abnormalIPVOS = new ArrayList<>();
//        for (Map.Entry<String, List<EduCourseHistoryDetailPicture>> entry : collect.entrySet()) {
//            String ip = entry.getKey();
//            List<EduCourseHistoryDetailPicture> value = entry.getValue();
//            Set<String> companySet = new HashSet<>();
//            for (EduCourseHistoryDetailPicture eduCourseHistoryDetailPicture : value) {
//                Integer historyDetailId = eduCourseHistoryDetailPicture.getHistoryDetailId();
//                EduCourseHistoryDetail eduCourseHistoryDetail = eduCourseHistoryDetails.stream().filter(item -> item.getId().equals(historyDetailId)).findFirst().orElse(null);
//                if (eduCourseHistoryDetail != null) {
//                    Integer studyhistoryId = eduCourseHistoryDetail.getStudyhistoryId();
//                    CourseStudyhistory courseStudyhistory = courseStudyhistories.stream().filter(item -> item.getId().equals(studyhistoryId)).findFirst().orElse(null);
//                    if (courseStudyhistory != null) {
//                        Integer trxorderDetailId = courseStudyhistory.getTrxorderDetailId();
//                        TrxorderDetail trxorderDetail = trxorderDetails.stream().filter(item -> item.getId().equals(trxorderDetailId)).findFirst().orElse(null);
//                        if (trxorderDetail != null) {
//                            companySet.add(trxorderDetail.getCompanyName());
//                        }
//                    }
//                }
//            }
//            if (companySet.size() > 1) {
//                AbnormalIPVO abnormalIPVO1 = new AbnormalIPVO();
//                abnormalIPVO1.setIp(ip);
//                abnormalIPVO1.setCount(value.size());
//                abnormalIPVO1.setTimes(times);
//                abnormalIPVOS.add(abnormalIPVO1);
//            }
//        }
//        page.setRecords(abnormalIPVOS);
//        page.setTotal(abnormalIPVOS.size());
//        return page;
//    }
}
