
package org.springcrazy.modules.agent.vo;

import org.springcrazy.modules.agent.entity.AgentMonitor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 监控设备视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AgentMonitorVO对象", description = "监控设备")
public class AgentMonitorVO extends AgentMonitor {
	private static final long serialVersionUID = 1L;

}
