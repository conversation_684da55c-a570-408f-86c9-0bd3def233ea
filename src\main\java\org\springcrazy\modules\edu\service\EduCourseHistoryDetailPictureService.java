package org.springcrazy.modules.edu.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;

/**
* <AUTHOR>
* @description 针对表【edu_course_history_detail_picture】的数据库操作Service
* @createDate 2023-06-05 11:35:02
*/
public interface EduCourseHistoryDetailPictureService extends IService<EduCourseHistoryDetailPicture> {

    IPage<EduCourseHistoryDetailPicture> getAbnormalIPVOs(IPage<EduCourseHistoryDetailPicture> page, QueryWrapper<EduCourseHistoryDetailPicture> wrapper);

}
