package org.springcrazy.modules.slry.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "edu_student_blacklist")
public class StudentBlacklist {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1-学员黑名单 2-企业黑名单
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 学员姓名
     */
    @TableField(value = "student_name")
    private String studentName;

    /**
     * 身份证号码
     */
    @TableField(value = "student_id_card_no")
    private String studentIdCardNo;

    /**
     * 企业名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 黑名单原因
     */
    @TableField(value = "msg")
    private String msg;

    /**
     * 0: 禁用 1: 启用
     */
    @TableField(value = "`status`")
    private Integer status;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "create_user")
    private String createUser;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "update_user")
    private String updateUser;
}