
package org.springcrazy.modules.system.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * StudentMemberExcel

 */
@Data
@ColumnWidth(25)
@HeadRowHeight(20)
@ContentRowHeight(18)
public class StudentMemberExcel implements Serializable {
	private static final long serialVersionUID = 1L;

	@ColumnWidth(20)
	@ExcelProperty("姓名（必填）")
	private String realName;

	@ColumnWidth(15)
	@ExcelProperty("身份证号（必填）")
	private String idCardNo;

	@ColumnWidth(20)
	@ExcelProperty("证件类别（必填）")
	private String zxtype;

	@ColumnWidth(20)
	@ExcelProperty("手机号码（必填）")
	private String mobile;

	@ColumnWidth(25)
	@ExcelProperty("注册密码")
	private String password;

	@ColumnWidth(25)
	@ExcelProperty("视频/照片")
	private String mediaType;

	@ColumnWidth(20)
	@ExcelProperty("证书有效期")
	private String zsyxq;

	@ColumnWidth(20)
	@ExcelProperty("企业名称")
	private String qymc;

	@ColumnWidth(35)
	@ExcelProperty("企业统一社会信用代码（必填）")
	private String qytyshxydm;

	@ColumnWidth(35)
	@ExcelProperty("（是/否）线上支付（必填）")
	private String paymentType;

	@ColumnWidth(35)
	@ExcelProperty("代理商名称")
	private String agentName;

	@ColumnWidth(35)
	@ExcelProperty("创建时间")
	private String createTime;
}
