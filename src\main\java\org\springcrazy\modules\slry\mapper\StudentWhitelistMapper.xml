<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.slry.mapper.StudentWhitelistMapper">
  <resultMap id="BaseResultMap" type="org.springcrazy.modules.slry.entity.StudentWhitelist">
    <!--@mbg.generated-->
    <!--@Table edu_student_whitelist-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="INTEGER" property="createUser" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, real_name, mobile, company_name, `status`, remark, create_time, create_user, 
    is_deleted
  </sql>
</mapper>