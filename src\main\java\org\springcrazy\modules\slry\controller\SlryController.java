package org.springcrazy.modules.slry.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.common.tool.OcrDataUtil;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.api.ResultCode;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.core.tool.utils.RedisUtil;
import org.springcrazy.modules.edu.entity.*;
import org.springcrazy.modules.edu.service.*;
import org.springcrazy.modules.exam.entity.Exampaper;
import org.springcrazy.modules.exam.entity.VideoQuestion;
import org.springcrazy.modules.exam.service.IExampaperService;
import org.springcrazy.modules.exam.service.IVideoQuestionService;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.service.SlryService;
import org.springcrazy.modules.slry.service.StudentZhengshuService;
import org.springcrazy.modules.slry.vo.ExVO;
import org.springcrazy.modules.slry.vo.HeartbeatVO;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springcrazy.modules.user.service.impl.AliFaceRecognitionUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 三类人员新增接口
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/slry")
public class SlryController {

    private final IStudentService studentService;
    private final ICourseService courseService;
    private final IExampaperService exampaperService;
    private final ICourseStudyhistoryService courseStudyhistoryService;
    private final ITrxorderDetailService trxorderDetailService;
    private final IVideoQuestionService videoQuestionService;
    private final SlryService slryService;
    private final StudentZhengshuService zhengshuService;
    private final AliFaceRecognitionUtil aliFaceRecognitionUtil;
    private final RedisUtil redisUtil;
    private final ICourseKpointService courseKpointService;
    private final EduCourseHistoryDetailService eduCourseHistoryDetailService;

    @Value("${spring.profiles.active}")
    private String active;
    @Value("${slry.needWatchTime}")
    private int needWatchTime;// 需要观看的课时时长（秒）
    @Value("${slry.classHourTime}")
    private int classHourTime;// 每节课时的时长（分钟）

    /**
     * 身份证OCR识别
     *
     * @param filePath 身份证路径
     * @return R
     */
    @GetMapping("/idCardOCR")
    public R<Map<String, Object>> idCardOCR(@RequestParam String filePath) {
        Integer userId = SecureUtil.getUserId();
        //判断如果没有用户信息则进行登录
        if (Func.equals(userId, -1)) {
            return R.fail(ResultCode.UN_AUTHORIZED);
        }
        // 防止重复提交
        String key = "IdCardOCR" + userId;
        Object redisKey = redisUtil.get(key);
        if (Func.notNull(redisKey)) {
            return R.fail("提交次数过多，请30秒后再试");
        }
        redisUtil.set(key, "1", 30);
        // 身份证识别校验
        Student student = studentService.getById(userId);
        String idCardNo = student.getIdCardNo();
        if (student.getIsReal() == 2) {
            student.setIsReal(3);
            student.setIdCardPhoto(filePath);
            studentService.updateById(student);
            return R.success("已实名认证");
        }
        Map<String, Object> idCard = studentService.idCard(filePath);
        log.info("userId:{},idCard:{}", userId, idCard);
        if (Func.isEmpty(idCard)) {
            return R.fail("身份证照片不符合要求，请勿使用截图、参考【身份证照片上传示例】重新上传");
        }
        String headUrl = Convert.toStr(idCard.get("url"));
//        try {
//            // 1.根据headUrl判断图片宽高是否小于256*256
//            URL url = new URL(headUrl);
//            InputStream inputStream = url.openStream();
//            BufferedImage bufferedImage = ImageIO.read(inputStream);
//            int width = bufferedImage.getWidth();
//            int height = bufferedImage.getHeight();
//            if (width < 256 || height < 256) {
//                return R.fail("身份证照片不符合规范，请您重新更换一张照片或重新拍照上传");
//            }
//        } catch (IOException e) {
//            log.info("{},获取图片宽高失败:{}", headUrl, e.getMessage());
//            return R.fail("身份证照片不符合要求，请参考【身份证照片上传示例】重新上传 code:01");
//        }
        // 2.判断截取的头像是否存在
        Integer detectBodyCount = aliFaceRecognitionUtil.detectBodyCount(headUrl);
        if (detectBodyCount <= 0) {
            return R.fail("身份证照片不符合要求，请参考【身份证照片上传示例】重新上传 code:02");
        }
        // 3.比对身份证号码
        OcrDataUtil ocrDataUtil = BeanUtil.toBean(idCard.get("data"), OcrDataUtil.class);
        log.info("ocrDataUtil:{}", ocrDataUtil);
        if (!idCardNo.equals(ocrDataUtil.getIdNumber())) {
            return R.fail("身份证号码不一致，请上传本人身份证件照片");
        }
        student.setIdCardPhoto(filePath);
        student.setHeadImg(headUrl);
        student.setIsReal(1);
        studentService.updateById(student);
        return R.data(idCard);
    }

    public static void main(String[] args) {
        String zhengshuYxq = "2025年08月07日";
        DateTime zhengshuYxqParsed = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
        // 如果有效期小于当前时间，则返回证书过期
        if (zhengshuYxqParsed.isBefore(DateUtil.beginOfDay(DateUtil.date()))) {
            System.out.println(111);
        }else {
            System.out.println(222);
        }
    }


    /**
     * 课程观看校验
     * 包含实名认证校验、证书有效期校验
     *
     * @return R
     */
    @GetMapping("/checkIsReal")
    public R checkIsReal(@RequestParam Integer id) {
        Integer userId = SecureUtil.getUserId();
        Student student = studentService.getById(userId);
        Integer isReal = student.getIsReal();
        if (isReal == 0 || isReal == 2) {
            return R.data("5001", "未实名认证");
        }
        // 如果是开发环境，则直接返回success
//        if ("dev".equals(active) || "my".equals(active)) {
//            return R.data("200", "success");
//        }
        // 证书有效期
        TrxorderDetail trxorderDetail = trxorderDetailService.getById(id);
        String zhengshuYxq = trxorderDetail.getZhengshuYxq();
        DateTime zhengshuYxqParsed = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
        // 如果有效期小于当前时间，则返回证书过期
        if (zhengshuYxqParsed.isBefore(DateUtil.beginOfDay(DateUtil.date()))) {
            return R.data("5003", "您的证书 " + trxorderDetail.getZhengshuCode() + " 已过期，是否更新证书？");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("code", trxorderDetail.getZhengshuCode());
        params.put("authKey", "b2b04e9a1ce7e973accc83f4dc98f39f");
        String resp = HttpUtil.get("https://frag.gsjtsz.com/api/agryApi/checkIsStudy", params, 20000);
        JSONObject jsonObject = JSON.parseObject(resp);
        Integer code = jsonObject.getInteger("code");
        try {
            if (code == 200) {
                LambdaQueryWrapper<CourseKpoint> lqw = new LambdaQueryWrapper<>();
                lqw.eq(CourseKpoint::getCourseId, trxorderDetail.getCourseId());
                lqw.eq(CourseKpoint::getFrag, 1);
                List<CourseKpoint> courseKpoints = courseKpointService.list(lqw);
                if (CollUtil.isEmpty(courseKpoints)) {
                    return R.data("200", "success");
                }
                for (CourseKpoint courseKpoint : courseKpoints) {
                    CourseStudyhistory onesStudyHistory = courseStudyhistoryService.getOnesStudyHistory(userId, courseKpoint.getId(), 1, 0);
                    if (ObjectUtil.isEmpty(onesStudyHistory)) {
                        CourseStudyhistory courseStudyhistory = new CourseStudyhistory();
                        courseStudyhistory.setUserId(userId);
                        courseStudyhistory.setCourseId(courseKpoint.getCourseId());
                        courseStudyhistory.setKpointId(courseKpoint.getId());
                        courseStudyhistory.setPlayercount(1);
                        Course course = courseService.getById(courseKpoint.getCourseId());
                        courseStudyhistory.setCourseName(course.getCourseName());
                        courseStudyhistory.setKpointName(courseKpoint.getName());
                        courseStudyhistory.setKpointCourseId(0);
                        courseStudyhistory.setCourseType("1");
                        courseStudyhistory.setComplete("2");
                        courseStudyhistory.setWatchTime(Convert.toInt(courseKpoint.getVideoTime()));
                        courseStudyhistory.setStudyLearning("100%");
                        courseStudyhistory.setUpdateTime(new Date());
                        courseStudyhistory.setTrxorderDetailId(trxorderDetail.getId());
                        courseStudyhistory.setRemark("法人安管继续教育系统已完成");
                        courseStudyhistoryService.save(courseStudyhistory);
                        EduCourseHistoryDetail eduCourseHistoryDetail_new = new EduCourseHistoryDetail();
                        eduCourseHistoryDetail_new.setStudyhistoryId(courseStudyhistory.getId());
                        eduCourseHistoryDetail_new.setStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())));
                        eduCourseHistoryDetail_new.setEndTime(null);
                        eduCourseHistoryDetailService.save(eduCourseHistoryDetail_new);
                    } else {
                        // 如果是已完成，就跳过
                        if (Func.equals(onesStudyHistory.getComplete(), "2")) {
                            continue;
                        }
                        onesStudyHistory.setComplete("2");
                        onesStudyHistory.setWatchTime(Convert.toInt(courseKpoint.getVideoTime()));
                        onesStudyHistory.setStudyLearning("100%");
                        onesStudyHistory.setUpdateTime(new Date());
                        onesStudyHistory.setRemark("法人安管继续教育系统已完成");
                        courseStudyhistoryService.updateById(onesStudyHistory);
                    }
                }
            }
        } catch (Exception e) {
            log.error("checkIsReal error:{}", e.getMessage());
        }
//        List<StudentZhengshu> zhengShuInfoData = slryService.getZhengShuInfo(student.getIdCardNo());// 接口获取到的证书信息
//        if (Func.isEmpty(zhengShuInfoData)) {
//            return R.data("5002", "未查询到证书信息，请检查证书是否过期");
//        }
//        for (StudentZhengshu zhengShuInfoDatum : zhengShuInfoData) {
//            if (zhengShuInfoDatum.getNewzsbh().equals(trxorderDetail.getZhengshuCode())) {
//                // 证书编号相同
//                // 判断接口获取到证书有效期是否大于数据库中的证书有效期
//                DateTime zsyxq = DateUtil.parse(zhengShuInfoDatum.getZsyxq(), "yyyy年MM月dd日");
//                DateTime zsyxq2 = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
//                if (zsyxq.isAfter(zsyxq2)) {
//                    // 接口获取到的证书有效期大于数据库中的证书有效期
//                    return R.data("5003", "您的证书 " + zhengShuInfoDatum.getNewzsbh() + " 已过期，是否更新证书？");
//                }
//            }
//        }

        // 判断证书有效期时间，只有在距离有效期6个月内才能进行课程学习
//        DateTime zhengshuYxqDate = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
//        long month = DateUtil.betweenMonth(DateUtil.date(), zhengshuYxqDate, false);
//        boolean isBefore = zhengshuYxqDate.isBefore(DateUtil.beginOfDay(new Date()));
//        if (isBefore || month > 6) {
//            return R.data("5002", "请在证书有效期前6个月内学习");
//        }
        return R.data("200", "success");
    }

    /**
     * 获取用户考试记录
     */
    @GetMapping("/exlist")
    public R<List<ExVO>> list() {
        List<ExVO> exVOList = new ArrayList<>();
        Integer userId = SecureUtil.getUserId();
        Student student = studentService.getById(userId);
        // 根据userid查询用户当前生效的课程
        LambdaQueryWrapper<TrxorderDetail> lqw = new LambdaQueryWrapper<>();
        lqw.eq(TrxorderDetail::getUserId, userId);
        lqw.eq(TrxorderDetail::getAuthStatus, TrxorderDetail.STATUS_SUCCESS);
        lqw.eq(TrxorderDetail::getClearType, 0);
        lqw.gt(TrxorderDetail::getAuthTime, DateUtil.now());
        List<TrxorderDetail> trxorderDetailList = trxorderDetailService.list(lqw);
        for (TrxorderDetail trxorderDetail : trxorderDetailList) {
            Integer courseId = trxorderDetail.getCourseId();
            Course course = courseService.getById(courseId);
            int count = zhengshuService.count(new LambdaQueryWrapper<StudentZhengshu>()
                    .eq(StudentZhengshu::getNewzsbh, trxorderDetail.getZhengshuCode())
                    .eq(StudentZhengshu::getZsyxq, trxorderDetail.getZhengshuYxq())
                    .eq(StudentZhengshu::getStatus, 1)
            );
            if (count <= 0) {
                // 证书已过期
                continue;
            }
            List<Exampaper> exampaperList = exampaperService.list(new LambdaQueryWrapper<Exampaper>().eq(Exampaper::getCourseId, courseId));
            for (Exampaper exampaper : exampaperList) {
                ExVO exVO = new ExVO();
                exVO.setId(exampaper.getId());
                exVO.setName(exampaper.getName());
                exVO.setLogo(course.getLogo());
                int y = courseStudyhistoryService.getstudykpoint(courseId);
                int v = courseStudyhistoryService.getstudystudyisok(userId, courseId, 0);
                exVO.setStudyKpoints(y);
                exVO.setStudyKpoint(v);

                // 学员只需在小程序中选择相应课程，累计学习时间达到24课时（每课时45分钟，总计1080分钟）即可参加继续教育在线考试。
                // 观看课时数
                int watchTime = courseStudyhistoryService.getstudystudyisokWatchTime(userId, courseId, 0);
                float classHour = (float) watchTime / 60 / classHourTime;
                DecimalFormat classHour_df = new DecimalFormat("0.00");//格式化小数
                classHour_df.setRoundingMode(RoundingMode.DOWN);
                exVO.setClassHours(classHour_df.format(classHour));
                // 需要观看课时数
                float needClassHour = (float) needWatchTime / 60 / classHourTime;
                DecimalFormat needClassHour_df = new DecimalFormat("0");//格式化小数
                exVO.setNeedClassHours(needClassHour_df.format(needClassHour));
                // 当前课程所有章节的时长（秒）
                int classAllHours = courseStudyhistoryService.getClassAllHours(courseId);
                float classAllHours_df = (float) classAllHours / 60 / classHourTime;
                DecimalFormat classAllHours_df_df = new DecimalFormat("0");//格式化小数
                exVO.setClassAllHours(classAllHours_df_df.format(classAllHours_df));
                // 观看课时数百分比
                if (watchTime <= 0) {
                    exVO.setClassHoursPercent("0.00%");
                } else {
                    float num = (float) watchTime * 100 / classAllHours;
                    // 保留整数
                    DecimalFormat num_df = new DecimalFormat("0");//格式化小数
                    num_df.format(num);
                    exVO.setClassHoursPercent(num_df.format(num) + "%");
                }
                // 每节课时的时长（分钟）
                exVO.setClassHoursTime(Integer.toString(classHourTime));

                exVOList.add(exVO);
            }
        }
        return R.data(exVOList);
    }


    /**
     * 修改 学员表
     */
    @PostMapping("/updateStudent")
    public R update(@RequestBody Student student) {
        Integer userId = SecureUtil.getUserId();
        student.setId(userId);
        return R.status(studentService.updateById(student));
    }

    /**
     * 视频弹出题目
     */
    @GetMapping("/getVideoQuestion")
    public R<List<VideoQuestion>> getVideoQuestion(String id) {
        LambdaQueryWrapper<VideoQuestion> lqw = new LambdaQueryWrapper<>();
        lqw.eq(VideoQuestion::getCourseInfoId, id);
        List<VideoQuestion> videoQuestions = videoQuestionService.list(lqw);
        return R.data(videoQuestions);
    }

    /**
     * 更新证书
     *
     * @return
     */
    @GetMapping("/updateZhengShu")
    public R<List<StudentZhengshu>> updateZhengShu() {
        return slryService.updateZhengShu();
    }


    /**
     * 获取课程播放记录(学习记录)详情
     */
    @GetMapping("/coursestudyhistoryDetail")
    public R<CourseStudyhistory> detail(CourseStudyhistory courseStudyhistory) {
        TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                .eq(TrxorderDetail::getUserId, SecureUtil.getUserId())
                .eq(TrxorderDetail::getCourseId, courseStudyhistory.getCourseId())
                .eq(TrxorderDetail::getClearType, 0)
        );
        LambdaQueryWrapper<CourseStudyhistory> lqw = new LambdaQueryWrapper<>();
        lqw.eq(CourseStudyhistory::getCourseId, courseStudyhistory.getCourseId());
        lqw.eq(CourseStudyhistory::getKpointId, courseStudyhistory.getKpointId());
        lqw.eq(CourseStudyhistory::getUserId, SecureUtil.getUserId());
        lqw.eq(CourseStudyhistory::getTrxorderDetailId, trxorderDetail.getId());
        CourseStudyhistory detail = courseStudyhistoryService.getOne(lqw);
        if (Func.isEmpty(detail)) {
            // 设置默认值
            detail = new CourseStudyhistory();
            detail.setWatchTime(0);
        }
        return R.data(detail);
    }

    /**
     * 获取证书
     *
     * @return R
     */
    @GetMapping("/getZhengshu")
    public R<List<StudentZhengshu>> getZhengshu() {
        Integer userId = SecureUtil.getUserId();
        Student student = studentService.getById(userId);
        String idCardNo = student.getIdCardNo();
        // 根据身份证id查询证书信息
        LambdaQueryWrapper<StudentZhengshu> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StudentZhengshu::getRyid, idCardNo);
        lqw.eq(StudentZhengshu::getStatus, 1);
        List<StudentZhengshu> studentZhengshuList = zhengshuService.list(lqw);// 已有的证书信息
        return R.data(studentZhengshuList);
    }

    /**
     * 观看视频心跳检测
     */
    @PostMapping("/heartbeat/submit")
    public R<HeartbeatVO> heartbeatSubmit(@RequestBody HeartbeatVO heartbeatVO) {
        slryService.updateHeartBeat(heartbeatVO);
        return R.success("success");
    }

//    public static void main(String[] args) {
//        String zhengshuYxq = "2024年2月15日";
//        long l = DateUtil.betweenMonth(DateUtil.date(), DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日"), false);
//        System.out.println("l = " + l);
//    }
}
