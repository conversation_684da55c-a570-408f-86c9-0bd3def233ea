<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.edu.mapper.EduCourseHistoryDetailPictureMapper">
    <resultMap id="BaseResultMap" type="org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture">
        <!--@mbg.generated-->
        <!--@Table edu_course_history_detail_picture-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="history_detail_id" jdbcType="INTEGER" property="historyDetailId" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="ip_count" jdbcType="VARCHAR" property="count" />
<!--        <result column="end_time" jdbcType="VARCHAR" property="endTime" />-->
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, history_detail_id, url, address, ip
    </sql>

    <select id="getAbnormalIPVOs" resultMap="BaseResultMap">
        SELECT t.ip,
               COUNT(DISTINCT t.company_name) AS ip_count
        FROM (SELECT main.*
              FROM (SELECT picture.ip,
                           trxorder.company_name,
                           detail.end_time
                    FROM edu_course_history_detail_picture picture
                             INNER JOIN edu_course_history_detail detail ON detail.id = picture.history_detail_id
                             INNER JOIN edu_course_studyhistory history ON history.id = detail.studyhistory_id
                             INNER JOIN edu_trxorder_detail trxorder ON trxorder.id = history.trxorder_detail_id
                    WHERE trxorder.id_card_no NOT IN (SELECT id_card FROM edu_student_whitelist WHERE STATUS = 1)
                      AND trxorder.examine_type = '0'
                    GROUP BY picture.ip, trxorder.company_name, detail.end_time) AS main ${ew.customSqlSegment}) t
        WHERE LENGTH(t.ip) > 0
        GROUP BY t.ip
        HAVING COUNT(DISTINCT t.company_name) > 1
        ORDER BY ip_count DESC;
    </select>
</mapper>