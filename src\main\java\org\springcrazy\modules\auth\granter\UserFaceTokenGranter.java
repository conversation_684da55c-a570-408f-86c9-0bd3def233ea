
package org.springcrazy.modules.auth.granter;

import lombok.AllArgsConstructor;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.auth.enums.UserEnum;
import org.springcrazy.modules.system.entity.UserInfo;
import org.springcrazy.modules.system.service.IUserService;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.stereotype.Component;

/**
 * PasswordTokenGranter
 */
@Component
@AllArgsConstructor
public class UserFaceTokenGranter implements ITokenGranter {

    public static final String GRANT_TYPE = "userFace";

    private IUserService userService;
    private IStudentService studentService;

    @Override
    public UserInfo grant(TokenParameter tokenParameter) {

        String tenantId = tokenParameter.getArgs().getStr("tenantId");
        String account = tokenParameter.getArgs().getStr("account");
        String password = tokenParameter.getArgs().getStr("password");
        String userFaceImage = tokenParameter.getArgs().getStr("userFaceImage");
        String wxCode = tokenParameter.getArgs().getStr("wxCode");
        String wxSign = tokenParameter.getArgs().getStr("wxSign");
        Integer wxVersion = tokenParameter.getArgs().getInt("wxVersion");
        UserInfo userInfo = null;
        if (Func.isNoneBlank(account)) {
            // 获取用户类型
            String userType = tokenParameter.getArgs().getStr("userType");
            if (userType.equals(UserEnum.STUDENT.getName())) {
                userInfo = studentService.userInfo_face(account, userFaceImage, wxCode ,wxSign, wxVersion);
            }
        }
        return userInfo;
    }

}
