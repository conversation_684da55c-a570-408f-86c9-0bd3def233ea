package org.springcrazy.modules.slry.vo;

import lombok.Data;

@Data
public class ExVO {

    /**
     * 试卷id
     */
    private Integer id;

    private String logo;

    private String name;

    /**
     * 应学章节数
     */
    private Integer studyKpoints;

    /**
     * 已学章节数
     */
    private Integer studyKpoint;

    /**
     * 当前课程所有章节的课时数
     */
    private String classAllHours;

    /**
     * 观看课时数
     */
    private String classHours;

    /**
     * 需要观看课时数
     */
    private String needClassHours;

    /**
     * 观看课时数的百分率
     */
    private String classHoursPercent;

    /**
     * 每节课时的时长
     */
    private String classHoursTime;

}
