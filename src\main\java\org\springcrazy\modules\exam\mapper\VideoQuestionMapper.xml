<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.VideoQuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springcrazy.modules.exam.entity.VideoQuestion">
        <id column="id" property="id" />
        <result column="course_info_id" property="courseInfoId" />
        <result column="topic" property="topic" />
        <result column="type" property="type" />
        <result column="right_answer" property="rightAnswer" />
        <result column="topic_time" property="topicTime" />
        <result column="back_time" property="backTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_info_id, topic, type, right_answer, topic_time, back_time
    </sql>

    <select id="selectVideoQuestionPage" resultMap="BaseResultMap">
        select * from exam_video_question
    </select>
</mapper>
