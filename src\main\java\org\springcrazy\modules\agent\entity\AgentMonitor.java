package org.springcrazy.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import org.springcrazy.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 监控设备
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@TableName("edu_agent_monitor")
@ApiModel(value = "AgentMonitor对象", description = "监控设备")
public class AgentMonitor extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "代理商用户ID")
    private Integer agentId;

    @ApiModelProperty(value = "教室ID")
    private Integer classroomId;

    @ApiModelProperty(value = "设备名称")
    private String name;

    @ApiModelProperty(value = "设备SN号")
    @TableField("codeNum")
    private String codeNum;

    @ApiModelProperty(value = "设备标识 1前  2后")
    private Integer monitor;

    @ApiModelProperty(value = "设备IP地址")
    private String ip;
}
