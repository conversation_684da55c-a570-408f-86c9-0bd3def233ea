package org.springcrazy.modules.slry.vo;

import lombok.Data;

@Data
public class HeartbeatVO {

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 当前观看进度（秒）
     */
    private Integer watchStingTime;

    /**
     * 课程id
     */
    private Integer courseId;

    /**
     * 节点id
     */
    private Integer kpointId;

    /**
     * 查询课程类型
     */
    private String courseType;

    /**
     * 套餐父级id
     */
    private Integer kpointCourseId;

}
