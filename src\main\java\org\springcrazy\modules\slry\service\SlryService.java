package org.springcrazy.modules.slry.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.log.exception.ServiceException;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.constant.CrazyConstant;
import org.springcrazy.core.tool.utils.*;
import org.springcrazy.modules.auth.entity.OnlineUser;
import org.springcrazy.modules.edu.entity.*;
import org.springcrazy.modules.edu.service.*;
import org.springcrazy.modules.exam.entity.Exampaper;
import org.springcrazy.modules.exam.entity.ExampaperRecord;
import org.springcrazy.modules.exam.service.IExampaperRecordService;
import org.springcrazy.modules.exam.service.IExampaperService;
import org.springcrazy.modules.exam.vo.FaceResult;
import org.springcrazy.modules.front.vo.VefVO;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.vo.HeartbeatVO;
import org.springcrazy.modules.system.service.IDictService;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
@RequiredArgsConstructor
public class SlryService {

    private final ICourseService courseService;
    private final IExampaperService exampaperService;
    private final IExampaperRecordService exampaperRecordService;
    private final ICourseStudyhistoryService courseStudyhistoryService;
    private final EduCourseHistoryDetailService eduCourseHistoryDetailService;
    private final EduCourseHistoryDetailPictureService eduCourseHistoryDetailPictureService;
    private final StudentZhengshuService studentZhengshuService;
    private final IDictService dictService;
    private final IStudentService studentService;
    private final RedisUtil redisUtil;
    private final ITrxorderDetailService trxorderDetailService;


    @Value("${slry.zhengshu.appKey}")
    private String ZHENGSHU_APPKEY;

    @Value("${slry.zhengshu.sign}")
    private String ZHENGSHU_SIGN;

    @Value("${slry.zhengshu.time}")
    private String ZHENGSHU_TIME;

    @Value("${slry.zhengshu.gssecretKey}")
    private String ZHENGSHU_GSSECRETKEY;

    @Value("${video.interval}")
    private Integer videoInterval;

    @Value("${slry.api-old-examine}")
    private String OLD_EXAMINE;
    public static final String SLRY_COURSE = "slry_course";

    @Value("${slry.api-zhengshu}")
    private String ZHENGSHU;

    public static final String slry_updateZhengShu_key = "slry_updateZhengShu";

    public static final Integer SLRY_COURSE_ID_A = 2259;
    public static final Integer SLRY_COURSE_ID_B = 2260;
    public static final Integer SLRY_COURSE_ID_C = 2261;

    public List<StudentZhengshu> getZhengShuInfo(String idcard) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("appKey", ZHENGSHU_APPKEY);
            params.put("sign", ZHENGSHU_SIGN);
            params.put("time", ZHENGSHU_TIME);
            params.put("idcard", idcard);
            log.info("{},getZhengShuInfoRequest:{}", idcard, params);
            //链式构建请求
            String data = HttpRequest.get(ZHENGSHU)
                    .header("gssecretKey", ZHENGSHU_GSSECRETKEY)
                    .form(params)
                    .timeout(20000)
                    .execute().body();
            log.info("{},getZhengShuInfoResponse:{}", idcard, data);
            JSONObject jsonObject = JSON.parseObject(data);
            JSONArray zhengshuData = jsonObject.getJSONArray("Data");
            List<StudentZhengshu> zhengShuList = zhengshuData.toJavaList(StudentZhengshu.class);
            if (Func.isEmpty(zhengShuList)) {
                return null;
            }
            for (StudentZhengshu zhengShu : zhengShuList) {
                String newzsbh = zhengShu.getNewzsbh();
                zhengShu.setZxtype(String.valueOf(newzsbh.charAt(3)));
                zhengShu.setCreatetime(DateUtil.now());
                // 判断证书有效期，如果有效期小于当前时间，证书状态为过期
                String zsyxq = zhengShu.getZsyxq();
                Date parse = DateUtil.parse(zsyxq, "yyyy年MM月dd日");
                if (parse.getTime() < System.currentTimeMillis()) {
                    zhengShu.setStatus(0);
                } else {
                    zhengShu.setStatus(1);
                }
            }
            return zhengShuList;
        } catch (Exception e) {
            log.info("获取证书信息失败", e);
            throw new ServiceException("获取证书信息失败");
        }
    }


    public R<List<StudentZhengshu>> updateZhengShu() {
        Integer userId = SecureUtil.getUserId();
        Object o = redisUtil.get(slry_updateZhengShu_key + userId);
        if (ObjectUtil.isNotEmpty(o)) {
            return R.fail("请勿重复提交数据");
        }
        // 未提交过数据
        redisUtil.set(slry_updateZhengShu_key + userId, "1", 3);

        Student student = studentService.getById(userId);
        String idCardNo = student.getIdCardNo();
        if (Func.isEmpty(idCardNo)) {
            return R.fail("未查询到您的身份证号码,请稍后重试");
        }
        List<StudentZhengshu> zhengShuInfoData = getZhengShuInfo(idCardNo);// 接口获取到的证书信息
        if (Func.isEmpty(zhengShuInfoData)) {
            return R.fail("未查询到您的证书信息,请稍后重试");
        }
        // 根据身份证id查询证书信息
        LambdaQueryWrapper<StudentZhengshu> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StudentZhengshu::getRyid, idCardNo);
        lqw.eq(StudentZhengshu::getStatus, 1);
        List<StudentZhengshu> studentZhengshuList = studentZhengshuService.list(lqw);// 已有的证书信息
        for (StudentZhengshu zhengShu : zhengShuInfoData) {
            // 是否有新的证书
            boolean flag = false;
            boolean flagZs = false;
            for (StudentZhengshu zhengShu1 : studentZhengshuList) {
                if (zhengShu.getNewzsbh().equals(zhengShu1.getNewzsbh())) {
                    // 证书编号相同，判断接口获取到证书有效期是否大于数据库中的证书有效期
                    String zsyxq = zhengShu.getZsyxq();
                    Date parse = DateUtil.parse(zsyxq, "yyyy年MM月dd日");
                    String zsyxq1 = zhengShu1.getZsyxq();
                    Date parse1 = DateUtil.parse(zsyxq1, "yyyy年MM月dd日");
                    if (parse.getTime() > parse1.getTime()) {
                        // 判断该证书新系统是否审核通过
                        TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                                        .eq(TrxorderDetail::getZhengshuCode, zhengShu1.getNewzsbh())
                                        .eq(TrxorderDetail::getClearType, 0)
//                                .eq(TrxorderDetail::getExamineType, 1)
                        );
                        // 判断该证书旧系统是否审核通过
                        Map<String, Object> params = new HashMap<>();
                        params.put("code", zhengShu1.getNewzsbh());
                        String data_old = HttpUtil.get(OLD_EXAMINE, params);
                        JSONObject jsonObject = JSON.parseObject(data_old);
                        Integer codeInfo = jsonObject.getInteger("code");
                        boolean new_detail = Func.isNotEmpty(trxorderDetail) && trxorderDetail.getExamineType() == 1;
                        boolean old_detail = Func.isNotEmpty(trxorderDetail) && trxorderDetail.getExamineType() == 0 && codeInfo == 0;
                        // 如果新系统审核通过或者旧系统审核通过，证书已延期，系统清除
                        if (new_detail || old_detail) {
                            StudentZhengshu zhengShu2 = new StudentZhengshu();
                            BeanUtil.copyProperties(zhengShu, zhengShu2);
                            zhengShu2.setId(zhengShu1.getId());
                            zhengShu2.setUpdatetime(DateUtil.now());
                            studentZhengshuService.updateById(zhengShu2);
                            trxorderDetail.setClearType(1);
                            trxorderDetail.setClearedType(3);
                            trxorderDetail.setClearTime(DateUtil.now());
                            trxorderDetail.setClearUserId(1);
                            trxorderDetail.setLastUpdateTime(DateUtil.now());
                            trxorderDetail.setExamineMsg(new_detail ? "证书已延期，系统清除" : "旧系统审核通过，证书已延期，系统清除");
                            trxorderDetailService.updateById(trxorderDetail);
                            flagZs = true;
                        } else {
                            flag = true;
                        }
                        break;
                    } else {
                        flag = true;
                        break;
                    }
                }
            }
            if (!flag) {
                if (zhengShu.getStatus() != 0) {
                    // 根据证书类型获取课程id
                    String slryCourse = dictService.getValue(SLRY_COURSE, zhengShu.getZxtype());
                    // 根据证书信息关联课程
                    courseService.giveCourse(Convert.toStr(student.getId()), slryCourse, zhengShu);
                }
                if (!flagZs) {
                    studentZhengshuService.save(zhengShu);
                }
            }
        }

        // 校验是否存在重复的A、B、C证书
        List<Integer> courseIds = Arrays.asList(SLRY_COURSE_ID_A, SLRY_COURSE_ID_B, SLRY_COURSE_ID_C); // 需要定义B、C证书的ID常量
        for (Integer courseId : courseIds) {
            List<TrxorderDetail> trxorderDetailList = trxorderDetailService.list(
                    new LambdaQueryWrapper<TrxorderDetail>()
                            .eq(TrxorderDetail::getUserId, student.getId())
                            .eq(TrxorderDetail::getCourseId, courseId)
                            .eq(TrxorderDetail::getClearType, 0)
                            .orderByAsc(TrxorderDetail::getZhengshuYxq)
            );
            // 如果存在多个相同类型证书，设置清除状态clearType，证书有效期最近的为有效0，其余为无效-1
            if (trxorderDetailList.size() > 1) {
                for (int i = 0; i < trxorderDetailList.size(); i++) {
                    trxorderDetailList.get(i).setClearType(i == 0 ? 0 : -1);
                }
                trxorderDetailService.updateBatchById(trxorderDetailList);
            }
        }

        // 更新学员信息
        LambdaUpdateWrapper<Student> studentLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        studentLambdaUpdateWrapper.eq(Student::getId, userId);
        studentLambdaUpdateWrapper.set(Student::getZhengshuInfo, JSON.toJSONString(zhengShuInfoData));
        studentLambdaUpdateWrapper.set(Student::getRealName, zhengShuInfoData.get(0).getRyxm());
        studentLambdaUpdateWrapper.set(Student::getCompanyName, zhengShuInfoData.get(0).getQymc());
        // 男：1，女：2
        studentLambdaUpdateWrapper.set(Student::getSex, StrUtil.contains(zhengShuInfoData.get(0).getRyxb(), "男") ? 1 : 2);
        studentService.update(studentLambdaUpdateWrapper);
        return R.data(zhengShuInfoData);
    }

    public FaceResult updateCourseStudyhistory(String filePath, VefVO vefVO, String mobile, boolean flag) {
        log.info("开始更新学习记录:手机号码:{},照片地址:{},课程id:{},课时id:{},学习时长:{}秒", mobile, filePath, vefVO.getCourseId(), vefVO.getKpointId(), vefVO.getWatchStingTime());
        //检测同一个用户同一章节的学习记录时长请求，如果50秒内请求超过3次，则为非正常请求。不接收。
        String studyTimeRedis = "STUDYTIMEREDIS_" + SecureUtil.getUserId() + "_" + vefVO.getKpointId();
        Object studyTimeRedisNums = redisUtil.get(studyTimeRedis);
        int StudyGetNums = 0;
        // 判断redis存储的是否有值
        if (Func.notNull(studyTimeRedisNums)) {
            StudyGetNums = (int) studyTimeRedisNums;
            //如果存储中的值大于等于2，说明本次请求为50秒内的第三次。则判断为无效。不进行储存
            if (StudyGetNums >= 5) {
                log.info("用户{}频繁请求学习记录接口", mobile);
                return FaceResult.error(500, "请勿频繁请求学习记录接口");
            } else {
                StudyGetNums += 1;
            }
        } else {
            StudyGetNums = 1;
        }
        redisUtil.set(studyTimeRedis, StudyGetNums, 50);
        //查询学习记录
        vefVO.setUserId(SecureUtil.getUserId());
        TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                .eq(TrxorderDetail::getUserId, SecureUtil.getUserId())
                .eq(TrxorderDetail::getCourseId, vefVO.getCourseId())
                .eq(TrxorderDetail::getClearType, 0)
        );
        QueryWrapper<CourseStudyhistory> courseStudyhistoryQueryWrapper = new QueryWrapper<>();
        courseStudyhistoryQueryWrapper.lambda().eq(CourseStudyhistory::getCourseId, vefVO.getCourseId())
                .eq(CourseStudyhistory::getUserId, vefVO.getUserId())
                .eq(CourseStudyhistory::getKpointId, vefVO.getKpointId())
                .eq(CourseStudyhistory::getCourseType, vefVO.getCourseType())
                .eq(CourseStudyhistory::getKpointCourseId, vefVO.getKpointCourseId())
                .eq(CourseStudyhistory::getTrxorderDetailId, trxorderDetail.getId());
        CourseStudyhistory tcourse = courseStudyhistoryService.getOne(courseStudyhistoryQueryWrapper);
        //如果学习记录不为空，则保存学习记录
        if (Func.isEmpty(tcourse)) {
            log.info("用户{}学习记录为空", mobile);
            return FaceResult.success("学习记录为空");
        }
        // 本次学习时长
        int times = Integer.parseInt(vefVO.getWatchStingTime());
        if (times == 0) {
            // 如果学习时长为0则不保存学习记录
            log.info("用户{}学习时长为0", mobile);
//            EduCourseHistoryDetail eduCourseHistoryDetail = new EduCourseHistoryDetail();
//            eduCourseHistoryDetail.setStudyhistoryId(tcourse.getId());
//            eduCourseHistoryDetail.setStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())));
//            eduCourseHistoryDetail.setEndTime(null);
//            eduCourseHistoryDetailService.save(eduCourseHistoryDetail);
            return FaceResult.success("学习时长为0");
        }
        vefVO.setId(tcourse.getId());
        vefVO.setWatchTime(times);
        //根据课程id和章节id查询该章节应该学习的时间，点播的化从视频云处拿到视频时长，直播是结束时间减开始时间。
        CourseStudyhistory courseStudyhistory2 = courseStudyhistoryService.getKpointAndCourse(tcourse.getCourseId(), tcourse.getKpointId());
        int videoTime = Integer.parseInt(courseStudyhistory2.getVideoTime());
        String recognizeFaceTime = courseStudyhistory2.getRecognizeFaceTime();
        List<String> split = StrUtil.split(recognizeFaceTime, ",");
        if (CollUtil.isNotEmpty(split)) {
            for (String s : split) {
                // 如果当前观看时间和课时人脸识别时间相等则加1秒
                if (s.equals(vefVO.getWatchStingTime())) {
                    times += 1;
                }
            }
        }
        // 获取上次学习时长
        int watchTime = tcourse.getWatchTime();
        // 如果完成学习了则不保存学习记录
        if (StrUtil.equals(tcourse.getComplete(), "2")) {
            log.info("用户{}该章节已完成学习", mobile);
            return FaceResult.success("该章节已完成学习");
        }
        //如果章节的视频时长是小等于0则他的学习进度是0
        if (videoTime <= 0) {
            vefVO.setStudyLearning("0.00%");
        } else {
            float num = (float) times * 100 / videoTime;
            DecimalFormat df = new DecimalFormat("0.00");//格式化小数
            if (num < 100) {
                vefVO.setStudyLearning(df.format(num) + "%");
            } else if (num >= 100) {
                if (!flag && tcourse.getHeartEnable() == 1) {
                    // 获取心跳次数
                    int heartbeatCount = tcourse.getHeartbeatCount();
                    if (heartbeatCount < 2) {
                        log.info("用户{},课时id{}.心跳次数不足,无法保存课时 01", mobile, tcourse.getId());
                        return FaceResult.error(500, "error 01");
                    }
                    // 计算合理的心跳次数
                    int expectedHeartbeatCount = videoTime / 60; // 假设每分钟提交一次心跳
                    int countMargin = 3; // 允许的心跳次数误差
                    // 验证心跳次数是否合理
                    if (heartbeatCount < expectedHeartbeatCount - countMargin) {
                        log.info("用户{},课时id{}.心跳次数不足,无法保存课时 02", mobile, tcourse.getId());
                        return FaceResult.error(500, "error 02");
                    }

                    // 新增：验证学习时间间隔的合理性，防止快速刷心跳
                    if (!validateStudyTime(tcourse, videoTime, mobile)) {
                        log.info("用户{},课时id{}.学习时间校验失败,无法保存课时 03", mobile, tcourse.getId());
                        return FaceResult.error(500, "error 03");
                    }
                }
                vefVO.setUpdateTime(new Date());
                vefVO.setComplete("2");
                vefVO.setStudyLearning("100%");
            }
        }
        courseStudyhistoryService.updateById(vefVO);
        // 修改完成后查询最新的一条学习记录并修改学习结束时间
        EduCourseHistoryDetail eduCourseHistoryDetail = eduCourseHistoryDetailService.getOne(new LambdaQueryWrapper<EduCourseHistoryDetail>()
                .eq(EduCourseHistoryDetail::getStudyhistoryId, vefVO.getId())
                .orderByDesc(EduCourseHistoryDetail::getStartTime)
                .last("limit 1"));
        if (Func.isNotEmpty(eduCourseHistoryDetail)) {
            eduCourseHistoryDetail.setEndTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())));
            eduCourseHistoryDetailService.updateById(eduCourseHistoryDetail);
            // 每次修改学习记录时新增学习记录详情照片数据
            EduCourseHistoryDetailPicture eduCourseHistoryDetailPicture = new EduCourseHistoryDetailPicture();
            eduCourseHistoryDetailPicture.setHistoryDetailId(eduCourseHistoryDetail.getId());
            eduCourseHistoryDetailPicture.setUrl(filePath);
            Object onlineUserInfo = redisUtil.get(CrazyConstant.ONLINE_KEY + mobile);
            if (Func.isNotEmpty(onlineUserInfo)) {
                OnlineUser onlineUserDto = (OnlineUser) onlineUserInfo;
                eduCourseHistoryDetailPicture.setAddress(onlineUserDto.getAddress());
                eduCourseHistoryDetailPicture.setIp(onlineUserDto.getIp());
            }
            eduCourseHistoryDetailPictureService.save(eduCourseHistoryDetailPicture);
            // 创建新学习记录详情
            EduCourseHistoryDetail eduCourseHistoryDetail_new = new EduCourseHistoryDetail();
            eduCourseHistoryDetail_new.setStudyhistoryId(tcourse.getId());
            eduCourseHistoryDetail_new.setStartTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(System.currentTimeMillis())));
            eduCourseHistoryDetail_new.setEndTime(null);
            eduCourseHistoryDetailService.save(eduCourseHistoryDetail_new);
        }
        return FaceResult.success("学习记录更新完成");
    }


    public void saveExampaperRecord(Integer id, String slryCourse) {
        // 查询所有课程
        List<Course> courseList = courseService.listByIds(Func.toIntList(slryCourse));
        // 关联试卷
        courseList.forEach(course -> {
            // 根据课程id查询试卷
            Integer couresId = course.getId();
            Exampaper exampaper = exampaperService.getOne(new QueryWrapper<Exampaper>().lambda().eq(Exampaper::getCourseId, couresId));
            ExampaperRecord exampaperRecord = new ExampaperRecord();
            exampaperRecord.setUserId(id);
            exampaperRecord.setEpId(exampaper.getId());
            exampaperRecord.setAddTime(LocalDateTime.now());
            exampaperRecord.setQstScore(exampaper.getScore());
            exampaperRecord.setQstCount(exampaper.getQstCount());
            exampaperRecord.setSubjectId(exampaper.getSubjectId());
            exampaperRecord.setExamType(exampaper.getType());
            exampaperRecord.setPaperName(exampaper.getName());
            exampaperRecord.setCourseId(couresId);
            exampaperRecordService.save(exampaperRecord);
        });
    }

    public void updateHeartBeat(HeartbeatVO heartbeatVO) {
        Integer courseId = heartbeatVO.getCourseId();
        String courseType = heartbeatVO.getCourseType();
        Integer kpointId = heartbeatVO.getKpointId();
        Integer kpointCourseId = heartbeatVO.getKpointCourseId();
        Integer watchStingTime = heartbeatVO.getWatchStingTime();

        //查询学习记录
        heartbeatVO.setUserId(SecureUtil.getUserId());
        TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                .eq(TrxorderDetail::getUserId, SecureUtil.getUserId())
                .eq(TrxorderDetail::getCourseId, courseId)
                .eq(TrxorderDetail::getClearType, 0)
        );
        QueryWrapper<CourseStudyhistory> courseStudyhistoryQueryWrapper = new QueryWrapper<>();
        courseStudyhistoryQueryWrapper.lambda().eq(CourseStudyhistory::getCourseId, courseId)
                .eq(CourseStudyhistory::getUserId, heartbeatVO.getUserId())
                .eq(CourseStudyhistory::getKpointId, kpointId)
                .eq(CourseStudyhistory::getCourseType, courseType)
                .eq(CourseStudyhistory::getKpointCourseId, kpointCourseId)
                .eq(CourseStudyhistory::getTrxorderDetailId, trxorderDetail.getId());
        CourseStudyhistory courseStudyhistory = courseStudyhistoryService.getOne(courseStudyhistoryQueryWrapper);
        // 检查记录是否存在
        if (Func.isEmpty(courseStudyhistory)) {
            throw new ServiceException("学习记录不存在");
        }
        // 检查是否已标记为完成
        if ("2".equals(courseStudyhistory.getComplete())) {
            return;
        }

        Student student = studentService.getById(SecureUtil.getUserId());
        boolean flag = studentService.checkWhiteList(student);
        if (!flag) {
            // 检查是否有其他课时记录正在进行中（未完成）
            List<CourseStudyhistory> otherStudyHistories = courseStudyhistoryService.list(
                    new LambdaQueryWrapper<CourseStudyhistory>()
                            .eq(CourseStudyhistory::getUserId, heartbeatVO.getUserId())
                            .ne(CourseStudyhistory::getId, courseStudyhistory.getId()) // 排除当前课时记录
                            .ne(CourseStudyhistory::getComplete, "2") // 未完成的课时
                            .isNotNull(CourseStudyhistory::getLastHeartbeatTime) // 有心跳记录的
            );

            if (CollUtil.isNotEmpty(otherStudyHistories)) {
                // 检查是否有其他课时在最近5分钟内有心跳记录（表示正在学习）
                Date fiveMinutesAgo = new Date(System.currentTimeMillis() - 5 * 60 * 1000);
                boolean hasActiveStudy = otherStudyHistories.stream()
                        .anyMatch(history -> history.getLastHeartbeatTime() != null
                                && history.getLastHeartbeatTime().after(fiveMinutesAgo));

                if (hasActiveStudy) {
                    String ip = WebUtil.getIP();
                    String ipInfoStr = getInfoNew(ip);
                    String locationInfo = parseLocationInfo(ipInfoStr);
                    log.info("updateHeartBeat 用户[{}]课程[{}]课时[{}]IP[{}]地址信息[{}]心跳更新失败-并发学习检测", heartbeatVO.getUserId(), courseId, kpointId, ip, locationInfo);
                    throw new ServiceException("系统繁忙，请稍后重试");
                }
            }
        }

        // 获取视频时长
//        CourseStudyhistory courseStudyhistory2 = courseStudyhistoryService.getKpointAndCourse(courseStudyhistory.getCourseId(), courseStudyhistory.getKpointId());
//        int videoTime = Integer.parseInt(courseStudyhistory2.getVideoTime());
        // 验证观看速度是否合理
        if (!isValidWatchSpeed(courseStudyhistory, watchStingTime)) {
            String ip = WebUtil.getIP();
            String ipInfoStr = getInfoNew(ip);
            String locationInfo = parseLocationInfo(ipInfoStr);
            log.info("updateHeartBeat 用户[{}]课程[{}]课时[{}]IP[{}]地址信息[{}]心跳更新失败-速度异常", heartbeatVO.getUserId(), courseId, kpointId, ip, locationInfo);
            throw new ServiceException("系统繁忙，请稍后重试");
        }
        // 如果用户暂停了视频，允许观看进度不变
        if (!Objects.equals(watchStingTime, courseStudyhistory.getLastHeartbeatProgress())) {
            courseStudyhistory.setHeartbeatCount(courseStudyhistory.getHeartbeatCount() + 1);
        }
        courseStudyhistory.setLastHeartbeatTime(new Date());
        courseStudyhistory.setLastHeartbeatProgress(watchStingTime);
        // 更新学习记录
        courseStudyhistoryService.updateById(courseStudyhistory);
    }

    private boolean isValidWatchSpeed(CourseStudyhistory courseStudyhistory, Integer watchStingTime) {
        Date lastHeartbeatTime = courseStudyhistory.getLastHeartbeatTime();// 最后一次心跳时间
        if (lastHeartbeatTime == null) {
            return true; // 第一次心跳，直接通过
        }

        long currentTime = new Date().getTime();// 当前时间
        long lastHeartbeat = lastHeartbeatTime.getTime();// 最后一次心跳时间时间戳
        long timeInterval = currentTime - lastHeartbeat;/// 上次心跳时间到本次心跳时间用了多长时间

        // 计算合理的时间间隔
        long expectedTimeInterval = 60000; // 假设每分钟提交一次心跳
        long timeErrorMargin = 5000; // 5秒的时间误差

        // 验证时间间隔是否合理
        if (timeInterval < (expectedTimeInterval - timeErrorMargin)) {
            log.info("updateHeartBeat 用户[{}]课时[{}]心跳更新失败-速度异常-当前:{} 上次:{} 间隔:{}ms", courseStudyhistory.getUserId(), courseStudyhistory.getId(), currentTime, lastHeartbeat, timeInterval);
            return false;
        }
        log.info("updateHeartBeat [{}]课时[{}]心跳正常-间隔:{}ms", courseStudyhistory.getUserId(), courseStudyhistory.getId(), timeInterval);

//        // 如果用户暂停了视频，允许观看进度不变
//        if (Objects.equals(watchStingTime, courseStudyhistory.getLastHeartbeatProgress())) {
//            return true;
//        }

//        // 计算合理的观看时间范围
//        int expectedWatchTime = courseStudyhistory.getLastHeartbeatProgress() + (int) (timeInterval / 1000);

//        // 验证观看速度是否合理
//        log.info("当前观看时间:watchStingTime:{}, 上次心跳时间:lastHeartbeatProgress:{}, 本次心跳时间:expectedWatchTime:{}", watchStingTime, courseStudyhistory.getLastHeartbeatProgress(), expectedWatchTime);
//        return watchStingTime <= expectedWatchTime;
        return true;
    }

    /**
     * {
     * "code": 0,
     * "msg": "",
     * "message": "",
     * "data": {
     * "addr": "*************",
     * "country": "中国",
     * "province": "甘肃",
     * "city": "兰州",
     * "isp": "联通",
     * "latitude": "36.059299",
     * "longitude": "103.756279"
     * }
     * }
     */
    public static String getInfoNew(String ip) {
        String api = StrUtil.format("https://api.live.bilibili.com/client/v1/Ip/getInfoNew?ip={}", ip);
        return OkHttpUtil.get(api, Maps.newHashMap());
    }

    /**
     * 解析IP地址信息，格式化为易读的字符串
     */
    private String parseLocationInfo(String ipInfoStr) {
        try {
            if (StrUtil.isBlank(ipInfoStr)) {
                return "地址信息获取失败";
            }

            JSONObject jsonObject = JSON.parseObject(ipInfoStr);
            Integer code = jsonObject.getInteger("code");

            if (code == null || code != 0) {
                return "地址信息解析失败";
            }

            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                return "地址数据为空";
            }

            String country = data.getString("country");
            String province = data.getString("province");
            String city = data.getString("city");
            String isp = data.getString("isp");

            StringBuilder locationBuilder = new StringBuilder();
            if (StrUtil.isNotBlank(country)) {
                locationBuilder.append(country);
            }
            if (StrUtil.isNotBlank(province)) {
                locationBuilder.append("-").append(province);
            }
            if (StrUtil.isNotBlank(city)) {
                locationBuilder.append("-").append(city);
            }
            if (StrUtil.isNotBlank(isp)) {
                locationBuilder.append("(").append(isp).append(")");
            }

            return locationBuilder.length() > 0 ? locationBuilder.toString() : "地址信息不完整";
        } catch (Exception e) {
            log.warn("解析IP地址信息失败: {}", e.getMessage());
            return "地址信息解析异常";
        }
    }

    /**
     * 验证学习时间的合理性
     *
     * @param tcourse   当前学习记录
     * @param videoTime 视频总时长（秒）
     * @param mobile    用户手机号
     * @return true 如果学习时间合理，false 否则
     */
    private boolean validateStudyTime(CourseStudyhistory tcourse, int videoTime, String mobile) {
        Integer userId = tcourse.getUserId();

        // 查询用户其他课程的学习记录，找到最近的一次心跳时间
        CourseStudyhistory latestHeartbeatRecord = courseStudyhistoryService.getOne(
                new LambdaQueryWrapper<CourseStudyhistory>()
                        .eq(CourseStudyhistory::getUserId, userId)
                        .ne(CourseStudyhistory::getId, tcourse.getId()) // 排除当前课时
                        .isNotNull(CourseStudyhistory::getLastHeartbeatTime)
                        .orderByDesc(CourseStudyhistory::getLastHeartbeatTime)
                        .last("limit 1")
        );

        if (latestHeartbeatRecord == null || latestHeartbeatRecord.getLastHeartbeatTime() == null) {
            log.info("用户{},课时id{}.未找到其他课时的心跳记录，允许完成（可能是首次学习）", mobile, tcourse.getId());
            return true; // 没有其他课时的心跳记录，允许完成
        }

        Date lastHeartbeatTime = latestHeartbeatRecord.getLastHeartbeatTime();
        Date currentTime = new Date();
        long timeSinceLastHeartbeat = (currentTime.getTime() - lastHeartbeatTime.getTime()) / 1000; // 距离最后一次心跳的时间（秒）

        // 要求距离其他课时最后一次心跳的时间至少达到当前视频时长的80%，防止快速切换课程刷课
        long minRequiredTime = (long) (videoTime * 0.8);

        if (timeSinceLastHeartbeat < minRequiredTime) {
            String ip = WebUtil.getIP();
            String ipInfoStr = getInfoNew(ip);
            String locationInfo = parseLocationInfo(ipInfoStr);
            log.info("用户{},课时id{}.距离其他课时最后心跳时间[{}]秒不足,要求最少[{}]秒,视频总时长[{}]秒,最后心跳课时id[{}],IP[{}]地址信息[{}],学习时间校验失败",
                    mobile, tcourse.getId(), timeSinceLastHeartbeat, minRequiredTime, videoTime, latestHeartbeatRecord.getId(), ip, locationInfo);
            return false;
        }

        // 确保当前课程也有心跳记录
        if (tcourse.getLastHeartbeatTime() == null) {
            log.info("用户{},课时id{}.当前课程无心跳记录", mobile, tcourse.getId());
            return false;
        }

        log.info("用户{},课时id{}.学习时间校验通过，距离其他课时最后心跳时间[{}]秒，视频时长[{}]秒，要求最少[{}]秒，心跳次数[{}]",
                mobile, tcourse.getId(), timeSinceLastHeartbeat, videoTime, minRequiredTime, tcourse.getHeartbeatCount());
        return true;
    }

}
