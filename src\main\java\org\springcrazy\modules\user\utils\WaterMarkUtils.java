package org.springcrazy.modules.user.utils;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.List;
import java.util.*;

@Slf4j
public class WaterMarkUtils {


    /**
     * 编辑图片,往指定位置添加文字
     *
     * @param srcImgPath    :源图片路径
     * @param targetImgPath :保存图片路径
     * @param list          :文字集合
     */
    public static void writeImage(String srcImgPath, String targetImgPath, List<ImageDTO> list) {
        FileOutputStream outImgStream = null;
        try {
            //读取原图片信息
            File srcImgFile = new File(srcImgPath);//得到文件
            Image srcImg = ImageIO.read(srcImgFile);//文件转化为图片
            int srcImgWidth = srcImg.getWidth(null);//获取图片的宽
            int srcImgHeight = srcImg.getHeight(null);//获取图片的高

            //添加文字:
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = bufImg.createGraphics();
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            for (ImageDTO imgDTO : list) {
                g.setColor(imgDTO.getColor());                                  //根据图片的背景设置水印颜色
                g.setFont(imgDTO.getFont());//设置字体
                // 添加二维码图片
                if (Objects.nonNull(imgDTO.getImg())) {
                    g.drawImage(imgDTO.getImg(), imgDTO.getX(), imgDTO.getY(), null);
                }
                if (Objects.nonNull(imgDTO.getYzImage())) {
                    g.drawImage(imgDTO.getYzImage(), imgDTO.getX(), imgDTO.getY(), null);
                }

                g.drawString(imgDTO.getText(), imgDTO.getX(), imgDTO.getY());   //画出水印
            }
            g.dispose();

            // 输出图片
            outImgStream = new FileOutputStream(targetImgPath);
            ImageIO.write(bufImg, "jpg", outImgStream);
        } catch (Exception e) {
            log.error("==== 系统异常::{} ====", e);
        } finally {
            try {
                if (null != outImgStream) {
                    outImgStream.flush();
                    outImgStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 创建ImageDTO, 每一个对象,代表在该图片中要插入的一段文字内容:
     *
     * @param text  : 文本内容;
     * @param color : 字体颜色(前三位)和透明度(第4位,值越小,越透明);
     * @param font  : 字体的样式和字体大小;
     * @param x     : 当前字体在该图片位置的横坐标;
     * @param y     : 当前字体在该图片位置的纵坐标;
     * @return
     */
    private static ImageDTO createImageDTO(String text, Color color, Font font, int x, int y, Image img, Image yzImage) {
        ImageDTO imageDTO = new ImageDTO();
        imageDTO.setText(text);
        imageDTO.setColor(color);
        imageDTO.setFont(font);
        imageDTO.setX(x);
        imageDTO.setY(y);
        imageDTO.setImg(img);
        imageDTO.setYzImage(yzImage);
        return imageDTO;
    }

//    public static void main(String[] args) {
//        String cerificate = createCerificate("E://certificate/", "张三", "2023年5月2日", "https://www.baidu.com");
//        System.out.println(cerificate);
//    }

    public static String createCerificate(String tarImgPath, String name, String examTime, String qrContent) {
        // 目标图片的地址
        String filepath = tarImgPath + UUID.randomUUID() + ".jpg";
        // 获取源图片模板
        File file = new File("/www/wwwroot/java_software/image/certificate.jpg");
        String srcImgPath = file.getPath();    //源图片地址
        // 获取印章图片
        File yzImg = new File("/www/wwwroot/java_software/image/yz.png");    //源图片地址
        Image yzImage = null;
        try {
            InputStream in = new FileInputStream(yzImg);
            yzImage = ImageIO.read(in);
        } catch (IOException e) {
            e.printStackTrace();
        }

        //==============================================================================
        String str = name + "同志于" + examTime + "参加由甘肃省住房和城乡建设厅组织的“全省房屋市政工程质量安全监督人员培训班”，修完全部课程，经考核合格，特发此证。";
        String str1 = str.substring(0, 44);
        String str2 = str.substring(44);
        //获取数据集合；
        ArrayList<ImageDTO> list = new ArrayList<>();
        list.add(createImageDTO(str1, Color.black, new Font("黑体", Font.BOLD, 22), 210, 443, null, null));
        list.add(createImageDTO(str2, Color.black, new Font("黑体", Font.BOLD, 22), 158, 487, null, null));
        list.add(createImageDTO("甘肃省房屋市政工程质量安全监督人员培训网", Color.black, new Font("黑体", Font.TRUETYPE_FONT, 18), 510, 682, null, null));
        list.add(createImageDTO(DateUtils.parseDate(new Date()), Color.black, new Font("黑体", Font.TRUETYPE_FONT, 18), 510, 721, null, null));
        try {
            list.add(createImageDTO("", null, null, 230, 625, QrCodeUtils.createImage(qrContent, "", false), null));
            list.add(createImageDTO("", null, null, 700, 652, null, yzImage));
        } catch (Exception e) {
            e.printStackTrace();
        }

        //操作图片:
        WaterMarkUtils.writeImage(srcImgPath, filepath, list);
        return filepath.substring(filepath.lastIndexOf("uploadFile") - 1);
    }

    /**
     * main方法:
     *
     * @param args
     */
//    public static void main(String[] args) {
//
//        // 获取源图片
//        ClassPathResource classPathResource = new ClassPathResource("/image/certificate.jpg");
//        String srcImgPath = null;    //源图片地址
//        try {
//            srcImgPath = classPathResource.getFile().getPath();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        System.out.println(srcImgPath);
//        String tarImgPath = "E://file.jpg";   //目标图片的地址
//        //==============================================================================
//        String text = "杜晨星于2023年4月12日参加由甘肃省住房和城乡建设厅组织的“全省房屋市政工程质量安全监督人员培训班”，修完全部课程，经考核合格，特发此证。";
//        //获取数据集合；
//        ArrayList<ImageDTO> list = new ArrayList<>();
//        list.add(createImageDTO("王兴军同志于2023年1月13日至1月18日参加由甘肃省住房和城乡建设厅组织的“全省房屋", Color.black, new Font("黑体", Font.BOLD, 22), 210, 443, null));
//        list.add(createImageDTO("市政工程质量安全监督人员培训班”，修完全部课程，经考核合格，特发此证。", Color.black, new Font("黑体", Font.BOLD, 22), 158, 487, null));
//        try {
//            list.add(createImageDTO("", null, null, 180, 550, QrCodeUtils.createImage("https://www.baidu.com", "", false)));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        //操作图片:
//        WaterMarkUtils.writeImage(srcImgPath, tarImgPath, list);
//
//    }

}


/**
 * 存放文本内容的类
 */
@Setter
@Getter
class ImageDTO {
    //文字内容
    private String text;
    //字体颜色和透明度
    private Color color;
    //字体和大小
    private Font font;
    //所在图片的x坐标
    private int x;
    //所在图片的y坐标
    private int y;

    private Image img;
    private Image yzImage;

}

