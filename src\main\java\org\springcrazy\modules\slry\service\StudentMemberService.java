package org.springcrazy.modules.slry.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.core.log.exception.ServiceException;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.slry.entity.StudentWhitelist;
import org.springcrazy.modules.slry.mapper.StudentMemberMapper;
import org.springcrazy.modules.slry.entity.StudentMember;
import org.springcrazy.modules.system.excel.StudentMemberExcel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class StudentMemberService extends ServiceImpl<StudentMemberMapper, StudentMember> {

    @Autowired
    private StudentMemberMapper studentMemberMapper;
    @Autowired
    private StudentWhitelistService studentWhitelistService;

    public List<StudentMemberExcel> exportMember(Wrapper<StudentMember> queryWrapper) {
        List<StudentMemberExcel> list = studentMemberMapper.exportMember(queryWrapper);
        return list;
    }

    public void importStudentMember(List<StudentMemberExcel> data) {
        if (data.size() == 0) {
            throw new ServiceException("文件无数据加载！！！");
        }

        int i = 1; //行数
        String errMsg = "";//错误信息
        List<StudentMember> studentMemberList = new ArrayList<>(); //导入员工账号数据集合
        //检测是否为纯数字
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        //检测字符串中是否包含特殊符号
        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);

        for (StudentMemberExcel studentMemberExcel : data) {
            //集合添值
            StudentMember studentMemberNow = new StudentMember();
            studentMemberList.add(i - 1, studentMemberNow);
            //获取Excel中的数据
            StudentMember studentMember = Objects.requireNonNull(BeanUtil.copy(studentMemberExcel, StudentMember.class));
            if (Func.isNull(studentMember.getRealName()) || Func.isNull(studentMember.getIdCardNo()) || Func.isNull(studentMember.getMobile())
                    || Func.isNull(studentMember.getQytyshxydm()) || Func.isNull(studentMember.getZxtype()) || Func.isNull(studentMemberExcel.getPaymentType())) {
                errMsg += "第" + i + "行，【姓名、身份证号、证件类别、手机号码、企业统一社会信用代码、（是/否）线上支付】是必填的，不能为空；<br />";
                continue;
            }

            //姓名
            if (pattern.matcher(studentMemberExcel.getRealName()).matches()) {
                errMsg += "第" + i + "行，姓名不能为纯数字；<br />";
            } else if (p.matcher(studentMemberExcel.getRealName()).find()) {
                errMsg += "第" + i + "行，姓名不可以包含符号；<br />";
            } else {
                studentMemberList.get(i - 1).setRealName(StrUtil.trim(studentMember.getRealName()));
            }

            //身份证号
            studentMemberList.get(i - 1).setIdCardNo(StrUtil.trim(studentMember.getIdCardNo().toUpperCase()));

            //证件类别
            //此处需要判断证件类别转大写后只能是ABC三者之一
            if (!"ABC".contains(StrUtil.trim(studentMember.getZxtype().toUpperCase()))) {
                errMsg += "第" + i + "行，证件类别不正确,请填写ABC其中一个；<br />";
            } else {
                studentMemberList.get(i - 1).setZxtype(StrUtil.trim(studentMember.getZxtype()));
            }

            //企业统一社会信用代码
            studentMemberList.get(i - 1).setQytyshxydm(StrUtil.trim(studentMember.getQytyshxydm()));

            //是否线上支付  1是  2否
            if (!"是否".contains(StrUtil.trim(studentMemberExcel.getPaymentType()))) {
                errMsg += "第" + i + "行，【（是/否）线上支付】不正确，请填写是或否；<br />";
            } else {
                studentMemberList.get(i - 1).setPaymentType("是".equals(StrUtil.trim(studentMemberExcel.getPaymentType())) ? 1 : 2);
            }

            //视频/照片  1视频  2照片
            if (Func.isNotEmpty(studentMemberExcel.getMediaType())) {
                if (!"视频照片".contains(StrUtil.trim(studentMemberExcel.getMediaType()))) {
                    errMsg += "第" + i + "行，【视频/照片】不正确，请填写视频或照片；<br />";
                } else {
                    studentMemberList.get(i - 1).setMediaType("视频".equals(StrUtil.trim(studentMemberExcel.getMediaType())) ? 1 : 2);
                }
            } else {
                //默认为视频
                studentMemberList.get(i - 1).setMediaType(1);
            }

            //手机号码
            if (!pattern.matcher(StrUtil.trim(studentMember.getMobile())).matches()) {
                errMsg += "第" + i + "行，手机号不正确;<br />";
            } else {
                studentMemberList.get(i - 1).setMobile(StrUtil.trim(studentMember.getMobile()));
            }

            //密码
            if (Func.isNotEmpty(studentMember.getPassword())) {
                studentMemberList.get(i - 1).setPassword(StrUtil.trim(studentMember.getPassword()));
            }

            //企业名称
            if (Func.isNotEmpty(studentMember.getQymc())) {
                studentMemberList.get(i - 1).setQymc(StrUtil.trim(studentMember.getQymc()));
            }

            //证书有效期
            if (Func.isNotEmpty(studentMember.getZsyxq())) {
                studentMemberList.get(i - 1).setZsyxq(StrUtil.trim(studentMember.getZsyxq()));
            }

            //代理商名称
            if (Func.isNotEmpty(studentMember.getAgentName())) {
                studentMemberList.get(i - 1).setAgentName(StrUtil.trim(studentMember.getAgentName()));
            }

            //根据身份证号、证书类型、企业统一信用代码查询证书信息
            Map<String, Object> mapZs = studentMemberMapper.getZhengshuByZxtypeAndIdCardNoAndZsyxq(studentMemberList.get(i - 1).getIdCardNo(), studentMemberList.get(i - 1).getZxtype(), studentMemberList.get(i - 1).getQytyshxydm());
            if (mapZs == null) {
                errMsg += "第" + i + "行，【姓名：" + studentMember.getRealName() + " 身份证号: " + studentMember.getIdCardNo() + "】的证书信息不存在，请确认学员是否已注册系统;<br />";
                break;
            }
            // 根据身份证号和证书编号查询员工账号信息是否存在
            if (studentMemberMapper.countByidCardNoAndNewzsbh(studentMemberList.get(i - 1).getIdCardNo(), (String) mapZs.get("NEWZSBH")) != 0) {
                errMsg += "第" + i + "行，【姓名：" + studentMember.getRealName() + " 身份证号: " + studentMember.getIdCardNo() + "】信息已存在,不能重复导入;<br />";
                break;
            }

            //证书编号
            studentMemberList.get(i - 1).setNewzsbh((String) mapZs.get("NEWZSBH"));
            //发证日期
            studentMemberList.get(i - 1).setFzrq((String) mapZs.get("FZRQ"));
            //证书有效期
            studentMemberList.get(i - 1).setZsyxq((String) mapZs.get("ZSYXQ"));
            //人员类别
            studentMemberList.get(i - 1).setRylb((String) mapZs.get("RYLB"));
            //企业名称
            studentMemberList.get(i - 1).setQymc((String) mapZs.get("QYMC"));
            //姓名(提高准确性取证书数据)
            studentMemberList.get(i - 1).setRealName((String) mapZs.get("RYXM"));

            if (i == data.size()) {
                break;
            } else {
                i++;
            }
        }

        //数据入库
        if (Func.equals(errMsg, "")) {
            //导入数据需通过企业统一社会信用代码分组，获取企业线上支付订单数
            List<StudentMember> newMemberList = new ArrayList<>();
            Map<String, List<StudentMember>> studentMemberByQy = studentMemberList.stream().collect(Collectors.groupingBy(StudentMember::getQytyshxydm));
            for (String key : studentMemberByQy.keySet()) {
                //判断导入的数据中是否存在"（是/否）线上支付"为是的数据
                List<StudentMember> tempList = studentMemberByQy.get(key).stream().filter(StudentMember -> StudentMember.getPaymentType() == 1).collect(Collectors.toList());
                if (tempList != null && tempList.size() > 0) {
                    //用户录入了线上支付的数据，需要查询中台订单笔数和企业支付类型为线上的数据做对比
                    String requestUrl = StrUtil.format("{}{}", "https://www.gsjtsz.cn/api/get900OrderCount?userName=", key); //"91620103MA72YXJG9L"
                    String returnMsg = "";
                    try {
                        returnMsg = HttpUtil.get(requestUrl);
                    } catch (Exception e) {
                        throw new ServiceException("建数云建筑施工企业综合服务平台当前遇到接口异常，请稍候片刻后再次尝试导入！！！");
                    }
                    //正常情况下接口一定会返回数据，招聘会员（900元）的订单数量
                    if (Func.isNotEmpty(returnMsg)) {
                        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(returnMsg);
                        //企业从未购买
                        if ("200".equals(jsonObject.getString("code")) && Integer.parseInt(jsonObject.getString("data")) > 0) {
                            //会员总数
                            int stCountSum = Integer.parseInt(jsonObject.getString("data"));
                            //查询当前企业已开通线上会员总数
                            int memberCount = studentMemberMapper.countOnlineByQYTYSHXYDM(key);
                            // 判断需要添加的线上支付人员数量是否超过剩余名额
                            if (tempList.size() <= stCountSum - memberCount) {
                                newMemberList.addAll(studentMemberByQy.get(key));
                            } else {
                                throw new ServiceException("【" + studentMemberByQy.get(key).get(0).getQymc() + "】目前线上支付名额仅剩" + (stCountSum - memberCount) + "人,而需要导入" + tempList.size() + "人,请确认修改【（是/否）线上支付】为否后重新导入！");
                            }
                        } else {
                            //不存在“招聘会员（900元）的订单数量”
                            throw new ServiceException("【" + studentMemberByQy.get(key).get(0).getQymc() + "】由于目前没有线上支付订单，请您将相关数据中的【（是/否）线上支付】选项修改为“否”，然后重新进行数据导入。");
                        }
                    } else {
                        throw new ServiceException("建数云建筑施工企业综合服务平台当前遇到接口异常，请稍候片刻后再次尝试导入！");
                    }
                } else {
                    //以用户导入时填写的为准
                    newMemberList.addAll(studentMemberByQy.get(key));
                }
            }
            List<StudentWhitelist> whitelistList = new ArrayList<>();
            for(StudentMember member : newMemberList){
                // 校验身份证号码是否存在
                int count = studentWhitelistService.count(new LambdaQueryWrapper<StudentWhitelist>().eq(StudentWhitelist::getIdCard, member.getIdCardNo()));
                if (count ==  0) {
                    StudentWhitelist whitelist = new StudentWhitelist();
                    whitelist.setIdCard(member.getIdCardNo());
                    whitelist.setRealName(member.getRealName());
                    whitelist.setMobile(member.getMobile());
                    whitelist.setCompanyName(member.getQymc());
                    whitelist.setCreateUser(SecureUtil.getUserId());
                    whitelistList.add(whitelist);
                }
            }
            //数据入库
            this.saveBatch(newMemberList);
            studentWhitelistService.saveBatch(whitelistList);
        } else {
            throw new ServiceException(errMsg);
        }
    }
}
