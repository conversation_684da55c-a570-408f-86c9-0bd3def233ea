<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.user.mapper.UserLoginLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userLoginLogResultMap" type="org.springcrazy.modules.user.entity.UserLoginLog">
        <id column="id" property="id"/>
        <result column="login_time" property="loginTime"/>
        <result column="ip" property="ip"/>
        <result column="user_id" property="userId"/>
        <result column="os_name" property="osName"/>
        <result column="user_agent" property="userAgent"/>
    </resultMap>


    <select id="selectUserLoginLogPage" resultMap="userLoginLogResultMap">
        select * from edu_user_login_log where is_deleted = 0
    </select>

</mapper>
