# 三类人员学习系统心跳校验安全优化

## 问题描述

在原有的 `updateCourseStudyhistory` 方法中，心跳校验只检查了心跳次数，存在安全漏洞：

1. 用户可以快速调用 `/heartbeat/submit` 接口来积累心跳次数
2. 然后一次性调用 `updateCourseStudyhistory` 完成所有课程
3. 更严重的是，用户可以快速切换课程：先在课程A上刷心跳，然后立即切换到课程B完成学习
4. 绕过了每个课程的实际学习时间要求

### 具体攻击场景

**场景1：快速切换课程刷课**
1. 10:00 - 用户开始学习课程A（30分钟视频）
2. 10:05 - 用户通过技术手段快速提交课程A的心跳（积累足够次数）
3. 10:06 - 用户切换到课程B（30分钟视频）并立即尝试完成
4. 原系统只检查课程B的心跳次数，可能被绕过

**场景2：并发学习多课程**
1. 用户同时打开多个课程页面
2. 在一个课程上正常学习，在其他课程上快速刷心跳
3. 然后批量完成所有课程

## 优化方案

### 1. 增加学习时间间隔校验

在原有心跳次数校验的基础上，新增了学习时间校验：

```java
// 原有校验：只检查心跳次数
if (heartbeatCount < expectedHeartbeatCount - countMargin) {
    return FaceResult.error(500, "error 02");
}

// 新增校验：检查实际学习时间
if (!validateStudyTime(tcourse, videoTime, mobile)) {
    return FaceResult.error(500, "error 03");
}
```

### 2. 学习时间间隔计算

通过查询用户所有课程的最近心跳时间来防止快速切换课程：

```java
// 查询用户所有课程的学习记录，找到最近的一次心跳时间
CourseStudyhistory latestHeartbeatRecord = courseStudyhistoryService.getOne(
    new LambdaQueryWrapper<CourseStudyhistory>()
        .eq(CourseStudyhistory::getUserId, userId)
        .isNotNull(CourseStudyhistory::getLastHeartbeatTime)
        .orderByDesc(CourseStudyhistory::getLastHeartbeatTime)
        .last("limit 1")
);

// 计算距离最后一次心跳的时间间隔
Date lastHeartbeatTime = latestHeartbeatRecord.getLastHeartbeatTime();
Date currentTime = new Date();
long timeSinceLastHeartbeat = (currentTime.getTime() - lastHeartbeatTime.getTime()) / 1000;
```

### 3. 学习时间阈值设置

要求距离最后一次心跳至少达到当前视频时长的80%：

```java
// 要求距离最后一次心跳的时间至少达到当前视频时长的80%，防止快速切换课程刷课
long minRequiredTime = (long) (videoTime * 0.8);

if (timeSinceLastHeartbeat < minRequiredTime) {
    // 记录详细日志并拒绝完成
    log.info("用户{},课时id{}.距离最后心跳时间[{}]秒不足,要求最少[{}]秒,视频总时长[{}]秒,最后心跳课时id[{}],IP[{}]地址信息[{}],学习时间校验失败", 
            mobile, tcourse.getId(), timeSinceLastHeartbeat, minRequiredTime, videoTime, latestHeartbeatRecord.getId(), ip, locationInfo);
    return false;
}
```

## 安全特性

### 1. 防止快速切换课程刷课
- 检查用户所有课程的最近心跳时间，防止快速切换课程
- 要求距离最后一次心跳至少80%的视频时长，确保用户真实学习了当前课程

### 2. 详细的审计日志
- 记录用户IP地址和地理位置信息
- 记录实际学习时间、要求时间、心跳次数等关键指标
- 便于后续安全审计和异常分析

### 3. 多层校验机制
- 心跳次数校验（防止心跳次数不足）
- 心跳间隔校验（防止心跳提交过快）
- 学习时间校验（防止总体学习时间不足）

## 效果验证

### 正常学习场景
```
用户1234,课时id567.学习时间校验通过，距离最后心跳时间[1800]秒，视频时长[2000]秒，要求最少[1600]秒，心跳次数[30]
```

### 异常快速切换课程场景
```
用户1234,课时id567.距离最后心跳时间[300]秒不足,要求最少[1600]秒,视频总时长[2000]秒,最后心跳课时id[565],IP[*************]地址信息[中国-北京-北京(联通)],学习时间校验失败
```

## 配置说明

### 时间阈值调整
目前设置的80%阈值可以根据业务需要调整：
- 提高阈值（如90%）：更严格的学习时间要求
- 降低阈值（如70%）：适度放宽，考虑用户可能的暂停等情况

### 错误码说明
- `error 01`: 心跳次数不足（少于2次）
- `error 02`: 心跳次数不符合预期（基于视频时长计算）
- `error 03`: 学习时间校验失败（实际学习时间不足）

## 部署建议

1. 先在测试环境验证功能正常
2. 观察日志输出，确认校验逻辑符合预期
3. 根据实际使用情况调整时间阈值
4. 监控异常日志，及时发现潜在的刷课行为 