package org.springcrazy.modules.user.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.iai.v20180301.IaiClient;
import com.tencentcloudapi.iai.v20180301.models.CompareFaceRequest;
import com.tencentcloudapi.iai.v20180301.models.CompareFaceResponse;
import com.xkcoding.http.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.modules.exam.vo.FaceResult;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import static org.springcrazy.common.tool.Base64Util.netImageToBase64;

/**
 * <AUTHOR>
 * <p>调用腾讯接口</p>
 **/
@Component
@Data
@Slf4j
public class FaceContrastServer {

    @Value("${tencentcloudapi.secretId}")
    private String secretId;
    @Value("${tencentcloudapi.secretKey}")
    private String secretKey;
    @Value("${tencentcloudapi.endpoint}")
    private String endpoint;
    @Value("${tencentcloudapi.region}")
    private String region;
    @Value("${tencentcloudapi.score}")
    private String tencentcloudapi_score;
    @Value("${tencentcloudapi.facemodelversion}")
    private String tencentcloudapi_facemodelversion;

    public FaceResult faceContrast(String imageA, String imageB, String urlA, String urlB){
        FaceResult faceResult = new FaceResult();
        try{
            Credential cred = new Credential(secretId, secretKey);
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint(endpoint);
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            IaiClient client = new IaiClient(cred, region, clientProfile);
            CompareFaceRequest req = new CompareFaceRequest();
            req.setImageA(netImageToBase64(urlA));
            req.setImageB(imageB.substring(imageB.indexOf(",") + 1));
            req.setFaceModelVersion(tencentcloudapi_facemodelversion);
            CompareFaceResponse resp = client.CompareFace(req);
            if(resp.getScore() >= Float.parseFloat(tencentcloudapi_score)){
                faceResult.setCode(FaceResult.SUCCESS_CODE);
            }else{
                faceResult.setCode(FaceResult.FACE_ERROR);
            }
        } catch (TencentCloudSDKException e) {
            faceResult.setCode(FaceResult.FACE_ERROR);
            faceResult.setMsg(e.getMessage());
        }
        return faceResult;
    }
}
