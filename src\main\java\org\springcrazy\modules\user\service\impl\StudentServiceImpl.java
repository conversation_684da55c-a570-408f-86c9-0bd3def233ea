
package org.springcrazy.modules.user.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.internet.WxMaInternetResponse;
import cn.binarywang.wx.miniapp.bean.internet.WxMaInternetUserKeyInfo;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.aliyun.ocr_api20210707.Client;
import com.aliyun.ocr_api20210707.models.RecognizeIdcardRequest;
import com.aliyun.ocr_api20210707.models.RecognizeIdcardResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Lists;
import com.xkcoding.http.util.StringUtil;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springcrazy.common.tool.HttpUtils;
import org.springcrazy.common.tool.MD5Utils;
import org.springcrazy.common.tool.OcrDataUtil;
import org.springcrazy.common.tool.OperateImage;
import org.springcrazy.core.log.exception.ServiceException;
import org.springcrazy.core.mp.support.Condition;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.CrazyUser;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.*;
import org.springcrazy.modules.auth.utils.TokenUtil;
import org.springcrazy.modules.cms.entity.WebsiteProfile;
import org.springcrazy.modules.edu.entity.Course;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.edu.service.ICourseService;
import org.springcrazy.modules.edu.service.ITrxorderDetailService;
import org.springcrazy.modules.exam.vo.FaceResult;
import org.springcrazy.modules.front.vo.VefVO;
import org.springcrazy.modules.slry.entity.CompanyWhitelist;
import org.springcrazy.modules.slry.entity.StudentWhitelist;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.service.CompanyWhitelistService;
import org.springcrazy.modules.slry.service.SlryService;
import org.springcrazy.modules.slry.service.StudentWhitelistService;
import org.springcrazy.modules.slry.service.StudentZhengshuService;
import org.springcrazy.modules.system.entity.User;
import org.springcrazy.modules.system.entity.UserInfo;
import org.springcrazy.modules.system.excel.ExportStudentExcel;
import org.springcrazy.modules.system.excel.StudentExcel;
import org.springcrazy.modules.system.service.IDictService;
import org.springcrazy.modules.system.service.IUserService;
import org.springcrazy.modules.user.dto.FaceCardDTO;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.entity.UserAccount;
import org.springcrazy.modules.user.entity.UserLoginLog;
import org.springcrazy.modules.user.mapper.StudentMapper;
import org.springcrazy.modules.user.service.IStudentService;
import org.springcrazy.modules.user.service.IUserAccountService;
import org.springcrazy.modules.user.service.IUserLoginLogService;
import org.springcrazy.modules.user.utils.Base64StrToImage;
import org.springcrazy.modules.user.vo.StudentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 学员表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@Service
@Slf4j
public class StudentServiceImpl extends ServiceImpl<StudentMapper, Student> implements IStudentService {

    @Autowired
    private IUserAccountService userAccountService;
    @Autowired
    private IUserLoginLogService userLoginLogService;
    @Autowired
    private ICourseService courseService;
    @Autowired
    private IUserService userService;
    @Autowired
    FaceContrastServer faceContrastServer;
    @Autowired
    AliFaceRecognitionUtil aliFaceRecognitionUtil;
    @Autowired
    private Client client;
    @Autowired
    private SlryService slryService;
    @Autowired
    private IDictService dictService;
    @Autowired
    private StudentZhengshuService studentZhengshuService;
    @Autowired
    private CompanyWhitelistService companyWhitelistService;
    @Autowired
    private StudentWhitelistService studentWhitelistService;
    @Autowired
    private ITrxorderDetailService trxorderDetailService;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private Base64StrToImage base64StrToImage;
    @Resource
    private WxMaService wxMaService;



    @Value("${crazy.prop.remote-path}")
    private String headPath;

    @Value("${crazy.prop.upload-domain}")
    private String headUrl;

    @Value("${faceapi.type}")
    private String faceapi_type;

    @Value("${crazy.prop.remote-path}")
    private String remotePath;

    @Value("${crazy.prop.upload-domain}")
    private String uploadDomain;

    @Value("${shumai.url}")
    private String shumai_url;

    @Value("${shumai.appid}")
    private String shumai_appid;

    @Value("${shumai.appSecurity}")
    private String shumai_appSecurity;

    @Value("${shumai.successScore}")
    private double shumai_successScore;

    public static final String SLRY_COURSE = "slry_course";

    public static final Integer SLRY_COURSE_ID_A = 2259;
    public static final Integer SLRY_COURSE_ID_B = 2260;
    public static final Integer SLRY_COURSE_ID_C = 2261;
    public static final String WHITELIST_IP = "whitelist_ip";
    public static final String WHITELIST_FACE_LOGIN_REG = "whitelist_face_login_reg";


    @Override
    public IPage<StudentVO> selectStudentPage(IPage<StudentVO> page, StudentVO student) {
        return page.setRecords(baseMapper.selectStudentPage(page, student));
    }

    @Override
    public UserInfo userInfo(Integer userId) {
        UserInfo userInfo = new UserInfo();
        Student student = baseMapper.selectById(userId);
        if (Func.isNull(student)) {
            return null;
        }
        //封装 CrazyUser
        CrazyUser CrazyUser = new CrazyUser();
        CrazyUser.setUserId(student.getId());
        CrazyUser.setTenantId(null);
        CrazyUser.setAccount(student.getMobile());
        CrazyUser.setUserName(student.getRealName());
        CrazyUser.setTenantId("000000");
        CrazyUser.setMobile(student.getMobile());
        CrazyUser.setShowName(student.getShowName());
        CrazyUser.setEmail(student.getEmail());
        CrazyUser.setRealName(student.getRealName());
        userInfo.setUser(CrazyUser);
        //设置用户角色
        List<String> roleAlias = Lists.newArrayList();
        roleAlias.add("student");
        userInfo.setRoles(roleAlias);
        return userInfo;
    }

    @Override
    public UserInfo userInfos(Integer userId) {
        UserInfo userInfo = new UserInfo();
        Student student = baseMapper.userInfos(userId);
        if (Func.isNull(student)) {
            return null;
        }
        //封装 CrazyUser
        CrazyUser CrazyUser = new CrazyUser();
        CrazyUser.setUserId(student.getId());
        CrazyUser.setTenantId(null);
        CrazyUser.setAccount(student.getMobile());
        if (student.getShowName() != null && !("").equals(student.getShowName())) {
            CrazyUser.setUserName(student.getShowName());
        } else {
            if (student.getMobile() != null && !("").equals(student.getMobile())) {
                CrazyUser.setUserName(student.getMobile());
            } else {
                if (student.getEmail() != null && !("").equals(student.getEmail())) {
                    CrazyUser.setUserName(student.getEmail());
                }
            }
        }
        CrazyUser.setTenantId("000000");
        CrazyUser.setMobile(student.getMobile());
        CrazyUser.setShowName(student.getShowName());
        CrazyUser.setEmail(student.getEmail());
        CrazyUser.setRealName(student.getRealName());
        userInfo.setUser(CrazyUser);
        //设置用户角色
        List<String> roleAlias = Lists.newArrayList();
        roleAlias.add("student");
        userInfo.setRoles(roleAlias);
        return userInfo;
    }

    @Override
    public Student selectStudentLogin(Student student) {
        return baseMapper.selectStudentLogin(student);
    }

    @Override
    public UserInfo userInfo(String account) {
        UserInfo userInfo = new UserInfo();
        Student student = new Student();
        student.setLoginAccount(account);
        student = baseMapper.selectStudentLogin(student);
        if (Func.isNull(student)) {
            return null;
        }
        //封装 CrazyUser
        CrazyUser CrazyUser = new CrazyUser();
        CrazyUser.setUserId(student.getId());
        CrazyUser.setTenantId(null);
        CrazyUser.setAccount(account);
        CrazyUser.setUserName(student.getRealName());
        CrazyUser.setMobile(student.getMobile());
        CrazyUser.setShowName(student.getShowName());
        CrazyUser.setEmail(student.getEmail());
        CrazyUser.setRealName(student.getRealName());
        CrazyUser.setTenantId("000000");
        userInfo.setUser(CrazyUser);
        //设置用户角色
        List<String> roleAlias = Lists.newArrayList();
        roleAlias.add("student");
        userInfo.setRoles(roleAlias);
        return userInfo;
    }

    @Override
    public UserInfo userInfo(String account, String password) {
        UserInfo userInfo = new UserInfo();
        Student student = new Student();
        student.setLoginAccount(account);
        student.setPassword(password);
        student = baseMapper.selectStudentLogin(student);
        if (Func.isNull(student)) {
            return null;
        }
        //账号冻结判断
        if (student.getIsAvalible() == 1) {
            throw new ServiceException(Convert.toStr(student.getIsAvalibleMsg(), "该账户已被冻结，请联系管理员"));
        }
        //封装 CrazyUser
        CrazyUser CrazyUser = new CrazyUser();
        CrazyUser.setUserId(student.getId());
        CrazyUser.setTenantId(null);
        CrazyUser.setAccount(account);
        CrazyUser.setUserName(student.getRealName());
        CrazyUser.setMobile(student.getMobile());
        CrazyUser.setShowName(student.getShowName());
        CrazyUser.setEmail(student.getEmail());
        CrazyUser.setRealName(student.getRealName());
        CrazyUser.setTenantId("000000");
        //学生用户登录权限   1
        CrazyUser.setRoleName("student");
        CrazyUser.setRoleId("-1");
        userInfo.setUser(CrazyUser);
        //设置用户角色
        List<String> roleAlias = Lists.newArrayList();
        roleAlias.add("student");
        userInfo.setRoles(roleAlias);
        //记录登录记录
        UserLoginLog userLoginLog = new UserLoginLog();
        UserAgent userAgent = UserAgent.parseUserAgentString(WebUtil.getRequest().getHeader("User-Agent"));
        userLoginLog.setOsName(userAgent.getBrowser().getName());
        userLoginLog.setUserAgent(userAgent.getOperatingSystem().getName());
        userLoginLog.setIp(WebUtil.getIP());
        userLoginLog.setUserId(student.getId());
        userLoginLog.setLoginTime(DateUtil.now());
        userLoginLogService.save(userLoginLog);
        return userInfo;
    }

    @Override
    public UserInfo userInfo_face(String account, String userFaceImage, String wxCode, String wxSign, Integer wxVersion) {
        UserInfo userInfo = new UserInfo();
        Student student = new Student();
        student.setLoginAccount(account);
        student = baseMapper.selectStudentLogin(student);
        if (Func.isNull(student)) {
            return null;
        }
        //账号冻结判断
        if (student.getIsAvalible() == 1) {
            throw new ServiceException(Convert.toStr(student.getIsAvalibleMsg(), "该账户已被冻结，请联系管理员"));
        }
        if (StrUtil.isBlank(userFaceImage) || StrUtil.isBlank(wxCode) || StrUtil.isBlank(wxSign) || Func.isNull(wxVersion)) {
            throw new ServiceException("人脸识别身份验证失败，请调整坐姿,确保面部位于摄像头采集区中");
        }
        // 开始解密数据
        try {
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(wxCode);
            String openid = sessionInfo.getOpenid();
            String sessionKey = sessionInfo.getSessionKey();
            WxMaInternetResponse userEncryptKey = wxMaService.getInternetService().getUserEncryptKey(openid, sessionKey);
            List<WxMaInternetUserKeyInfo> wxKeyInfoList = userEncryptKey.getKeyInfoList();
            WxMaInternetUserKeyInfo wxMaInternetUserKeyInfo = new WxMaInternetUserKeyInfo();
            // 判断是否有版本号相同的版本号
            for (WxMaInternetUserKeyInfo keyInfo : wxKeyInfoList) {
                if (Objects.equals(keyInfo.getVersion(), wxVersion)) {
                    wxMaInternetUserKeyInfo = keyInfo;
                }
            }
            if (Func.isNull(wxMaInternetUserKeyInfo.getEncryptKey())) {
                log.info("接口:登录,微信小程序服务调用失败1,当前用户account:{},ip:{}", account, WebUtil.getIP());
                throw new ServiceException("auth 微信小程序服务调用失败，请联系管理员");
            }
            // 解密数据
            AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, wxMaInternetUserKeyInfo.getEncryptKey().getBytes(), wxMaInternetUserKeyInfo.getIv().getBytes());
            aes.decryptStr(wxSign, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            log.info("接口:登录,微信小程序服务调用失败2,当前用户account:{},ip:{}", account, WebUtil.getIP(), e);
            throw new ServiceException("auth 微信小程序服务调用失败，请联系管理员");
        }

        // 人脸活体校验
        // 没有实名认证，不允许人脸识别登录
        if (student.getIsReal() == 0) {
            throw new ServiceException("40001");
        }
        String studentImg = student.getHeadImg();
        if (student.getSyncPictureStatus() == 1 && StrUtil.isNotBlank(student.getProfilePhoto())) {
            studentImg = student.getProfilePhoto();
        }
        if (StrUtil.isBlank(studentImg)) {
            throw new ServiceException("未检测到头像，请上传头像");
        }
        FaceResult faceResult = new FaceResult();
        //判断人脸活体检测是否通过
        String detectLivingFaceRedis = student.getId() + "_DetectLivingFace_" + student.getMobile();
        Object detectLivingFace = redisUtil.get(detectLivingFaceRedis);
        if (Func.isNull(detectLivingFace)) {
            faceResult = aliFaceRecognitionUtil.detectLivingFace(userFaceImage);
            log.info("阿里云返回登录人脸活体检测报文:{}", faceResult);
            if (Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
                redisUtil.set(detectLivingFaceRedis, "Y", 5400);
                log.info("登录人脸活体检测通过,保存到Redis中,有效时间为90分钟:{}", detectLivingFaceRedis);
            } else {
                //保存人脸识别错误照片,此类文件可删除
                String filePath = base64StrToImage.savaFaceErrorImg(userFaceImage, "studyFace_login", student.getMobile());
                try {
                    Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
                } catch (IOException e) {
                    log.info("{},studyFace压缩图片失败:{}", filePath, e.getMessage());
                }
                log.info("登录人脸活体检测不通过,返回前端报文：{}", faceResult);
                throw new ServiceException("登录人脸活体检测不通过,请调整光线、角度等，保持面部没有遮挡物（口罩、眼镜等）");
            }
        }
        //人脸1:1对比
        if (faceapi_type.equals("tencent")) {
            faceResult = faceContrastServer.faceContrast(null, userFaceImage, studentImg, null);
        }
        if (faceapi_type.equals("ali")) {
            faceResult = aliFaceRecognitionUtil.faceContrast(null, userFaceImage, studentImg, null);
            log.info("阿里云返回活体人脸对比结果:{}", faceResult);
        }
        // 判断人脸1:1对比是否成功
        if (!Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
            //保存人脸识别错误照片,此类文件可删除
            String filePath = base64StrToImage.savaFaceErrorImg(userFaceImage, "studyliving_login", student.getMobile());
            try {
                Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
            } catch (IOException e) {
                log.info("{},studyliving压缩图片失败:{}", filePath, e.getMessage());
            }
            faceResult.setCode(FaceResult.FACE_ERROR);
            faceResult.setMsg("人脸对比验证失败，请调整坐姿,确保面部位于摄像头采集区中");
            throw new ServiceException("人脸对比验证失败，请调整坐姿,确保面部位于摄像头采集区中");
        }

        //封装 CrazyUser
        CrazyUser CrazyUser = new CrazyUser();
        CrazyUser.setUserId(student.getId());
        CrazyUser.setTenantId(null);
        CrazyUser.setAccount(account);
        CrazyUser.setUserName(student.getRealName());
        CrazyUser.setMobile(student.getMobile());
        CrazyUser.setShowName(student.getShowName());
        CrazyUser.setEmail(student.getEmail());
        CrazyUser.setRealName(student.getRealName());
        CrazyUser.setTenantId("000000");
        //学生用户登录权限   1
        CrazyUser.setRoleName("student");
        CrazyUser.setRoleId("-1");
        userInfo.setUser(CrazyUser);
        //设置用户角色
        List<String> roleAlias = Lists.newArrayList();
        roleAlias.add("student");
        userInfo.setRoles(roleAlias);
        //记录登录记录
        UserLoginLog userLoginLog = new UserLoginLog();
        UserAgent userAgent = UserAgent.parseUserAgentString(WebUtil.getRequest().getHeader("User-Agent"));
        userLoginLog.setOsName(userAgent.getBrowser().getName());
        userLoginLog.setUserAgent(userAgent.getOperatingSystem().getName());
        userLoginLog.setIp(WebUtil.getIP());
        userLoginLog.setUserId(student.getId());
        userLoginLog.setLoginTime(DateUtil.now());
        userLoginLogService.save(userLoginLog);
        return userInfo;
    }


    @Override
    public void importUserAgent(List<StudentExcel> data) {
        if (data.size() == 0) {
            throw new ServiceException("文件无数据加载！！！");
        }

        int i = 1; //行数
        String errMsg = "";//错误信息
        List<Student> studentList = new ArrayList<>(); //导入用户数据集合
        //检测是否为纯数字
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        //检测手机号
        String pMobile = "^((13[0-9])|(14[5|7])|(15([0-3]|[5-9]))|(17[013678])|(18[0,5-9]))\\\\d{8}$";
        //检测邮箱
        Pattern patternEmail = Pattern.compile("[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?");
        //检测字符串中是否包含特殊符号
        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);


        for (StudentExcel studentExcel : data) {
            //集合添值
            Student studentNow = new Student();
            studentList.add(i - 1, studentNow);
            //获取Excel中的数据
            Student student = Objects.requireNonNull(BeanUtil.copy(studentExcel, Student.class));

            if (Func.isNotEmpty(studentExcel.getUserName()) || Func.isNotEmpty(studentExcel.getMobile()) ||
                    Func.isNotEmpty(studentExcel.getEmail()) || Func.isNotEmpty(studentExcel.getShowName()) ||
                    Func.isNotEmpty(studentExcel.getRealName()) || Func.isNotEmpty(studentExcel.getIdCardNo()) ||
                    Func.isNotEmpty(studentExcel.getSex()) || Func.isNotEmpty(studentExcel.getAge()) ||
                    Func.isNotEmpty(studentExcel.getPassword())) {
                //验证
                if (Func.isNull(studentExcel.getUserName()) && Func.isNull(studentExcel.getMobile()) &&
                        Func.isNull(studentExcel.getEmail())) {
                    errMsg += "第" + i + "行，账号、手机、邮箱 三者必须填写其中一个;<br />";
                }
                //查库
                Student temp = new Student();
                //账号
                if (Func.isNotEmpty(student.getUserName())) {
                    temp = baseMapper.checkStudentUserName(student);

                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，账号重复;<br />";
                    } else if (pattern.matcher(studentExcel.getUserName()).matches()) {
                        errMsg += "第" + i + "行，账号不能为纯数字;<br />";
                    } else {
                        studentList.get(i - 1).setUserName(studentExcel.getUserName());
                    }
                }

                //手机
                if (Func.isNotEmpty(student.getMobile())) {
                    temp = baseMapper.checkStudentMobile(student);
                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，电话重复;<br />";
                    } else if (Func.isNotEmpty(studentExcel.getMobile()) && !pattern.matcher(studentExcel.getMobile()).matches() && studentExcel.getMobile().matches(pMobile)) {
                        errMsg += "第" + i + "行，手机号不正确;<br />";
                    } else {
                        studentList.get(i - 1).setMobile(studentExcel.getMobile());
                    }
                }

                //邮箱
                if (Func.isNotEmpty(student.getEmail())) {
                    temp = baseMapper.checkStudentEmail(student);
                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，邮箱重复;<br />";
                    } else if (!patternEmail.matcher(studentExcel.getEmail()).matches()) {
                        errMsg += "第" + i + "行，邮箱不正确;<br />";
                    } else {
                        studentList.get(i - 1).setEmail(studentExcel.getEmail());
                    }
                }

                //真实姓名
                if (Func.isNull(studentExcel.getRealName())) {
                    studentList.get(i - 1).setRealName("");
                } else {
                    if (pattern.matcher(studentExcel.getRealName()).matches()) {
                        errMsg += "第" + i + "行，真实姓名不能为纯数字；<br />";
                    } else if (p.matcher(studentExcel.getRealName()).find()) {
                        errMsg += "第" + i + "行，真实姓名不可以包含符号；<br />";
                    } else {
                        studentList.get(i - 1).setRealName(studentExcel.getRealName());
                    }
                }

                studentList.get(i - 1).setShowName(Func.isNull(studentExcel.getShowName()) ? "" : studentExcel.getShowName());
                studentList.get(i - 1).setIdCardNo(Func.isNull(student.getIdCardNo()) ? "" : student.getIdCardNo());
                if (Func.isNull(studentExcel.getSex())) {
                    studentList.get(i - 1).setSex(0);
                } else if (studentExcel.getSex().equals("男")) {
                    studentList.get(i - 1).setSex(1);
                } else if (studentExcel.getSex().equals("女")) {
                    studentList.get(i - 1).setSex(2);
                }
                studentList.get(i - 1).setAge(Func.isNull(student.getAge()) ? null : student.getAge());
                studentList.get(i - 1).setRegisterFrom("6");
                // 设置默认密码
                studentList.get(i - 1).setPassword(Func.isNull(student.getPassword()) ? "123456" : student.getPassword());

                //赠送课程
                if (Func.isNotEmpty(studentExcel.getCourseIdList())) {
                    if (studentExcel.getCourseIdList().contains("，")) {
                        errMsg += "第" + i + "行，课程id间隔请使用英文\",\"<br />";
                        i++;
                        continue;
                    }
                    String[] cours = studentExcel.getCourseIdList().split(",");
                    List<Integer> courseIdList = new ArrayList<>();
                    for (int courNum = 0; courNum < cours.length; courNum++) {
                        //判断是否可以转成int类型
                        if (!NumberUtils.isDigits(cours[courNum])) {
                            errMsg += "第" + i + "行，课程id间隔请使用英文\",\"<br />";
                            continue;
                        }
                        //查询课程id是否有课程
                        Course course = courseService.getCourseById(Integer.valueOf(cours[courNum]));
                        //检测课程（套餐）id
                        if (Func.isNull(course)) {
                            errMsg += "第" + i + "行，课程id为" + cours[courNum] + "的课程不存在;<br />";
                            continue;
                        } else
                            //被删除的课程是否参与赠送
                            if (course.getIsDeleted() == 1) {
                                errMsg += "第" + i + "行，课程id为" + cours[courNum] + "的课程已被删除;<br />";
                                continue;
                            }
                        courseIdList.add(Integer.valueOf(cours[courNum]));
                    }
                    studentList.get(i - 1).setCourseIdList(courseIdList);
                }
                if (i == data.size()) {
                    break;
                } else {
                    i++;
                }
            } else {
                return;
            }
        }
        //数据入库
        if (Func.equals(errMsg, "")) {
            for (Student student : studentList) {
                student.setAgentId(SecureUtil.getUserId());
                this.register(student);
                if (Func.isNotEmpty(student.getCourseIdList())) {
                    Student student1 = baseMapper.checkStudentNew(student);
                    for (Integer courseId : student.getCourseIdList()) {
                        List<Course> courseList = new ArrayList<>();
                        Course course = courseService.getCourseById(courseId);
                        courseList.add(course);

                        courseService.courseOrder(student1, courseList);
                    }
                }
            }
        } else {
            throw new ServiceException(errMsg);
        }
    }

    @Override
    public void importUser(List<StudentExcel> data) {
        if (data.size() == 0) {
            throw new ServiceException("文件无数据加载！！！");
        }

        int i = 1; //行数
        String errMsg = "";//错误信息
        List<Student> studentList = new ArrayList<>(); //导入用户数据集合
        //检测是否为纯数字
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        //检测手机号
        String pMobile = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
        //检测邮箱
        Pattern patternEmail = Pattern.compile("[\\w!#$%&'*+/=?^_`{|}~-]+(?:\\.[\\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\\w](?:[\\w-]*[\\w])?\\.)+[\\w](?:[\\w-]*[\\w])?");
        //检测字符串中是否包含特殊符号
        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);


        for (StudentExcel studentExcel : data) {
            //集合添值
            Student studentNow = new Student();
            studentList.add(i - 1, studentNow);
            //获取Excel中的数据
            Student student = Objects.requireNonNull(BeanUtil.copy(studentExcel, Student.class));

            if (Func.isNotEmpty(studentExcel.getUserName()) || Func.isNotEmpty(studentExcel.getMobile()) ||
                    Func.isNotEmpty(studentExcel.getEmail()) || Func.isNotEmpty(studentExcel.getShowName()) ||
                    Func.isNotEmpty(studentExcel.getRealName()) || Func.isNotEmpty(studentExcel.getIdCardNo()) ||
                    Func.isNotEmpty(studentExcel.getSex()) || Func.isNotEmpty(studentExcel.getAge()) ||
                    Func.isNotEmpty(studentExcel.getPassword())) {
                //验证
                if (Func.isNull(studentExcel.getUserName()) && Func.isNull(studentExcel.getMobile()) &&
                        Func.isNull(studentExcel.getEmail())) {
                    errMsg += "第" + i + "行，账号、手机、邮箱 三者必须填写其中一个;<br />";
                }
                //查库
                Student temp = new Student();
                //账号
                if (Func.isNotEmpty(student.getUserName())) {
                    temp = baseMapper.checkStudentUserName(student);

                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，账号重复;<br />";
                    } else if (pattern.matcher(studentExcel.getUserName()).matches()) {
                        errMsg += "第" + i + "行，账号不能为纯数字;<br />";
                    } else {
                        studentList.get(i - 1).setUserName(studentExcel.getUserName());
                    }
                }

                //手机
                if (Func.isNotEmpty(student.getMobile())) {
                    temp = baseMapper.checkStudentMobile(student);
                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，电话重复;<br />";
                    } else if (Func.isNotEmpty(studentExcel.getMobile())
                            && !pattern.matcher(studentExcel.getMobile()).matches() && studentExcel.getMobile().matches(pMobile)) {
                        errMsg += "第" + i + "行，手机号不正确;<br />";
                    } else {
                        studentList.get(i - 1).setMobile(studentExcel.getMobile());
                    }
                }

                //邮箱
                if (Func.isNotEmpty(student.getEmail())) {
                    temp = baseMapper.checkStudentEmail(student);
                    if (Func.isNotEmpty(temp)) {
                        errMsg += "第" + i + "行，邮箱重复;<br />";
                    } else if (!patternEmail.matcher(studentExcel.getEmail()).matches()) {
                        errMsg += "第" + i + "行，邮箱不正确;<br />";
                    } else {
                        studentList.get(i - 1).setEmail(studentExcel.getEmail());
                    }
                }

                //真实姓名
                if (Func.isNull(studentExcel.getRealName())) {
                    studentList.get(i - 1).setRealName("");
                } else {
                    if (pattern.matcher(studentExcel.getRealName()).matches()) {
                        errMsg += "第" + i + "行，真实姓名不能为纯数字；<br />";
                    } else if (p.matcher(studentExcel.getRealName()).find()) {
                        errMsg += "第" + i + "行，真实姓名不可以包含符号；<br />";
                    } else {
                        studentList.get(i - 1).setRealName(studentExcel.getRealName());
                    }
                }

                studentList.get(i - 1).setShowName(Func.isNull(studentExcel.getShowName()) ? "" : studentExcel.getShowName());
                studentList.get(i - 1).setIdCardNo(Func.isNull(student.getIdCardNo()) ? "" : student.getIdCardNo());
                if (Func.isNull(studentExcel.getSex())) {
                    studentList.get(i - 1).setSex(0);
                } else if (studentExcel.getSex().equals("男")) {
                    studentList.get(i - 1).setSex(1);
                } else if (studentExcel.getSex().equals("女")) {
                    studentList.get(i - 1).setSex(2);
                }
                studentList.get(i - 1).setAge(Func.isNull(student.getAge()) ? null : student.getAge());
                studentList.get(i - 1).setRegisterFrom("5");
                // 设置默认密码
                studentList.get(i - 1).setPassword(Func.isNull(student.getPassword()) ? "123456" : student.getPassword());

                //赠送课程
                if (Func.isNotEmpty(studentExcel.getCourseIdList())) {
                    if (studentExcel.getCourseIdList().contains("，")) {
                        errMsg += "第" + i + "行，课程id间隔请使用英文\",\"<br />";
                        i++;
                        continue;
                    }
                    String[] cours = studentExcel.getCourseIdList().split(",");
                    List<Integer> courseIdList = new ArrayList<>();
                    for (int courNum = 0; courNum < cours.length; courNum++) {
                        //判断是否可以转成int类型
                        if (!NumberUtils.isDigits(cours[courNum])) {
                            errMsg += "第" + i + "行，课程id间隔请使用英文\",\"<br />";
                            continue;
                        }
                        //查询课程id是否有课程
                        Course course = courseService.getCourseById(Integer.valueOf(cours[courNum]));
                        //检测课程（套餐）id
                        if (Func.isNull(course)) {
                            errMsg += "第" + i + "行，课程id为" + cours[courNum] + "的课程不存在;<br />";
                            continue;
                        } else
                            //被删除的课程是否参与赠送
                            if (course.getIsDeleted() == 1) {
                                errMsg += "第" + i + "行，课程id为" + cours[courNum] + "的课程已被删除;<br />";
                                continue;
                            }
                        courseIdList.add(Integer.valueOf(cours[courNum]));
                    }
                    studentList.get(i - 1).setCourseIdList(courseIdList);
                }
                if (i == data.size()) {
                    break;
                } else {
                    i++;
                }
            } else {
                return;
            }
        }
        //数据入库
        if (Func.equals(errMsg, "")) {
            for (Student student : studentList) {
                this.register(student);
                if (Func.isNotEmpty(student.getCourseIdList())) {
                    Student student1 = baseMapper.checkStudentNew(student);
                    for (Integer courseId : student.getCourseIdList()) {
                        List<Course> courseList = new ArrayList<>();
                        Course course = courseService.getCourseById(courseId);
                        courseList.add(course);

                        courseService.courseOrder(student1, courseList);
                    }
                }
            }
        } else {
            throw new ServiceException(errMsg);
        }
    }

    @Override
    public void exportUser(HttpServletResponse response, Map<String, Object> user) {
        ExcelWriter excelWriter = null;
        try {
            Integer size = 3000;

            excelWriter = EasyExcel.write(response.getOutputStream(), ExportStudentExcel.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("用户数据表").build();
            //分页写入
            QueryWrapper<Student> queryWrapper = Condition.getQueryWrapper(new Student());
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("id")), Student::getId, user.get("id"));
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("mobile")), Student::getMobile, user.get("mobile"));
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("email")), Student::getEmail, user.get("email"));
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("showName")), Student::getShowName, user.get("showName"));
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("realName")), Student::getRealName, user.get("realName"));
            queryWrapper.lambda().like(Func.isNotEmpty(user.get("userName")), Student::getUserName, user.get("userName"));

            queryWrapper.lambda().eq(Func.isNotEmpty(user.get("registerFrom")), Student::getRegisterFrom, user.get("registerFrom"));
            queryWrapper.lambda().eq(Student::getIsAvalible, "2");

            Integer count = baseMapper.selectCount(queryWrapper);
            Page<ExportStudentExcel> page = new Page<>();
            for (int i = 1; i <= (count / size) + 1; i++) {
                page.setCurrent(i);
                page.setSize(size);
                List<ExportStudentExcel> list = baseMapper.exportUser(page, queryWrapper);
                User adminUser = new User();
                adminUser.setRoleId("12");
                List<User> listUserAdmin = userService.list(Condition.getQueryWrapper(adminUser));
                for (ExportStudentExcel studentExcel : list) {
                    if (Func.isNotEmpty(studentExcel.getAgentId()) && !("-1").equals(studentExcel.getAgentId())) {
                        for (User userAdmin : listUserAdmin) {
                            if ((Integer.valueOf(studentExcel.getAgentId())).equals(userAdmin.getId())) {
                                studentExcel.setAgentId(userAdmin.getName());
                                break;
                            }
                        }
                    } else {
                        studentExcel.setAgentId("");
                    }
                }
                excelWriter.write(list, writeSheet);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
        }
    }

    @Override
    public R<?> register(Student student) {
        student.setIsAvalible(2);

        // 若上传身份证则校验
        if (StringUtils.isNotEmpty(student.getIdCardPhoto())) {
            Map<String, Object> cardData = this.idCard(student.getIdCardPhoto());
            if (Objects.nonNull(cardData)) {
                OcrDataUtil data = (OcrDataUtil) cardData.get("data");
                student.setHeadImg(cardData.get("url").toString());
                if (!StringUtils.equals(data.getName(), student.getRealName()) || !StringUtils.equals(data.getIdNumber(), student.getIdCardNo())) {
                    return R.fail("身份证信息错误，请上传个人身份证件照片");
                }
            } else {
                return R.fail("身份证格式错误,请重新上传横向身份证照片");
            }
        }
        // 调用接口查询证书
        List<StudentZhengshu> zhengShuInfo = slryService.getZhengShuInfo(student.getIdCardNo());
        if (Func.isEmpty(zhengShuInfo)) {
            // 证书信息为空，回滚
            throw new ServiceException("证书信息为空");
        }

        boolean hasAorB = zhengShuInfo.stream().anyMatch(zhengShu -> "A".equals(zhengShu.getZxtype()) || "B".equals(zhengShuInfo.get(0).getZxtype()));
        if (hasAorB) {
            // 手机号二要素校验 https://frag.gsjtsz.cn/api/front/mobile2Ele/check?name=蒋应辉&phone=18693269755&note=全省建筑施工企业主要负责人专题安全教育培训系统
            String mobile2EleUrl = "https://frag.gsjtsz.com/api/front/mobile2Ele/check?"
                    + "name=" + student.getRealName()
                    + "&phone=" + student.getMobile()
                    + "&note=" + "agry_".concat(zhengShuInfo.get(0).getQymc()).concat("_").concat(zhengShuInfo.get(0).getSsqytyshxydm()).concat("_").concat(zhengShuInfo.get(0).getRyid());
            String mobile2Ele = HttpUtil.get(mobile2EleUrl);
            if (StrUtil.isBlank(mobile2Ele)) {
                throw new ServiceException("您访问的服务器正在升级，请稍后重试");
            }
            boolean date = JSONUtil.parseObj(mobile2Ele).get("data").equals(true);
            if (!date) {
                throw new ServiceException("请核实并输入您的真实姓名和手机号码");
            }
        }

        // 如果证书状态都为0，则不允许注册
        boolean isAllStatusZero = zhengShuInfo.stream().allMatch(zhengShu -> zhengShu.getStatus() == 0);
        if (isAllStatusZero) {
            throw new ServiceException("证书已过期，请联系省厅");
        }
        // 针对无法人脸身份证比对白名单（树脉接口问题）
        boolean checkFaceIdCardReg = true;
        Object whitelist_face_login_reg_o = redisUtil.get(WHITELIST_FACE_LOGIN_REG);
        if (Func.isNotEmpty(whitelist_face_login_reg_o)) {
            String whitelist_face_login_reg = Convert.toStr(whitelist_face_login_reg_o);
            if (StrUtil.contains(whitelist_face_login_reg, student.getIdCardNo())) {
                checkFaceIdCardReg = false;
            }
        }
        // 人脸身份证比对
        if (checkFaceIdCardReg && StrUtil.isNotBlank(student.getUserFaceImage())) {
            StudentZhengshu studentZhengshu = zhengShuInfo.get(0);
            String ryid = studentZhengshu.getRyid();
            String ryxm = studentZhengshu.getRyxm();
            String timestamp = System.currentTimeMillis() + "";
            String sign = MD5Utils.encrypt(shumai_appid + "&" + timestamp + "&" + shumai_appSecurity);
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("appid", shumai_appid);
            params.put("timestamp", timestamp);
            params.put("sign", sign);
            params.put("name", ryxm);
            params.put("idcard", ryid);
            params.put("image", student.getUserFaceImage());
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            try {
                log.info("{}:注册人脸身份证比对请求报文：{}", ryid, JSONUtil.toJsonStr(params));
                String result = HttpUtils.postForm(shumai_url, headers, params);
                JSONObject parseObj = JSONUtil.parseObj(result);
                FaceCardDTO faceCardDTO = JSONUtil.toBean(parseObj.get("data").toString(), FaceCardDTO.class);
                log.info("{}:人脸身份证比对返回报文：{}", ryid, result);
                if (faceCardDTO.getIncorrect() == 101) {
                    log.info("{}:身份证号码姓名不一致", ryid);
                    throw new ServiceException("身份证号码姓名不一致,请重新核实");
                }
                if (faceCardDTO.getScore() <= shumai_successScore) {
                    log.info("{}:人脸身份证比对失败,分数：{}", ryid, faceCardDTO.getScore());
                    throw new ServiceException("人脸身份证比对失败");
                }
                // 人脸识别通过
                String filePath = base64StrToImage.base64ToFile(student.getUserFaceImage(), IdUtil.simpleUUID());
                student.setHeadImg(filePath);
                student.setIsReal(2);
            } catch (Exception e) {
                log.error("{}:人脸身份证比对失败", ryid, e);
                throw new ServiceException("人脸身份证比对失败");
            }
        }
        //密码加密
        student.setPassword(DigestUtil.encrypt(student.getPassword()));
        save(student);
        //保存学生信息
        //创建学生储值
        BigDecimal zero = new BigDecimal(0);
        UserAccount userAccount = new UserAccount();
        userAccount.setBalance(zero);
        userAccount.setBackAmount(zero);
        userAccount.setCashAmount(zero);
        userAccount.setForzenAmount(zero);
        userAccount.setVmAmount(zero);
        userAccount.setUserId(student.getId());
        userAccountService.save(userAccount);
        for (StudentZhengshu zhengShu : zhengShuInfo) {
            // 根据证书编号查询是否存在证书，如果存在，则无法注册
            int count = studentZhengshuService.count(new LambdaQueryWrapper<StudentZhengshu>().eq(StudentZhengshu::getNewzsbh, zhengShuInfo.get(0).getNewzsbh()).eq(StudentZhengshu::getStatus, 1));
            if (count > 0) {
                throw new ServiceException("证书已被注册");
            }
            if (zhengShu.getStatus() != 0) {
                // 根据证书类型获取课程id
                String slryCourse = dictService.getValue(SLRY_COURSE, zhengShu.getZxtype());
                // 根据证书信息关联课程
                courseService.giveCourse(Convert.toStr(student.getId()), slryCourse, zhengShu);
                // 根据课程id关联试卷
//               slryService.saveExampaperRecord(student.getId(), slryCourse);
            }
        }

        // 校验是否存在重复的A、B、C证书
        List<Integer> courseIds = Arrays.asList(SLRY_COURSE_ID_A, SLRY_COURSE_ID_B, SLRY_COURSE_ID_C); // 需要定义B、C证书的ID常量
        for (Integer courseId : courseIds) {
            List<TrxorderDetail> trxorderDetailList = trxorderDetailService.list(
                    new LambdaQueryWrapper<TrxorderDetail>()
                            .eq(TrxorderDetail::getUserId, student.getId())
                            .eq(TrxorderDetail::getCourseId, courseId)
                            .orderByAsc(TrxorderDetail::getZhengshuYxq)
            );
            // 如果存在多个相同类型证书，设置清除状态clearType，证书有效期最近的为有效0，其余为无效-1
            if (trxorderDetailList.size() > 1) {
                for (int i = 0; i < trxorderDetailList.size(); i++) {
                    trxorderDetailList.get(i).setClearType(i == 0 ? 0 : -1);
                }
                trxorderDetailService.updateBatchById(trxorderDetailList);
            }
        }

        // 男：1，女：2
        student.setSex(StrUtil.contains(zhengShuInfo.get(0).getRyxb(), "男") ? 1 : 2);
        student.setRealName(zhengShuInfo.get(0).getRyxm());
        student.setCompanyName(zhengShuInfo.get(0).getQymc());
        student.setZhengshuInfo(JSON.toJSONString(zhengShuInfo));
        // 将之前的证书信息修改为无效
        LambdaUpdateWrapper<StudentZhengshu> studentZhengshuLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        studentZhengshuLambdaUpdateWrapper.eq(StudentZhengshu::getRyid, student.getIdCardNo());
        studentZhengshuLambdaUpdateWrapper.set(StudentZhengshu::getStatus, 0);
        studentZhengshuLambdaUpdateWrapper.set(StudentZhengshu::getUpdatetime, DateUtil.now());
        studentZhengshuService.update(studentZhengshuLambdaUpdateWrapper);
        studentZhengshuService.saveBatch(zhengShuInfo);
        return R.success("操作成功");
    }

    @SneakyThrows
    @Override
    public Map<String, Object> idCard(String filePath) {

        Map<String, Object> map = new HashMap<>();
        URL url = new URL(filePath);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(10 * 1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        //得到输入流
        InputStream inputStream = conn.getInputStream();
        RecognizeIdcardRequest recognizeIdcardRequest = new RecognizeIdcardRequest()
                .setBody(inputStream)
                .setOutputQualityInfo(true)
                .setOutputFigure(true);

        RuntimeOptions runtime = new RuntimeOptions();


        try {
            RecognizeIdcardResponse recognizeIdcardResponse = client.recognizeIdcardWithOptions(recognizeIdcardRequest, runtime);
            // 获取图片对应数据
            Object face = new JSONObject(new JSONObject(recognizeIdcardResponse.body.data).get("data")).get("face");
            if (Objects.isNull(face)) {
                throw new ServiceException("身份证照片不符合要求，请参考【身份证照片上传示例】重新上传 code:03");
            }
            // 获取身份证图片的角度
            int angle = (int) new JSONObject(face).get("angle");
//            if (angle != 0) {
//                throw new ServiceException("请上传横向身份证照片");
//            }
//            log.info(recognizeIdcardResponse.body.data);
            //获取身份证正面信息转为DataVo
            OcrDataUtil dataVo = JSON.parseObject(String.valueOf(new JSONObject(face).get("data")), OcrDataUtil.class);

            // 获取头像定位以截取
            List figureList = JSON.parseObject(String.valueOf(new JSONObject(String.valueOf(face)).get("figure")), List.class);
            JSONObject figure = new JSONObject(figureList.get(0));
            // 证件照高度
            int h = Convert.toInt(figure.get("h"));
            // 证件照宽度
            int w = Convert.toInt(figure.get("w"));
            //证件照所在身份证x轴的值
            int x = Convert.toInt(figure.get("x"));
            //证件照所在身份证y轴的值
            int y = Convert.toInt(figure.get("y"));
            // 获取图片后缀以裁剪和保存头像之后的类型
            String suffix = filePath.substring(filePath.lastIndexOf(".") + 1);
            // 初始化OperateImage对象
            OperateImage o = new OperateImage(x, y, w, h);
            // 网络地址转为本地地址
            String localLocation = headPath + "/" + filePath.substring(filePath.lastIndexOf("upload"));
            // 本地图片地址
            o.setSrcpath(localLocation);
//            //unix时间戳命名证件照图片名称
//            long timeStamp = System.currentTimeMillis();
//            int timeStampUnix = (int) (timeStamp / 1000);
            // uuid命名证件照图片名称
            String timeStampUnix = IdUtil.fastSimpleUUID();
            // 证件照图片输出地址
            String headPhotoUrl = headPath + "/upload/student/" + timeStampUnix + "." + suffix;
            String photoUrl = headUrl + "/upload/student/" + timeStampUnix + "." + suffix;
            o.setSubpath(headPhotoUrl);
            // 裁剪
            try {
                o.cut(suffix, angle);
                // 裁剪（优化）
//                ImgUtil.cut(
//                        cn.hutool.core.io.FileUtil.file(localLocation),// 原图
//                        cn.hutool.core.io.FileUtil.file(headPhotoUrl),// 裁剪后的图片
//                        new Rectangle(x, y, w, h)//裁剪的矩形区域
//                );
//                // 根据角度旋转裁剪后的头像
//                if (angle > 0) {
//                    Image rotate = ImgUtil.rotate(ImgUtil.read(cn.hutool.core.io.FileUtil.file(headPhotoUrl)), 360 - angle);
//                    ImgUtil.write(rotate, FileUtil.file(headPhotoUrl));
//                }
            } catch (Exception e) {
                log.error("裁剪失败", e);
                return null;
            }
            map.put("data", dataVo);
            map.put("path", headPhotoUrl);
            map.put("url", photoUrl);
            return map;
        } catch (TeaException error) {
            // 如有需要，请打印 error
            log.error("阿里云idCard_TeaException", error);
            return null;
        }
    }

    @Override
    public Student checkStudent(Student student) {
        return baseMapper.checkStudent(student);
    }

    @Override
    public Student checkStudentNew(Student student) {
        return baseMapper.checkStudentNew(student);
    }

    @Override
    public Student checkStudentUserName(Student student) {
        return baseMapper.checkStudentUserName(student);
    }

    @Override
    public Student checkStudentUserIdNumber(Student student) {
        return baseMapper.checkStudentUserIdNumber(student);
    }

    @Override
    public Student checkStudentMobile(Student student) {
        return baseMapper.checkStudentMobile(student);
    }

    @Override
    public Student checkStudentEmail(Student student) {
        return baseMapper.checkStudentEmail(student);
    }

    @Override
    public Student getStudent(Integer userId) {
        return baseMapper.getStudent(userId);
    }

    @Override
    public void updateAvalible(String key) {
        key = key.substring(6, key.length());
        baseMapper.updateAvalible(key);
    }

    @Override
    public FaceResult vef(String imageBase, Integer userId) {
        FaceResult faceResult = new FaceResult();
        imageBase = JSONUtil.parseObj(imageBase).getStr("imageBase");
        Student student = baseMapper.getStudent(userId);
        if (Func.isNull(student)) {
            return FaceResult.error(FaceResult.NOT_FOUND_FACE, "用户不存在");
        }
        String studentImg = student.getHeadImg();
        if (student.getSyncPictureStatus() == 1 && StrUtil.isNotBlank(student.getProfilePhoto())) {
            studentImg = student.getProfilePhoto();
        }
        if (StringUtil.isNotEmpty(studentImg)) {
            //考试无论人脸识别成功与否都保存照片
            String filePath = base64StrToImage.base64ToFile(imageBase, IdUtil.simpleUUID());
            //按照400 * 400 压缩图片
            try {
                Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(330, 230).toFile(filePath.replace(uploadDomain, remotePath));
            } catch (IOException e) {
                log.info("图片压缩失败:{}", e.getMessage());
            }
            boolean flag = checkWhiteList(student);
            if (flag) {
                //判断人脸活体检测是否通过
                String ksDetectLivingFaceRedisKey = userId + "_ksDetectLivingFace_" + student.getMobile();
                Object ksDetectLivingFace = redisUtil.get(ksDetectLivingFaceRedisKey);
                if (Func.isNull(ksDetectLivingFace)) {
                    faceResult = aliFaceRecognitionUtil.detectLivingFace(imageBase);
                    log.info("阿里云返回考试人脸活体检测报文:{}", faceResult);
                    if (Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
                        redisUtil.set(ksDetectLivingFaceRedisKey, "Y", 5400);
                        log.info("考试人脸活体检测通过,保存到Redis中,有效时间为90分钟:{}", ksDetectLivingFaceRedisKey);
                    } else {
                        //所有非200状态码都返回-1
                        faceResult.setCode(FaceResult.FACE_ERROR);
                        faceResult.setData(filePath);
                        faceResult.setMsg("人脸活体检测不通过，可调整光线、角度等，保持面部没有遮挡物（口罩、眼镜等）");
                        log.info("考试人脸活体检测不通过,返回前端报文：{}", faceResult);
                        return faceResult;
                    }
                }
            }

            //人脸1:1对比
            if (faceapi_type.equals("tencent")) {
                faceResult = faceContrastServer.faceContrast(null, imageBase, studentImg, null);
            }

            if (faceapi_type.equals("ali")) {
                log.info("考试人脸对比");
                faceResult = aliFaceRecognitionUtil.faceContrast(null, imageBase, studentImg, null);
                log.info("阿里云返回考试人脸对比结果:{}", faceResult);
            }

            // 判断人脸1:1对比是否成功
            if (!Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
                faceResult.setCode(FaceResult.FACE_ERROR);
                faceResult.setMsg("人脸识别身份验证失败，请调整坐姿,确保面部位于摄像头采集区中");
            }
            //返回人脸识别照片
            faceResult.setData(filePath);
        } else {
            faceResult.setCode(FaceResult.FACE_ERROR);
        }
        log.info("考试人脸比对最终返回前端数据:{}", faceResult);
        return faceResult;
    }


    @Override
    public FaceResult vef(VefVO vefVO, Integer userId) {
        FaceResult faceResult = new FaceResult();
        String imageBase = vefVO.getImageBase();
        Student student = baseMapper.getStudent(userId);
        if (Func.isNull(student)) {
            return FaceResult.error(FaceResult.NOT_FOUND_FACE, "用户不存在");
        }
        boolean flag = checkWhiteList(student);
        // 解密数据
        if (flag) {
            try {
                WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService().getSessionInfo(vefVO.getWxCode());
                String openid = sessionInfo.getOpenid();
                String sessionKey = sessionInfo.getSessionKey();
                WxMaInternetResponse userEncryptKey = wxMaService.getInternetService().getUserEncryptKey(openid, sessionKey);
                List<WxMaInternetUserKeyInfo> wxKeyInfoList = userEncryptKey.getKeyInfoList();
                WxMaInternetUserKeyInfo wxMaInternetUserKeyInfo = new WxMaInternetUserKeyInfo();
                // 判断是否有版本号相同的版本号
                for (WxMaInternetUserKeyInfo keyInfo : wxKeyInfoList) {
                    if (Objects.equals(keyInfo.getVersion(), vefVO.getWxVersion())) {
                        wxMaInternetUserKeyInfo = keyInfo;
                    }
                }
                if (Func.isNull(wxMaInternetUserKeyInfo.getEncryptKey())) {
                    log.info("接口:人脸学习人脸对比,微信小程序服务调用失败1,当前用户userId:{},当前用户ip:{}", userId, WebUtil.getIP());
                    throw new ServiceException("vefVideo 微信小程序服务调用失败，请联系管理员");
                }
                // 解密数据
                AES aes = new AES(Mode.CBC, Padding.PKCS5Padding, wxMaInternetUserKeyInfo.getEncryptKey().getBytes(), wxMaInternetUserKeyInfo.getIv().getBytes());
                aes.decryptStr(vefVO.getWxSign(), CharsetUtil.CHARSET_UTF_8);
            } catch (Exception e) {
                log.info("接口:人脸学习人脸对比,微信小程序服务调用失败2,当前用户userId:{},当前用户ip:{}", userId, WebUtil.getIP());
                throw new ServiceException("vefVideo 微信小程序服务调用失败，请联系管理员");
            }
        }
        // 检测是否存在非法摄像头驱动设备
        String deviceVideo = vefVO.getDeviceVideo();
        if (Func.isNotEmpty(deviceVideo)) {
            log.info("{}摄像头驱动：{}", student.getMobile(), deviceVideo);
            // 记录摄像头驱动
            saveDevice(userId, deviceVideo);
            // 判断摄像头驱动是否在黑名单中
            JSONArray deviceVideoArray = JSONUtil.parseArray(deviceVideo);// 用户使用的摄像头驱动
            Object blackDeviceConfig = redisUtil.get(WebsiteProfile.BLACK_DEVICE);// 黑名单中的摄像头驱动列表 逗号分割
            if (flag && Func.isNotEmpty(blackDeviceConfig)) {
                String blackDevice = Convert.toStr(blackDeviceConfig);
                String[] blackDeviceArray = blackDevice.split(",");
                for (Object device : deviceVideoArray) {
                    String label = JSONUtil.parseObj(device).getStr("label").toLowerCase();
                    if (Func.isNotEmpty(label)) {
                        for (String s : blackDeviceArray) {
                            if (label.contains(s.toLowerCase())) {
                                String filePath = base64StrToImage.savaFaceErrorImg(imageBase, "blackDevice", student.getMobile());
                                log.info("{}摄像头驱动在黑名单中，保存照片：{}", student.getMobile(), filePath);
                                return FaceResult.error(FaceResult.NOT_FOUND_FACE, "请勿使用虚拟设备");
                            }
                        }
                    }
                }
            }
        } else {
            log.info("{}摄像头驱动为空", student.getMobile());
        }

        String studentImg = student.getHeadImg();
        // 照片已同步，使用证件照
        if (student.getSyncPictureStatus() == 1 && StrUtil.isNotBlank(student.getProfilePhoto())) {
            studentImg = student.getProfilePhoto();
        }
        if (StringUtil.isNotEmpty(studentImg)) {
            //检测同一个用户同一用户15分钟内调用活体检测次数是否超过5次。
            String detectLivingFaceNumRedis = userId + "_DetectLivingFace_Num_" + student.getMobile();
            Object detectLivingFaceNums = redisUtil.get(detectLivingFaceNumRedis);
            int faceNum = 0;
            if (Func.notNull(detectLivingFaceNums)) {
                faceNum = (int) detectLivingFaceNums;
                if (faceNum >= 5) {
                    String filePath = base64StrToImage.savaFaceErrorImg(imageBase, "studyliving5", student.getMobile());
                    try {
                        Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
                    } catch (IOException e) {
                        log.info("{},studyliving5压缩图片失败:{}", filePath, e.getMessage());
                    }
                    log.info("{}_该用户在15分钟内请求人脸活体认证接口超过5次：{}", detectLivingFaceNumRedis, filePath);
                    return FaceResult.error(FaceResult.NOT_FOUND_FACE, "系统检测到您多次未通过人脸活体检测,请确保本人操作,15分钟后重试！");
                }
            }
            //人脸1:1对比
            if (faceapi_type.equals("tencent")) {
                faceResult = faceContrastServer.faceContrast(null, imageBase, studentImg, null);
            }
            if (faceapi_type.equals("ali")) {
                faceResult = aliFaceRecognitionUtil.faceContrast(null, imageBase, studentImg, null);
            }

            // 判断人脸1:1对比是否成功
            if (!Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
                //保存人脸识别错误照片,此类文件可删除
                String filePath = base64StrToImage.savaFaceErrorImg(imageBase, "studyFace", student.getMobile());
                try {
                    Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
                } catch (IOException e) {
                    log.info("{},studyFace压缩图片失败:{}", filePath, e.getMessage());
                }
                faceResult.setCode(FaceResult.FACE_ERROR);
                faceResult.setMsg("人脸识别身份验证失败，请调整坐姿,确保面部位于摄像头采集区中");
                log.info("学习人脸对比不通过,照片地址为：{}", filePath);
                return faceResult;
            }
            String filePath = base64StrToImage.base64ToFile(imageBase, IdUtil.simpleUUID());
            try {
                BufferedImage sourceImg = null;
                InputStream murl = new URL(filePath).openStream();
                sourceImg = ImageIO.read(murl);
                log.info("{},图片宽高为：{}x{}", filePath, sourceImg.getWidth(), sourceImg.getHeight());
                // 检测图片宽高是否符合要求，必须为500*500 或 225*225
                // 获取referer
                String referer = WebUtil.getRequest().getHeader("referer");
                // 如果是来自微信小程序的请求，不做宽高检测
                boolean isWechat = StrUtil.contains(referer, TokenUtil.SERVER_WECHAT);
                if (!isWechat && (sourceImg.getWidth() != 500 || sourceImg.getHeight() != 500)) {
                    log.info("学习人脸照片检测宽高失败:{}", filePath);
                    base64StrToImage.savaFaceErrorImg(imageBase, "no500", student.getMobile());
                    return FaceResult.error(FaceResult.NOT_FOUND_FACE, "非法请求，您的违规操作已被记录！");
                }
                //按照400 * 400 压缩图片
                Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
            } catch (IOException e) {
                log.info("{},获取图片宽高失败:{}", filePath, e.getMessage());
            }
            if (flag) {
                //判断人脸活体检测是否通过
                String detectLivingFaceRedis = userId + "_DetectLivingFace_" + student.getMobile();
                Object detectLivingFace = redisUtil.get(detectLivingFaceRedis);
                if (Func.isNull(detectLivingFace)) {
                    faceResult = aliFaceRecognitionUtil.detectLivingFace(imageBase);
                    if (Func.equals(FaceResult.SUCCESS_CODE, faceResult.getCode())) {
                        redisUtil.set(detectLivingFaceRedis, "Y", 3600);
                        log.info("学习人脸活体检测通过,保存到Redis中,有效时间为60分钟:{}", detectLivingFaceRedis);
                    } else {
                        //所有非200状态码都返回-1
                        faceResult.setCode(FaceResult.FACE_ERROR);
                        filePath = base64StrToImage.savaFaceErrorImg(imageBase, "studyliving", student.getMobile());
                        try {
                            Thumbnails.of(filePath.replace(uploadDomain, remotePath)).size(400, 400).toFile(filePath.replace(uploadDomain, remotePath));
                        } catch (IOException e) {
                            log.info("{},studyliving压缩图片失败:{}", filePath, e.getMessage());
                        }
                        faceResult.setMsg("人脸活体检测不通过，可调整光线、角度等，保持面部没有遮挡物（口罩、眼镜等）。照片模糊、反光、光线过亮或背景为白墙都会影响检测结果，建议更换一个亮度适中的环境再次尝试！");
                        log.info("学习人脸活体检测不通过,照片地址为:{},返回前端报文:{}", filePath, faceResult);
                        faceNum += 1;
                        redisUtil.set(detectLivingFaceNumRedis, faceNum, 900);
                        return faceResult;
                    }
                }
            }
            log.info("学习人脸对比通过,照片地址为：{}", filePath);
            faceResult = slryService.updateCourseStudyhistory(filePath, vefVO, student.getMobile(), flag);
            log.info("更新学习记录报文：{}", faceResult);
        } else {
            faceResult.setCode(FaceResult.FACE_ERROR);
        }
        return faceResult;
    }

    private void saveDevice(Integer userId, String deviceVideo) {
        UserLoginLog userLoginLog = new UserLoginLog();
        userLoginLog.setLoginTime(DateUtil.now());
        userLoginLog.setIp(WebUtil.getIP());
        userLoginLog.setUserId(userId);
        userLoginLog.setOsName("camera");
        userLoginLog.setUserAgent(deviceVideo);
        userLoginLogService.save(userLoginLog);
    }

    @Override
    public boolean checkWhiteList(Student student) {
        boolean flag = true;
        Object whitelist_ip_o = redisUtil.get(WHITELIST_IP);
        if (Func.isNotEmpty(whitelist_ip_o)) {
            String whitelist_ip = Convert.toStr(whitelist_ip_o);
            String ip = WebUtil.getIP();
            if (!whitelist_ip.contains(ip)) {
                return flag;
            }
        }
        // 查询个人白名单
        int i = studentWhitelistService.count(new LambdaQueryWrapper<StudentWhitelist>()
                .eq(StudentWhitelist::getIdCard, student.getIdCardNo())
                .eq(StudentWhitelist::getStatus, 1));
        if (i > 0) {
            // 个人白名单存在
            flag = false;
        } else {
            // 个人白名单不存在，查询企业白名单
            // 根据身份证id查询证书信息
            LambdaQueryWrapper<StudentZhengshu> lqw = new LambdaQueryWrapper<>();
            lqw.eq(StudentZhengshu::getRyid, student.getIdCardNo());
            lqw.eq(StudentZhengshu::getStatus, 1);
            List<StudentZhengshu> studentZhengshuList = studentZhengshuService.list(lqw);// 已有的证书信息
            for (StudentZhengshu zhengshu : studentZhengshuList) {
                // 查询企业白名单
                i = companyWhitelistService.count(new LambdaQueryWrapper<CompanyWhitelist>()
                        .eq(CompanyWhitelist::getCompanyName, zhengshu.getQymc())
                        .eq(CompanyWhitelist::getStatus, 1));
                if (i > 0) {
                    // 企业白名单存在
                    flag = false;
                    break;
                }
            }
        }
        return flag;
    }

    @Override
    public FaceResult savaScreen(String imageBase) {
        //保存照片
        FaceResult faceResult = new FaceResult();
        String filePath = base64StrToImage.base64ToFile(imageBase, IdUtil.simpleUUID());
        if (Func.isNotEmpty(filePath)) {
            faceResult.setCode(FaceResult.SUCCESS_CODE);
            faceResult.setData(filePath);
            faceResult.setMsg("保存成功");
            log.info("考试成绩展示页截图保存成功,截图地址为：" + filePath);
        } else {
            faceResult.setCode(FaceResult.FACE_ERROR);
            faceResult.setData(filePath);
            faceResult.setMsg("保存失败");
        }
        return faceResult;
    }

    @Override
    public Page<Student> selectByAll(Query query, QueryWrapper<Student> queryWrapper) {
        Page<Student> page = new Page<>(query.getCurrent(), query.getSize());
        return studentMapper.selectByAll(page, queryWrapper);
    }

//    public static void main(String[] args) {
//        String[] a = {"360Cam","USB Camera (058d:1864)"};
//        String label = "USB Camera (058d:1864)";
//        // 判断摄像头驱动是否在黑名单中，模糊匹配
//        for (String s : a) {
//            if (label.toLowerCase().contains(s)) {
//                System.out.println("包含");
//            }
//        }
//    }
}
