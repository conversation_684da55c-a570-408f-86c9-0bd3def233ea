package org.springcrazy.modules.front.controller;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023/4/23
 */

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.boot.ctrl.CrazyController;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.api.ResultCode;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.core.tool.utils.RedisUtil;
import org.springcrazy.modules.exam.entity.ExampaperRecord;
import org.springcrazy.modules.exam.service.IExampaperRecordService;
import org.springcrazy.modules.front.vo.VefVO;
import org.springcrazy.modules.user.service.IStudentService;
import org.springcrazy.modules.user.utils.Base64StrToImage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * banner图管理 控制器
 *
 * <AUTHOR>
 * @since 2020-03-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/front/face")
@Api(value = "前端人脸比对接口", tags = "前端人脸比对接口")
@Slf4j
public class FaceController extends CrazyController {
    @Autowired
    IStudentService studentService;

    @Autowired
    IExampaperRecordService exampaperRecordService;

    @Autowired
    private Base64StrToImage base64StrToImage;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 人脸比对（考试）
     */
    @PostMapping("vef")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "人脸验证", notes = "人脸验证")
    public R faceVef(@RequestBody String imageBase) {
        Integer userId = SecureUtil.getUserId();
        if (Func.isEmpty(imageBase)) {
            log.info("{}考试人脸识别图片为空", userId);
            return R.fail("人脸识别身份验证失败，请调整坐姿,确保面部位于摄像头采集区中");
        }
        //判断如果没有用户信息则进行登录
        if (Func.equals(userId, -1)) {
            return R.fail(ResultCode.UN_AUTHORIZED);
        }
        return R.data(studentService.vef(imageBase, userId));
    }


    /**
     * 人脸比对（观看课程）
     */
    @PostMapping("/vefVideo")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "人脸验证", notes = "人脸验证")
    public R faceVefVideo(@RequestBody VefVO vefVO, HttpServletRequest request) {
        //vefVO.setImageBase(netImageToBase64("https://agry.gsjtsz.com/upload/facePicture/successImg/2023-08-19/d744fe55d1514e8191c2822605ec8456.png"));
        Integer userId = SecureUtil.getUserId();
        if (Func.isEmpty(vefVO.getImageBase())) {
            log.info("{}看课人脸识别图片为空", userId);
            return R.fail("人脸识别身份验证失败，请调整坐姿,确保面部位于摄像头采集区中");
        }
        //判断如果没有用户信息则进行登录
        if (Func.equals(userId, -1)) {
            return R.fail(ResultCode.UN_AUTHORIZED);
        }
        return R.data(studentService.vef(vefVO, userId));
    }

    /**
     * 保存截图
     */
    @PostMapping("/savaScreen")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "保存截图", notes = "保存截图")
    public R savaScreen(@RequestBody VefVO vefVO) {
        Integer userId = SecureUtil.getUserId();
        //判断如果没有用户信息则进行登录
        if (Func.equals(userId, -1)) {
            return R.fail(ResultCode.UN_AUTHORIZED);
        }
        //考试成绩页面截图
        String filePath = base64StrToImage.base64ToFile(vefVO.getImageBase(), IdUtil.simpleUUID());
        log.info("考试成绩页面截图：" + filePath);
        ExampaperRecord record = exampaperRecordService.getById(vefVO.getExamRecordId());
        record.setId(vefVO.getExamRecordId());
        if (Func.isNotEmpty(filePath)) {
            record.setScreen(StrUtil.isNotBlank(record.getScreen()) ? record.getScreen() + "," + filePath : filePath);
            exampaperRecordService.updateById(record);
            log.info("考试成绩页面保存成功！");
        } else {
            log.info("成绩页截图base64：" + vefVO.getImageBase());
            log.info("成绩页截内容有误！保存失败");
        }
        return R.success("保存成功");
    }


//    public static void main(String[] args) {
//        String s = "SET \"school_mater_1_1_5_lqx{}_DetectLivingFace_{}\" \"\\\"Y\\\"\" EX 172800";
//        String a = "12751";
//        String b = "15352469777";
//        s = StrUtil.format(s,a,b);
//        System.out.println(s);
//    }
}
