package org.springcrazy.modules.slry.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "edu_company_whitelist")
public class CompanyWhitelist {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 企业名称
     */
    @TableField(value = "company_name")
    private String companyName;

    /**
     * 0: 禁用 1: 启用
     */
    @TableField(value = "`status`")
    private Integer status;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "create_user")
    private Integer createUser;

    /**
     * 是否已删除 0-未删除 1-已删除
     */
    @TableField(value = "is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(exist = false)
    private String[] timeArr;
}