package org.springcrazy.modules.slry.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.core.tool.utils.RedisUtil;
import org.springcrazy.core.tool.utils.WebUtil;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.edu.service.ITrxorderDetailService;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.service.SlryService;
import org.springcrazy.modules.slry.service.StudentZhengshuService;
import org.springcrazy.modules.slry.vo.CertInfoTodayVO;
import org.springcrazy.modules.slry.vo.OrderDetailVO;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * 甘肃省住房和城乡建设厅查询学员课时接口
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/agryApi")
public class SlryApiController {

    private final IStudentService studentService;
    private final ITrxorderDetailService trxorderDetailService;
    private final StudentZhengshuService studentZhengshuService;
    private final SlryService slryService;
    private final RedisUtil redisUtil;
    public static final String AUTH_KEY = "b2b04e9a1ce7e973accc83f4dc98f39f";

    @Value("${slry.api-old-examine}")
    private String OLD_EXAMINE;

    /*@GetMapping("/detailInfo_old")
    public HashMap<String, Object> detailInfo(@RequestParam String code, @RequestParam String authKey) {
        getReqInfo();
        log.info("detailInfo入参code:{}", code);
        HashMap<String, Object> map = new HashMap<>();
        if (!StrUtil.equals(authKey, AUTH_KEY)) {
            log.error("authKey错误");
            map.put("code", -1);
            map.put("data", null);
            map.put("msg", "authKey错误");
            return map;
        }
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("code", code);
            String data_old = HttpUtil.get(OLD_EXAMINE, params);
            log.info("旧系统接口返回数据 data_old:{}", data_old);
            JSONObject jsonObject = JSON.parseObject(data_old);
            OrderDetailVO orderDetailVOInfo = jsonObject.getObject("data", OrderDetailVO.class);
            Integer codeInfo = jsonObject.getInteger("code");
            String msgInfo = jsonObject.getString("msg");
            if (Func.isNotEmpty(orderDetailVOInfo) || codeInfo == 0) {
                map.put("code", codeInfo);
                map.put("data", orderDetailVOInfo);
                map.put("msg", msgInfo);
                return map;
            }
            TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                    .eq(TrxorderDetail::getZhengshuCode, code)
                    .eq(TrxorderDetail::getClearType, 0)
                    .eq(TrxorderDetail::getExamineType, 1)
            );
            log.info("trxorderDetail:{}", trxorderDetail);
            if (Func.isEmpty(trxorderDetail)) {
                map.put("code", -1);
                map.put("data", null);
                map.put("msg", "继续教育未完成 或 暂未注册");
                return map;
            }
            String zhengshuYxq = trxorderDetail.getZhengshuYxq();
            DateTime zhengshuYxqDate = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
            // 判断证书是否过期
            boolean isBefore = zhengshuYxqDate.isBefore(DateUtil.beginOfDay(new Date()));
            if (isBefore) {
                map.put("code", -1);
                map.put("data", null);
                map.put("msg", "继续教育未完成 或 暂未注册");
                return map;
            }
            Student student = studentService.getById(trxorderDetail.getUserId());
            log.info("student:{}", student);
            // 用户是否被冻结
            if (Func.isEmpty(student) || student.getIsAvalible() == 1) {
                map.put("code", -1);
                map.put("data", null);
                map.put("msg", "继续教育未完成 或 暂未注册");
                return map;
            }
            OrderDetailVO orderDetailVO = OrderDetailVO.builder()
                    .userId(trxorderDetail.getUserId()).userName(trxorderDetail.getUserName()).nickName("").email("")
                    .phonenumber(student.getMobile()).cardNumber(student.getIdCardNo()).sex(student.getSex() == 1 ? "0" : "1")
                    .createTime(trxorderDetail.getCreateTime()).updateTime(trxorderDetail.getLastUpdateTime()).remark("")
                    .finish(true).code(code).finishTime(trxorderDetail.getExamineTime())
                    .build();
            log.info("orderDetailVO:{}", orderDetailVO);
            map.put("code", 0);
            map.put("data", orderDetailVO);
            map.put("msg", "执行成功");
            return map;
        } catch (Exception e) {
            log.error("detailInfo异常", e);
            map.put("code", -1);
            map.put("data", null);
            map.put("msg", "继续教育未完成 或 暂未注册");
            return map;
        }
    }*/

    @GetMapping("/detailInfo")
    public ApiResult detailInfoNew(@RequestParam String code, @RequestParam String idCardNo, @RequestParam String authKey) {
        getReqInfo();
        log.info("甘肃省住房和城乡建设厅查询学员课时接口 入参code:{}", code);
        if (!StrUtil.equals(authKey, AUTH_KEY)) {
            log.error("authKey错误");
            return ApiResult.error("authKey错误");
        }
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("code", code);
            String data_old = HttpUtil.get(OLD_EXAMINE, params);
            log.info("证书编号:{},旧系统接口返回数据 data_old:{}", code, data_old);
            JSONObject jsonObject = JSON.parseObject(data_old);
            OrderDetailVO orderDetailVOInfo = jsonObject.getObject("data", OrderDetailVO.class);
            Integer codeInfo = jsonObject.getInteger("code");
            String msgInfo = jsonObject.getString("msg");
            if (codeInfo == 0) {
                return ApiResult.success(orderDetailVOInfo, msgInfo);
            }
            if (codeInfo == -1) {
                List<StudentZhengshu> zhengShuInfo = slryService.getZhengShuInfo(idCardNo);
                for (StudentZhengshu studentZhengshu : zhengShuInfo) {
                    Map<String, Object> hashMap = new HashMap<>();
                    hashMap.put("code", studentZhengshu.getNewzsbh());
                    String data_old_r = HttpUtil.get(OLD_EXAMINE, hashMap);
                    JSONObject jsonObject_r = JSON.parseObject(data_old_r);
                    OrderDetailVO orderDetailVOInfo_r = jsonObject_r.getObject("data", OrderDetailVO.class);
                    Integer codeInfo_r = jsonObject_r.getInteger("code");
                    String msgInfo_r = jsonObject_r.getString("msg");
                    if (codeInfo_r == 0) {
                        return ApiResult.success(orderDetailVOInfo_r, msgInfo_r);
                    }
                }
            }
            StudentZhengshu studentZhengshu = studentZhengshuService.getOneByNewzsbh(code);
            if (Func.isEmpty(studentZhengshu)) {
                log.info("证书编号:{},未查询到证书信息", code);
                return ApiResult.error("继续教育未完成 或 暂未注册[1]");
            }
            String zhengshuYxq = studentZhengshu.getZsyxq();
            DateTime zhengshuYxqDate = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
            DateTime offsetDate = DateUtil.offset(zhengshuYxqDate, DateField.YEAR, -3);
            TrxorderDetail detail;
            // 查询三年内的订单
            List<TrxorderDetail> trxorderDetailList = trxorderDetailService.list(new LambdaQueryWrapper<TrxorderDetail>()
                            .eq(TrxorderDetail::getIdCardNo, studentZhengshu.getRyid())
//                    .and(LambdaQueryWrapper -> LambdaQueryWrapper.eq(TrxorderDetail::getClearType, 0).eq(TrxorderDetail::getExamineType, 1).or().eq(TrxorderDetail::getExamineType, 1).eq(TrxorderDetail::getClearType, 1).eq(TrxorderDetail::getClearedType, 3))
                            .and(i -> i.and(j -> j.eq(TrxorderDetail::getClearType, 0).eq(TrxorderDetail::getExamineType, 1))
                                    .or(j -> j.eq(TrxorderDetail::getClearType, 1).eq(TrxorderDetail::getClearedType, 3))
                            )

                            .between(TrxorderDetail::getExamineTime, offsetDate, zhengshuYxqDate)
            );
            log.info("证书编号:{},三年内的订单:{}", code, trxorderDetailList);
            if (Func.isEmpty(trxorderDetailList)) {
                log.info("证书编号:{},三年内的订单为空", code);
                // 查询当前订单是否审核通过
                TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
                                .eq(TrxorderDetail::getZhengshuCode, code)
//                        .and(LambdaQueryWrapper -> LambdaQueryWrapper.eq(TrxorderDetail::getClearType, 0).eq(TrxorderDetail::getExamineType, 1).or().eq(TrxorderDetail::getExamineType, 1).eq(TrxorderDetail::getClearType, 1).eq(TrxorderDetail::getClearedType, 3))
                                .and(i -> i.and(j -> j.eq(TrxorderDetail::getClearType, 0).eq(TrxorderDetail::getExamineType, 1))
                                        .or(j -> j.eq(TrxorderDetail::getClearType, 1).eq(TrxorderDetail::getClearedType, 3))
                                )
                );
                log.info("证书编号:{},当前证书订单:{}", code, trxorderDetail);
                if (Func.isEmpty(trxorderDetail)) {
                    log.info("证书编号:{},三年内的订单为空,当前证书订单为空", code);
                    return ApiResult.error("继续教育未完成 或 暂未注册[2]");
                } else {
                    detail = trxorderDetail;
                }
            } else {
                detail = trxorderDetailList.get(0);
            }
            Student student = studentService.getById(detail.getUserId());
            log.info("学员信息:{}", student);
            // 用户是否被冻结
            if (Func.isEmpty(student) || student.getIsAvalible() == 1) {
                log.info("证书编号:{},学员信息为空 或 学员被冻结", code);
                return ApiResult.error("继续教育未完成 或 暂未注册[3]");
            }
            OrderDetailVO orderDetailVO = OrderDetailVO.builder()
                    .userId(student.getId()).userName(student.getRealName()).nickName("").email("")
                    .phonenumber(student.getMobile()).cardNumber(student.getIdCardNo()).sex(student.getSex() == 1 ? "0" : "1")
                    .createTime(detail.getCreateTime()).updateTime(detail.getLastUpdateTime()).remark("")
                    .finish(true).code(code).finishTime(detail.getExamineTime())
                    .build();
            log.info("orderDetailVO:{}", orderDetailVO);
            return ApiResult.success(orderDetailVO, "执行成功");
        } catch (Exception e) {
            log.info("detailInfo异常", e);
            return ApiResult.error("继续教育未完成 或 暂未注册[4]");
        }
    }

    /**
     * 更新活体时长
     *
     * @param mobile  手机号码
     * @param type    类型
     * @param authKey 授权码
     * @return ApiResult
     */
    @GetMapping("/updateHT")
    public ApiResult updateHT(@RequestParam String mobile, @RequestParam Integer type, @RequestParam String authKey) {
        if (!StrUtil.equals(authKey, AUTH_KEY)) {
            log.error("authKey错误");
            return ApiResult.error("authKey错误");
        }
        // type 1:观看视频 2:考试
        if (type == 1) {
            String key_tmp = "{}_DetectLivingFace_{}";
            String key_del_tmp = "{}_DetectLivingFace_Num_{}";
            studentService.list(new LambdaQueryWrapper<Student>().eq(Student::getMobile, mobile)).forEach(student -> {
                String key = StrUtil.format(key_tmp, student.getId(), student.getMobile());
                String key_del = StrUtil.format(key_del_tmp, student.getId(), student.getMobile());
                redisUtil.set(key, 0, 2, TimeUnit.DAYS);
                redisUtil.del(key_del);
            });
            return ApiResult.success("观看视频增加活体时长成功");
        }
        if (type == 2) {
            String key_tmp_ks = "{}_ksDetectLivingFace_{}";
            studentService.list(new LambdaQueryWrapper<Student>().eq(Student::getMobile, mobile)).forEach(student -> {
                String key = StrUtil.format(key_tmp_ks, student.getId(), student.getMobile());
                redisUtil.set(key, 0, 2, TimeUnit.DAYS);
            });
            return ApiResult.success("考试增加活体时长成功");
        }
        return ApiResult.error("type错误");
    }

    /**
     * 获取当天证书信息
     *
     * @param startTime 开始时间 yyyy-MM-dd HH:mm:ss
     * @param endTime   结束时间 yyyy-MM-dd HH:mm:ss
     * @return
     */
    @GetMapping("/cert_info_today")
    public R certInfoToday(@RequestParam String startTime, @RequestParam String endTime, @RequestParam String authKey) {
        getReqInfo();
        log.info("获取当天证书信息：startTime:{},endTime:{}", startTime, endTime);
        if (!StrUtil.equals(authKey, AUTH_KEY)) {
            log.error("authKey错误");
            return R.fail("authKey错误");
        }
        // 判断时间格式是否是当天
        DateTime start = DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss");
        DateTime end = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");
        if (!DateUtil.isSameDay(start, DateUtil.date()) || !DateUtil.isSameDay(end, DateUtil.date())) {
            return R.fail("时间格式错误，检查是否为当天日期");
        }
        LambdaQueryWrapper<StudentZhengshu> lqw = new LambdaQueryWrapper<>();
        // 只查询当天范围内的数据
//        lqw.between(StudentZhengshu::getCreatetime, startTime, endTime);
        lqw.between(StudentZhengshu::getCreatetime, "2024-06-07 12:00:00", "2024-06-07 15:00:00");
        List<StudentZhengshu> list = studentZhengshuService.list(lqw);
        List<CertInfoTodayVO> studentZhengshus = BeanUtil.copyToList(list, CertInfoTodayVO.class);

        // nc
        studentZhengshus.forEach(certInfoTodayVO -> {
            String idCardNo = certInfoTodayVO.getRyid();
            // 获取身份证后最四位 每一位数字+1，如果是9则变为0
            String lastFourDigits = idCardNo.substring(idCardNo.length() - 4);
            StringBuilder result = new StringBuilder();
            for (char ch : lastFourDigits.toCharArray()) {
                if (Character.isDigit(ch)) {
                    int digit = Character.getNumericValue(ch);
                    digit = (digit + 1) % 10; // increment digit and wrap around at 10
                    result.append(digit);
                }
            }
            String modifiedLastFourDigits = result.toString();
            String newIdCardNo = idCardNo.substring(0, idCardNo.length() - 4) + modifiedLastFourDigits;
            certInfoTodayVO.setRyid(newIdCardNo);
        });
        return R.data(studentZhengshus);
    }

    private void getReqInfo() {
        HttpServletRequest request = WebUtil.getRequest();
        String agent = WebUtil.getRequest().getHeader("User-Agent");
        //解析agent字符串
        UserAgent userAgent = UserAgent.parseUserAgentString(agent);
        //获取浏览器对象
        Browser browser = userAgent.getBrowser();
        //获取操作系统对象
        OperatingSystem operatingSystem = userAgent.getOperatingSystem();
        log.info("甘肃省住房和城乡建设厅查询学员课时接口");
        log.info("浏览器名:" + browser.getName());
        log.info("浏览器类型:" + browser.getBrowserType());
        log.info("浏览器家族:" + browser.getGroup());
        log.info("浏览器生产厂商:" + browser.getManufacturer());
        log.info("浏览器使用的渲染引擎:" + browser.getRenderingEngine());
        log.info("浏览器版本:" + userAgent.getBrowserVersion());
        log.info("操作系统名:" + operatingSystem.getName());
        log.info("访问设备类型:" + operatingSystem.getDeviceType());
        log.info("操作系统家族:" + operatingSystem.getGroup());
        log.info("操作系统生产厂商:" + operatingSystem.getManufacturer());
        String ip = WebUtil.getIP(request);
        log.info("IP地址:" + ip);
    }

    /**
     * 查询企业信息
     */
    @GetMapping("/getQyxydmByQymc")
    public R getQyxydmByQymc(@RequestParam String qymc, @RequestParam int minLength, @RequestParam int maxLimit) {
        try {
            List<StudentZhengshu> qyxydmList = studentZhengshuService.findSsqytyshxydmByQymcLike(qymc, minLength, maxLimit);
            return R.data(qyxydmList);
        } catch (Exception e) {
            log.error("查询企业信息异常", e);
            return R.fail("查询失败");
        }
    }
}
