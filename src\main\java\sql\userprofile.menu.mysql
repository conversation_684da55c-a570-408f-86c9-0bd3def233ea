INSERT INTO `crazy_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `remark`, `is_deleted`)
VALUES (0, 'userprofile', '', 'menu', '/partyLogin/userprofile', NULL, 1, 1, 0, 1, NULL, 0);
set @parentid = (SELECT LAST_INSERT_ID());
INSERT INTO `crazy_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `remark`, `is_deleted`)
VALUES (@parentid, 'userprofile_add', '新增', 'add', '/partyLogin/userprofile/add', 'plus', 1, 2, 1, 1, NULL, 0);
INSERT INTO `crazy_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `remark`, `is_deleted`)
VALUES (@parentid, 'userprofile_edit', '修改', 'edit', '/partyLogin/userprofile/edit', 'form', 2, 2, 1, 2, NULL, 0);
INSERT INTO `crazy_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `remark`, `is_deleted`)
VALUES (@parentid, 'userprofile_delete', '删除', 'delete', '/api/partyLogin/userprofile/remove', 'delete', 3, 2, 1, 3, NULL, 0);
INSERT INTO `crazy_menu`(`parent_id`, `code`, `name`, `alias`, `path`, `source`, `sort`, `category`, `action`, `is_open`, `remark`, `is_deleted`)
VALUES (@parentid, 'userprofile_view', '查看', 'view', '/partyLogin/userprofile/view', 'file-text', 4, 2, 1, 2, NULL, 0);
