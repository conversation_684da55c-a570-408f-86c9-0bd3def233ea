package org.springcrazy.modules.slry.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.mapper.StudentZhengshuMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class StudentZhengshuService extends ServiceImpl<StudentZhengshuMapper, StudentZhengshu> {

    @Autowired
    private StudentZhengshuMapper studentZhengshuMapper;

    public Integer countByQymc(String qymc) {
        return studentZhengshuMapper.countByQymc(qymc);
    }

    public Integer countByRyid(String ryid) {
        return studentZhengshuMapper.countByRyid(ryid);
    }

    public List<String> findRyidByQymc(String qymc) {
        return studentZhengshuMapper.findRyidByQymc(qymc);
    }

    public String findQymcByNewzsbh(String newzsbh) {
        return studentZhengshuMapper.findQymcByNewzsbh(newzsbh);
    }

    public StudentZhengshu findByRyid(String ryid) {
        return studentZhengshuMapper.findByRyid(ryid);
    }

    public Integer countDisqymc() {
        return studentZhengshuMapper.countDisqymc();
    }

    public StudentZhengshu getOneByNewzsbh(String zsbh) {
        return studentZhengshuMapper.getOneByNewzsbh(zsbh);
    }

    public List<StudentZhengshu> findSsqytyshxydmByQymcLike(String likeQymc, int minLength, int maxLimit) {
        return studentZhengshuMapper.findSsqytyshxydmByQymcLike(likeQymc, minLength, maxLimit);
    }
}
