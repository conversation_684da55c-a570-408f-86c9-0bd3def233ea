<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.AreaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="areaResultMap" type="org.springcrazy.modules.web.entity.Area">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="parent" property="parent"/>
        <result column="arealevel" property="arealevel"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springcrazy.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>


    <select id="selectAreaPage" resultMap="areaResultMap">
        select * from web_area where is_deleted = 0
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent as parent_id, name as title, id as 'value', id as 'key' from web_area where parent = 620000 or id = 620000 and status = 1
    </select>

</mapper>
