package org.springcrazy.modules.agent.service.impl;

import org.springcrazy.modules.agent.entity.AgentAttendance;
import org.springcrazy.modules.agent.mapper.AgentAttendanceMapper;
import org.springcrazy.modules.agent.service.IAgentAttendanceService;
import org.springcrazy.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 考勤机设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Service
public class AgentAttendanceServiceImpl extends BaseServiceImpl<AgentAttendanceMapper, AgentAttendance> implements IAgentAttendanceService {

}
