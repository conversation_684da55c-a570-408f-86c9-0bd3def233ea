package org.springcrazy.modules.edu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课程播放记录详情(学习记录详情)
 * @TableName edu_course_history_detail
 */
@TableName(value ="edu_course_history_detail")
@Data
public class EduCourseHistoryDetail implements Serializable {
    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * edu_course_studyhistory外键
     */
    private Integer studyhistoryId;

    /**
     * 开始时间（yyyy-MM-ddHHmmss）
     */
    private String startTime;

    /**
     * 结束时间（yyyy-MM-ddHHmmss）
     */
    private String endTime;

    /**
     * 图片结果集
     */
    @TableField(exist = false)
    private List<EduCourseHistoryDetailPicture> eduCourseHistoryDetailPictureList;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 图片结果集
     */
    @TableField(exist = false)
    private List<String> pictureList;
}