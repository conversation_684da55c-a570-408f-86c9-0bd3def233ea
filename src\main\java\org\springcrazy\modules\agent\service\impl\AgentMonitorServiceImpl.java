package org.springcrazy.modules.agent.service.impl;

import org.springcrazy.modules.agent.entity.AgentMonitor;
import org.springcrazy.modules.agent.mapper.AgentMonitorMapper;
import org.springcrazy.modules.agent.service.IAgentMonitorService;
import org.springcrazy.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 监控设备 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Service
public class AgentMonitorServiceImpl extends BaseServiceImpl<AgentMonitorMapper, AgentMonitor> implements IAgentMonitorService {

}
