server:
  #服务端口
  port: 81
tencentcloudapi:
  # 你的 secretId
  secretId: AKIDmEcu3ziGbjgQxHQNOCnCPO9g0B92kJis
  # 你的 secretKey
  secretKey: hCVbUyIPJ2YrxsZ6QaYIpAIDxUovpX1G
  # 请求官方地址 不变
  endpoint: iai.tencentcloudapi.com
  # 地域 可不变
  region: ap-shanghai
  # 两张图片中人脸的相似度分数。
  # 若需要验证两张图片中人脸是否为同一人，3.0版本误识率千分之一对应分数为40分，误识率万分之一对应分数为50分，误识率十万分之一对应分数为60分。 一般超过50分则可认定为同一人。
  score: 55
  # 人脸识别服务所用的算法模型版本。
  facemodelversion: 3.0
alifaceapi:
  # 你的 secretId
  secretId: LTAI5tFJXjzZdLAU8iA8Gv1V
  # 你的 secretKey
  secretKey: ******************************
  # 质量分阈值，对人脸图片进行质量分析判断，若某张人脸质量分小于该阈值，则会在返回结果上增加MessageTips说明。
  qualityscorethreshold: 0.0
  # 两张图片中的最大人脸属于同一个人的置信度，取值范围0~100。供参考的三个阈值是61，69和75，分别对应千分之一，万分之一和十万分之一误识率。阈值设置越高，误识率越低，通过率也越低，对安全性要求越高的场合，可以设置更高的阈值。如果某张图片中没有人脸，则报错误信息。
  confidence: 65
# 人脸识别接口类型
faceapi:
  #type: tencent ali
  type: ali
# 设置视频间隔请求时长
video:
  # 请求间隔时间(秒)
  interval: 305
exam:
  count: 3
#数据源配置
spring:
  redis:
    #redis 单机环境配置
    host: 127.0.0.1
    port: 6379
    password:
    database: 0
    ssl: false
  datasource:
    url: *******************************************************************************************************************************************************************************
    username: slry
    password: G3ekW2r3RmTi8XKd
    driver-class-name: com.mysql.cj.jdbc.Driver
    # druid相关配置
    druid:
      # 初始化 最小 最大
      initial-size: 5
      min-idle: 5
      max-active: 110
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 'x'
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      filter:
        stat:
          log-slow-sql: false
          slow-sql-millis: 1000

#crazy配置
crazy:
  prop:
    #上传文件访问域名，用于文件上传外网访问地址的拼接，文件上传后自动拼接此前缀
    #upload-domain: http://127.0.0.1:1999
    upload-domain: https://test.agry.gsjtsz.com
    #开启远程上传路径
    remote-mode: true
    # 设置文件上传路径，上传的静态资源都上传到了此路径下
    #remote-path: F:\school\resources
    remote-path: /mnt/disk/slry/resources
    # 系统的站点域名，用于系统中，支付宝，微信调用时的回调地址，后面要带上api结尾
    domain: https://edu.gsjtsz.com/api
    #domain: http://************:1888/api
    #前台站点域名(用于学员端支付，第三方登录时返回访问地址，拼接地址前缀时使用，必须配置)
    front-domain: https://edu.gsjtsz.com
    #front-domain: http://************:1888
    # 上传文件类型限制
    upload-file-type: jpg,png,jpeg,JPG,PNG,JPEG,docx,doc,xls,xlsx,ico
    # vue前台 icon上传路径
    #front-icon-path: F:\school\resources\upload\favicon.ico
    front-icon-path: /mnt/disk/school/resources/upload
    # vue后台管理端 icon上传路径
    admin-icon-path: /mnt/disk/school/resources/upload
    # redis 缓存前缀
    redis-prefix: school_mater_1_1_5_lqx
    # 视频云服务域名(视频云地址，如果使用了配置，如果不使用不配置也没事)
    videoyun-domain: http://videoyun.tongzhoushidai.com/api
  mybatis-plus:
    sql-log: true
aliyun:
  accessKeyId: LTAI5tALPXrZ9VZH4PXGXKpv
  accessKeySecret: ******************************

face:
  #local-url: F:/school/resources/upload/facePicture/
  #picture-url: http://192.168.1.25:1999/upload/facePicture/
  local-url: /mnt/disk/slry/resources/upload/facePicture
  picture-url: https://test.agry.gsjtsz.com/upload/facePicture/

#移动视频相关
yd:
  uid: 300000183
  secretId: 95aab74b92943bdfe35d2d1b0219a35e
  uname: ydy_8i9Ekc
  passwd: D)i||ZYt5T8K|!k7
  secretKey: 01850ac378ea1886a9ec794b86af3fff
  getUrl: https://mgspy-api-dongguan.cmecloud.cn/vod2/v1/getUrl
  postUrl: https://mgspy-api-dongguan.cmecloud.cn/vod2/v0/getVideoList
  ydyDomain: http://dg5sy-vodcdn.migucloud.com
  vagentUrl: https://agry.gsjtsz.com/ydyVurl
# shumaiapi.com
shumai:
  url: https://api.shumaidata.com/v4/face_id_card/compare
  appid: Ua4CmYvi0YajAEn1XNX7DYxW0U9YgbM2
  appSecurity: YMPdmQCcDA8TJcg09ldXiIyZqipdI74d
  successScore: 0.45
#配置日志地址
logging:
  config: classpath:log/logback_test.xml
slry:
  # 获取证书接口请求参数
  zhengshu:
    appKey: c9406731-50bc-4e23-b3f5-b7e7d3093875
    sign: UZRnjTyW563ssBypsGQWfA==
    time: 1650591495000
    gssecretKey: e0d6416ff61242738af58c01635a903a
  # 获取证书接口
#  api-zhengshu: https://zhzw.zjt.gansu.gov.cn/dataexchangeserver/GSApi/getThreeTypesUserInfo
  api-zhengshu: https://zhzw.zjt.gansu.gov.cn/dataexchangeserver/GSApi/jxjy_getThreeTypesUserInfo_new
  # 查询旧系统学员课时审核状态接口
  api-old-examine: http://*************:8002/xsOnlineCourseUser
  needWatchTime: 79200 # 需要观看的课时时长 24节课时（每节课时45分钟）
  classHourTime: 55 # 每节课时的时长（分钟）