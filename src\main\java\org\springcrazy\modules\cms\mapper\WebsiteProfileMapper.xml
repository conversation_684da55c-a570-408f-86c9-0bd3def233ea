<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.cms.mapper.WebsiteProfileMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="websiteProfileResultMap" type="org.springcrazy.modules.cms.entity.WebsiteProfile">
        <id column="id" property="id"/>
        <result column="config_type" property="configType"/>
        <result column="form_type" property="formType"/>
        <result column="key" property="key"/>
        <result column="value" property="value"/>
        <result column="explain" property="explain"/>
        <result column="tip" property="tip"/>
        <result column="sort" property="sort"/>
        <result column="agent_id" property="agentId"/>
    </resultMap>


    <select id="selectWebsiteProfilePage" resultMap="websiteProfileResultMap">
        select * from cms_website_profile where is_deleted = 0
    </select>

</mapper>
