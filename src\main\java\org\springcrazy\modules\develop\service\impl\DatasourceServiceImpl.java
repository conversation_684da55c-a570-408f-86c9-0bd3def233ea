
package org.springcrazy.modules.develop.service.impl;

import org.springcrazy.core.mp.base.BaseServiceImpl;
import org.springcrazy.modules.develop.entity.Datasource;
import org.springcrazy.modules.develop.mapper.DatasourceMapper;
import org.springcrazy.modules.develop.service.IDatasourceService;
import org.springframework.stereotype.Service;

/**
 * 数据源配置表 服务实现类
 *

 */
@Service
public class DatasourceServiceImpl extends BaseServiceImpl<DatasourceMapper, Datasource> implements IDatasourceService {

}
