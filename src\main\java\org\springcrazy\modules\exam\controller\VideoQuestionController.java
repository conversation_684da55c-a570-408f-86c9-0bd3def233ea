package org.springcrazy.modules.exam.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.core.boot.ctrl.CrazyController;
import org.springcrazy.core.mp.support.Condition;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.exam.entity.VideoQuestion;
import org.springcrazy.modules.exam.service.IVideoQuestionService;
import org.springcrazy.modules.exam.vo.VideoQuestionVO;
import org.springcrazy.modules.exam.wrapper.VideoQuestionWrapper;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 视频课程对应弹出题目 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/exam/videoquestion")
public class VideoQuestionController extends CrazyController {

    private IVideoQuestionService videoQuestionService;


    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入VideoQuestion")
    public R<VideoQuestionVO> detail(VideoQuestion VideoQuestion) {
        VideoQuestion detail = videoQuestionService.getOne(Condition.getQueryWrapper(VideoQuestion));
        return R.data(VideoQuestionWrapper.build().entityVO(detail));
    }

    /**
     * 分页 考试试题收藏表
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入VideoQuestion")
    public R<IPage<VideoQuestionVO>> list(VideoQuestion VideoQuestion, Query query) {
        IPage<VideoQuestion> pages = videoQuestionService.page(Condition.getPage(query), Condition.getQueryWrapper(VideoQuestion));
        return R.data(VideoQuestionWrapper.build().pageVO(pages));
    }


    /**
     * 自定义分页 考试试题收藏表
     */
    @GetMapping("/page")
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "分页", notes = "传入VideoQuestion")
    public R<IPage<VideoQuestionVO>> page(VideoQuestionVO VideoQuestion, Query query) {
        IPage<VideoQuestionVO> pages = videoQuestionService.selectVideoQuestionPage(Condition.getPage(query), VideoQuestion);
        return R.data(pages);
    }

    /**
     * 新增 考试试题收藏表
     */
    @PostMapping("/save")
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "新增", notes = "传入VideoQuestion")
    public R save(@Valid @RequestBody VideoQuestion VideoQuestion) {
        return R.status(videoQuestionService.save(VideoQuestion));
    }

    /**
     * 修改 考试试题收藏表
     */
    @PostMapping("/update")
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "修改", notes = "传入VideoQuestion")
    public R update(@Valid @RequestBody VideoQuestion VideoQuestion) {
        return R.status(videoQuestionService.updateById(VideoQuestion));
    }

    /**
     * 新增或修改 考试试题收藏表
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入VideoQuestion")
    public R submit(@Valid @RequestBody VideoQuestion VideoQuestion) {
        return R.status(videoQuestionService.saveOrUpdate(VideoQuestion));
    }


    /**
     * 删除 考试试题收藏表
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        videoQuestionService.removeByIds(Func.toIntList(ids));
        return R.success("删除成功");
    }

}

