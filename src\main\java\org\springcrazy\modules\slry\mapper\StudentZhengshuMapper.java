package org.springcrazy.modules.slry.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springcrazy.modules.slry.entity.StudentZhengshu;

import java.util.List;

public interface StudentZhengshuMapper extends BaseMapper<StudentZhengshu> {

    Integer countByQymc(@Param("qymc") String qymc);

    Integer countByRyid(@Param("ryid") String ryid);

    List<String> findRyidByQymc(@Param("qymc") String qymc);

    String findQymcByNewzsbh(@Param("newzsbh") String newzsbh);

    StudentZhengshu findByRyid(@Param("ryid") String ryid);

    Integer countDisqymc();

    StudentZhengshu getOneByNewzsbh(@Param("newzsbh") String zsbh);

    List<StudentZhengshu> findSsqytyshxydmByQymcLike(
            @Param("likeQymc") String likeQymc,
            @Param("minLength") int minLength,
            @Param("maxLimit") int maxLimit
    );
}