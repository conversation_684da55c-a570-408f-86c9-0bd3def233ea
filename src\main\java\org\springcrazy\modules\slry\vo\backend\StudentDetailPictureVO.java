package org.springcrazy.modules.slry.vo.backend;

import lombok.Data;

@Data
public class StudentDetailPictureVO {

    /**
     * 开始时间（yyyy-MM-ddHHmmss）
     */
    private String startTime;

    /**
     * 结束时间（yyyy-MM-ddHHmmss）
     */
    private String endTime;

    /**
     * 图片结果集
     */
    private String pictures;

    /**
     * 地址
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    /**
     * 格式化后的时间
     */
    private String formattedDatetime;

}
