<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.slry.mapper.StudentMemberMapper">
    <resultMap id="BaseResultMap" type="org.springcrazy.modules.slry.entity.StudentMember">
        <!--@mbg.generated-->
        <!--@Table edu_student_member-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="media_type" jdbcType="INTEGER" property="mediaType"/>
        <result column="qymc" jdbcType="VARCHAR" property="qymc"/>
        <result column="qytyshxydm" jdbcType="VARCHAR" property="qytyshxydm"/>
        <result column="payment_type" jdbcType="INTEGER" property="paymentType"/>
        <result column="agent_name" jdbcType="VARCHAR" property="agentName"/>
        <result column="newzsbh" jdbcType="VARCHAR" property="newzsbh"/>
        <result column="fzrq" jdbcType="VARCHAR" property="fzrq"/>
        <result column="zsyxq" jdbcType="VARCHAR" property="zsyxq"/>
        <result column="rylb" jdbcType="VARCHAR" property="rylb"/>
        <result column="zxtype" jdbcType="VARCHAR" property="zxtype"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="is_deleted" jdbcType="INTEGER" property="isDeleted"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, real_name, id_card_no, mobile, password, media_type, qymc, qytyshxydm, payment_type,
        agent_name, newzsbh, fzrq, zsyxq, rylb, zxtype, create_time, update_time, is_deleted
    </sql>
    <select id="countByidCardNoAndNewzsbh" resultType="java.lang.Integer">
        select count(1)
        from edu_student_member
        where
        id_card_no =  #{idCardNo,jdbcType=VARCHAR}
        and newzsbh = #{newzsbh,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>
    <select id="getZhengshuByZxtypeAndIdCardNoAndZsyxq" resultType="java.util.Map">
        SELECT
        *
        FROM
        edu_student_zhengshu
        WHERE
        RYID = #{idCardNo,jdbcType=VARCHAR}
        AND ZXTYPE = #{zxtype,jdbcType=VARCHAR}
        AND SSQYTYSHXYDM = #{qytyshxydm,jdbcType=VARCHAR}
        AND STATUS = 1
        LIMIT 1
    </select>
    <select id="countOnlineByQYTYSHXYDM" resultType="java.lang.Integer">
        select count(1)
        from edu_student_member
        where
        payment_type = 1
        and is_deleted = 0
        AND qytyshxydm = #{qytyshxydm,jdbcType=VARCHAR}
    </select>
    <select id="exportMember" resultType="org.springcrazy.modules.system.excel.StudentMemberExcel">
        select
        real_name realName,
        id_card_no idCardNo,
        zxtype,
        mobile,
        (CASE media_type WHEN '1' THEN '视频' ELSE '图片' END) mediaType,
        zsyxq zsyxq,
        qymc,
        qytyshxydm,
        (CASE payment_type WHEN '1' THEN '是' ELSE '否' END) paymentType,
        agent_name agentName,
        date_format(create_time, '%Y-%m-%d %H:%i:%s') createTime
        from edu_student_member ${ew.customSqlSegment}
    </select>
</mapper>