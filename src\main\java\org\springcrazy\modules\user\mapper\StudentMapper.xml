<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.user.mapper.StudentMapper">
    <resultMap id="studentResultMap" type="org.springcrazy.modules.user.entity.Student">
        <id column="id" property="id"/>
        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="password" property="password"/>
        <result column="user_name" property="userName"/>
        <result column="show_name" property="showName"/>
        <result column="sex" property="sex"/>
        <result column="nation" property="nation"/>
        <result column="age" property="age"/>
        <result column="create_time" property="createTime"/>
        <result column="is_avalible" property="isAvalible"/>
        <result column="head_img" property="headImg"/>
        <result column="banner_url" property="bannerUrl"/>
        <result column="msg_num" property="msgNum"/>
        <result column="sys_msg_num" property="sysMsgNum"/>
        <result column="last_system_time" property="lastSystemTime"/>
        <result column="login_account" property="loginAccount"/>
        <result column="register_from" property="registerFrom"/>
        <result column="invitationCode" property="invitationCode"/>
        <result column="realName" property="realName"/>
        <result column="idCardNo" property="idCardNo"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="school" property="school"/>
        <result column="subject" property="subject"/>
        <result column="userInfo" property="userInfo"/>
        <result column="weibo" property="weibo"/>
        <result column="wechat" property="wechat"/>
        <result column="qq" property="qq"/>
        <result column="weiboname" property="weiboname"/>
        <result column="wechatname" property="wechatname"/>
        <result column="qqname" property="qqname"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="rylb" property="rylb"/>
        <result column="qymc" property="qymc"/>
        <result column="newzsbh" property="newzsbh"/>
        <result column="fzrq" property="fzrq"/>
        <result column="zsyxq" property="zsyxq"/>
    </resultMap>


    <select id="selectStudentPage" resultMap="studentResultMap">
        select * from edu_student where is_deleted = 0
    </select>
    <select id="selectStudentLogin" resultMap="studentResultMap">
        select * from edu_student where is_deleted=0
        <if test="loginAccount !=null and loginAccount !='' ">
            and (login_account = #{loginAccount} or
            mobile = #{loginAccount} or
            email = #{loginAccount} or
            user_name = #{loginAccount} )
        </if>
        <if test="password !=null and password !='' ">
            and password = #{password}
        </if>
        limit 1
    </select>

    <select id="checkStudent" resultMap="studentResultMap">
        select * from edu_student where 1=1 and is_deleted=0
        and (
        <if test="mobile !=null and mobile !='' ">
           mobile = #{mobile}
        </if>
        <if test="email !=null and email !='' ">
            or email = #{email}
        </if>
        <if test="userName !=null and userName !='' ">
            or user_name = #{userName}
        </if>
        )
        limit 1
    </select>

    <select id="userInfos" resultMap="studentResultMap" parameterType="int">
        select * from edu_student where id = #{email}
        limit 1
    </select>

    <select id="getStudent" resultMap="studentResultMap" parameterType="int">
        select * from edu_student where id = #{value}
    </select>
    <select id="checkStudentEmail" resultMap="studentResultMap">
        select * from edu_student where email = #{email} and is_deleted = 0
        limit 1
    </select>
    <select id="checkStudentMobile" resultMap="studentResultMap">
        select * from edu_student where mobile = #{mobile} and is_deleted = 0
        limit 1
    </select>
    <select id="checkStudentUserName" resultMap="studentResultMap">
        select * from edu_student where user_name = #{userName} and is_deleted = 0
        limit 1
    </select>

    <select id="checkStudentUserIdNumber" resultMap="studentResultMap">
        select * from edu_student where idCardNo = #{idCardNo} and is_deleted = 0
        limit 1
    </select>

    <select id="checkStudentNew" resultMap="studentResultMap">
        select * from edu_student
        <where>
            <if test="mobile !=null and mobile !='' ">
                or mobile = #{mobile}
            </if>
            <if test="email !=null and email !='' ">
                or email = #{email}
            </if>
            <if test="userName !=null and userName !='' ">
                or user_name = #{userName}
            </if>
            <if test="id !=null and id !='' ">
                or id = #{id}
            </if>
            and is_deleted=0
        </where>
        limit 1
    </select>

    <select id="exportUser" resultType="org.springcrazy.modules.system.excel.ExportStudentExcel">
        SELECT * FROM edu_student ${ew.customSqlSegment} and is_deleted=0
    </select>


    <update id="updateAvalible" >
        update edu_student set is_avalible = 1
        where
            <if test="key !=null and key !='' ">
                ( mobile = #{key} or
                email = #{key} or
                user_name = #{key} )
            </if>
            and is_deleted = 0
    </update>


    <select id="selectByAll" resultMap="studentResultMap">
        select
            a.id,a.mobile,a.email,a.`password`,a.user_name,a.show_name,a.sex,a.nation,a.age,a.create_time,a.is_avalible,a.head_img,
            a.banner_url,a.msg_num,a.sys_msg_num,a.last_system_time,a.login_account,a.register_from,a.invitationCode,a.realName,
            a.idCardNo,a.province,a.city,a.area,a.school,a.subject,a.userInfo,a.agent_id,a.id_card_photo,a.weibo,a.wechat,a.qq,
            a.weiboname,a.wechatname,a.qqname,a.open_id,a.wechat_name,a.is_deleted,a.is_real,a.profile_photo,
            a.zhengshu_info,a.is_avalible_msg,a.sync_picture_status,a.company_name,
            b.rylb, b.qymc, b.newzsbh, b.fzrq, b.zsyxq
        from edu_student a left join edu_student_zhengshu b
        on a.idCardNo = b.ryid
        ${ew.customSqlSegment}
    </select>


<!--    <if test="agentId != null">-->
<!--        and null=#{agentId}-->
<!--    </if>-->
<!--    <if test="idCardPhoto != null">-->
<!--        and null=#{idCardPhoto}-->
<!--    </if>-->
<!--    <if test="openId != null">-->
<!--        and null=#{openId}-->
<!--    </if>-->
<!--    <if test="isReal != null">-->
<!--        and null=#{isReal}-->
<!--    </if>-->
<!--    <if test="profilePhoto != null">-->
<!--        and null=#{profilePhoto}-->
<!--    </if>-->
<!--    <if test="zhengshuInfo != null">-->
<!--        and null=#{zhengshuInfo}-->
<!--    </if>-->
<!--    <if test="isAvalibleMsg != null">-->
<!--        and null=#{isAvalibleMsg}-->
<!--    </if>-->
<!--    <if test="syncPictureStatus != null">-->
<!--        and null=#{syncPictureStatus}-->
<!--    </if>-->
<!--    <if test="companyName != null">-->
<!--        and null=#{companyName}-->
<!--    </if>-->

</mapper>
