package org.springcrazy.modules.edu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetail;
import org.springcrazy.modules.edu.entity.EduCourseHistoryDetailPicture;
import org.springcrazy.modules.edu.mapper.EduCourseHistoryDetailMapper;
import org.springcrazy.modules.edu.mapper.EduCourseHistoryDetailPictureMapper;
import org.springcrazy.modules.edu.service.EduCourseHistoryDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【edu_course_history_detail(课程播放记录详情(学习记录详情))】的数据库操作Service实现
* @createDate 2023-06-05 11:33:48
*/
@Service
public class EduCourseHistoryDetailServiceImpl extends ServiceImpl<EduCourseHistoryDetailMapper, EduCourseHistoryDetail>
    implements EduCourseHistoryDetailService {

    @Resource
    private EduCourseHistoryDetailMapper eduCourseHistoryDetailMapper;

    @Resource
    private EduCourseHistoryDetailPictureMapper eduCourseHistoryDetailPictureMapper;


    /**
     * 查询学习记录详情及照片信息
     * @param historyId 学习记录id
     * @return
     */
    @Override
    public R<?> getDetail(Integer historyId) {
        // 根据学习记录id获取学习记录详情
        List<EduCourseHistoryDetail> eduCourseHistoryDetails = eduCourseHistoryDetailMapper
                .selectList(new LambdaQueryWrapper<EduCourseHistoryDetail>()
                .eq(EduCourseHistoryDetail::getStudyhistoryId, historyId));
        if (eduCourseHistoryDetails.size() > 0) {
            // 拿到所有学习记录详情id
            List<Integer> detailIds = eduCourseHistoryDetails.stream().map(EduCourseHistoryDetail::getId).collect(Collectors.toList());
            // 根据详情记录id获取所有抓拍图片信息
            List<EduCourseHistoryDetailPicture> eduCourseHistoryDetailPicturesList = eduCourseHistoryDetailPictureMapper
                    .selectList(new LambdaQueryWrapper<EduCourseHistoryDetailPicture>()
                    .in(EduCourseHistoryDetailPicture::getHistoryDetailId, detailIds));
            if (!(eduCourseHistoryDetailPicturesList.size() > 0)) {
                return R.data(eduCourseHistoryDetails);
            }
            // 以detailId作为key将eduCourseHistoryDetailPicturesList转为map
            Map<Integer, List<EduCourseHistoryDetailPicture>> pictureMap = eduCourseHistoryDetailPicturesList.stream()
                    .collect(Collectors.groupingBy(EduCourseHistoryDetailPicture::getHistoryDetailId));
//            Set<Integer> detailList = pictureMap.keySet();
            List<Integer> detailList = eduCourseHistoryDetails.stream().map(EduCourseHistoryDetail::getId).collect(Collectors.toList());
            // 循环嵌套数据结构
            List<EduCourseHistoryDetail> list = new ArrayList<>();
            List<EduCourseHistoryDetail> removeList = new ArrayList<>();
            eduCourseHistoryDetails.forEach(e -> {
                //处理未更新到的学习结束时间
                if (StringUtils.isEmpty(e.getEndTime())) {
                    list.add(e);
                }
                // 循环判断如果key相同则装配map数据
                detailList.forEach(d -> {
                    if (d == e.getId()) {
                        e.setEduCourseHistoryDetailPictureList(pictureMap.get(d));
                    }
                });

                List<EduCourseHistoryDetailPicture> picList = e.getEduCourseHistoryDetailPictureList();
                if(picList != null && picList.size() > 0){
                    List<String> urlList =new ArrayList<>();
                    picList.forEach(d -> {
                        if (StringUtils.isNotEmpty(d.getUrl())) {
                            urlList.add(d.getUrl());
                        }
                    });
                    e.setPictureList(urlList);
                }
            });
            eduCourseHistoryDetails.removeAll(removeList);
            if (list.size() > 0) {
                List<Integer> idList = list.stream().map(EduCourseHistoryDetail::getId).collect(Collectors.toList());
                eduCourseHistoryDetailMapper.deleteBatchIds(idList);
            }

            return R.data(eduCourseHistoryDetails);
        }
        return R.data(new ArrayList<>());
    }
}




