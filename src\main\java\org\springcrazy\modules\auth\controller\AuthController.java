
package org.springcrazy.modules.auth.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.wf.captcha.SpecCaptcha;
import io.jsonwebtoken.Claims;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springcrazy.common.cache.CacheNames;
import org.springcrazy.core.log.annotation.ApiLog;
import org.springcrazy.core.secure.AuthInfo;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.constant.CrazyConstant;
import org.springcrazy.core.tool.support.Kv;
import org.springcrazy.core.tool.utils.DateUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.core.tool.utils.RedisUtil;
import org.springcrazy.core.tool.utils.WebUtil;
import org.springcrazy.modules.auth.enums.UserEnum;
import org.springcrazy.modules.auth.granter.*;
import org.springcrazy.modules.auth.service.OnlineUserService;
import org.springcrazy.modules.auth.utils.TokenUtil;
import org.springcrazy.modules.cms.entity.WebsiteProfile;
import org.springcrazy.modules.system.entity.UserInfo;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 认证模块
 */
@RestController
@AllArgsConstructor
@RequestMapping("crazy-auth")
@Api(value = "用户授权认证", tags = "授权接口")
@Slf4j
public class AuthController {

    private RedisUtil redisUtil;
    private OnlineUserService onlineUserService;
    private IStudentService studentService;

    public static final String CHECK_IS_FACE_LOGIN = "checkIsFaceLogin";
    public static final String CHECK_IS_FACE_REG = "checkIsFaceReg";
    public static final String CHECK_IS_HEAD_GESTURE = "checkIsHeadGesture";

    private static final String[] CITIES = {"兰州", "天水", "白银", "金昌", "嘉峪关", "武威", "庆阳", "平凉", "张掖", "酒泉", "定西", "陇南", "临夏", "甘南", "甘肃"};

    @ApiLog("登录用户获取token")
    @PostMapping("token")
    @ApiOperation(value = "获取认证token", notes = "传入租户ID:tenantId,账号:account,密码:password")
    public R<AuthInfo> token(@ApiParam(value = "授权类型", required = true) @RequestParam(defaultValue = "password", required = false) String grantType,
                             @ApiParam(value = "刷新令牌") @RequestParam(required = false) String refreshToken,
                             @ApiParam(value = "令牌") @RequestParam(required = false) String token,
                             @ApiParam(value = "租户ID", required = true) @RequestParam(defaultValue = "000000", required = false) String tenantId,
                             @ApiParam(value = "账号") @RequestParam(required = false) String account,
                             @ApiParam(value = "密码") @RequestParam(required = false) String password,
                             @ApiParam(value = "用户人脸照片Base64") @RequestParam(required = false) String userFaceImage,
                             @ApiParam(value = "小程序登录code") @RequestParam(required = false) String wxCode,
                             @ApiParam(value = "小程序登录sign") @RequestParam(required = false) String wxSign,
                             @ApiParam(value = "小程序登录加密key的vision") @RequestParam(required = false) Integer wxVersion,
                             HttpServletRequest request) {

        String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);
        //学员登录
        if (userType.equals(UserEnum.STUDENT.getName())) {
            //获取用户代理
            String agent = WebUtil.getRequest().getHeader("User-Agent");
            //判断是否需要客户端强制登录
            if (Func.equals(TokenUtil.CLENT_FORCECLENT_LOGIN, "1")) {
                //判断用户是否首次登录
                if (Func.isNotEmpty(Convert.toStr(account, ""))) {
                    // 获取referer 判断是否是来自微信小程序的请求
                    String referer = WebUtil.getRequest().getHeader("referer");
                    boolean isWechat = StrUtil.contains(referer, TokenUtil.SERVER_WECHAT);
                    // 2.不需要校验用户组
                    Student student = new Student();
                    student.setLoginAccount(account);
                    student = studentService.selectStudentLogin(student);
                    if (ObjectUtil.isEmpty(student)) {
                        return R.fail(isWechat ? TokenUtil.USER_NOT_FOUND_WECHAT : TokenUtil.USER_NOT_FOUND);
                    }
                    boolean isWhiteList = studentService.checkWhiteList(student);
                    if (isWhiteList) {
                        // 如果不是微信小程序请求则返回错误
                        if (!isWechat) {
                            log.info(TokenUtil.IILLEGAL_ACCESS);
                            return R.fail(888, TokenUtil.IILLEGAL_ACCESS);
                        }
                    } else {
                        // 获取浏览器版本，浏览器版本不是指定版本，则返回错误
                        UserAgent ua = UserAgentUtil.parse(agent);
                        String version = ua.getVersion();
                        boolean isVersion = version.equals(TokenUtil.BROWSER_VERSION);
                        if (!isVersion) {
                            log.info(TokenUtil.IILLEGAL_ACCESS);
                            return R.fail(888, TokenUtil.IILLEGAL_ACCESS);
                        }
                    }
                }
                // 判断是否为黑名单Ip
                String ip = WebUtil.getIP(request);
                Object blackIpConfig = redisUtil.get(WebsiteProfile.BLACK_IP);
                if (Func.isNotEmpty(blackIpConfig)) {
                    String blackIp = Convert.toStr(blackIpConfig);
                    String[] blackIpArray = blackIp.split(",");
                    if (ArrayUtil.contains(blackIpArray, ip)) {
                        return R.fail("非法访问");
                    }
                }
            }
        }

        TokenParameter tokenParameter = new TokenParameter();
        tokenParameter.getArgs().set("tenantId", tenantId)
                .set("account", account)
                .set("password", password)
                .set("grantType", grantType)
                .set("refreshToken", refreshToken)
                .set("token", token)
                .set("userType", userType)
                .set("userFaceImage", userFaceImage)
                .set("wxCode", wxCode)
                .set("wxSign", wxSign)
                .set("wxVersion", wxVersion);

        ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
        UserInfo userInfo = granter.grant(tokenParameter);

        if (userInfo == null || userInfo.getUser() == null) {
            return R.fail(TokenUtil.USER_NOT_FOUND);
        }
        AuthInfo authInfo = TokenUtil.createAuthInfo(userInfo);
        //当为password 登录时，清除redis token缓存
        if (StrUtil.equals(grantType, PasswordTokenGranter.GRANT_TYPE) || StrUtil.equals(grantType, UserFaceTokenGranter.GRANT_TYPE)) {
            redisUtil.del("token" + authInfo.getAccount());
        }
        //只记录学生在线用户数据
        if (userType.equals(UserEnum.STUDENT.getName())) {
            onlineUserService.save(authInfo);
        }
        //记录登录记录
        String dateStr = DateUtil.format(DateUtil.now(), DateUtil.PATTERN_DATE);
        //时间为10天
        redisUtil.sSetAndTime(CacheNames.LOGINLOG + "_" + dateStr, 864000, userInfo.getUser().getUserId());
        return R.data(authInfo);
    }


    @ApiLog("验证token")
    @PostMapping("checkToken")
    @ApiOperation(value = "验证token", notes = "验证token")
    public R checkToken(
            @ApiParam(value = "令牌") @RequestParam(required = false) String token,
            HttpServletRequest request) {

        Claims claims = SecureUtil.parseJWT(token);
        boolean flag = false;
        if (claims != null) {
            Object o = redisUtil.get(CrazyConstant.ONLINE_KEY + claims.get(SecureUtil.ACCOUNT));
            if (Func.isNotEmpty(o)) {
                Map<String, Object> onlineUser = Func.toMap(o);
                String jwt = Func.toStr(onlineUser.get("key"));
                if (Func.equals(token, jwt)) {
                    flag = true;
                }
            }
        }

        return R.data(flag);
    }

    @GetMapping("/captcha")
    public R<Kv> captcha() {
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        String verCode = specCaptcha.text().toLowerCase();
        String key = UUID.randomUUID().toString();
        // 存入redis并设置过期时间为30分钟
        redisUtil.set(CacheNames.CAPTCHA_KEY + key, verCode, 30L, TimeUnit.MINUTES);
        // 将key和base64返回给前端
        return R.data(Kv.init().set("key", key).set("image", specCaptcha.toBase64()));
    }

    /**
     * 校验是否实名认证，如果没有实名认证，改为普通登录
     *
     * @param account 账号
     * @return R
     */
    @GetMapping("/checkIsRealLogin")
    public R checkIsRealLogin(@RequestParam String account) {
        return onlineUserService.checkIsRealLogin(account);
    }

    /**
     * 是否需要人脸识别
     * 微信小程序审核时需要改为0
     */
    @GetMapping("/checkIsFaceLogin")
    public R checkIsFaceLogin() {
        String isFaceLogin = Convert.toStr(redisUtil.get(CHECK_IS_FACE_LOGIN));// isFaceLogin: 0-小程序校验模式 1-正常模式
        if (StrUtil.equals(isFaceLogin, "0") && !isCityIncluded(WebUtil.getIP())) {
            return R.fail("success");
        } else {
            return R.success("success");
        }
    }

    /**
     * 是否需要人脸识别
     * 微信小程序审核时需要改为0
     */
    @GetMapping("/checkIsFaceReg")
    public R checkIsFaceReg() {
        String isFaceLogin = Convert.toStr(redisUtil.get(CHECK_IS_FACE_REG));// isFaceLogin: 0-小程序校验模式 1-正常模式
        if (StrUtil.equals(isFaceLogin, "0") && !isCityIncluded(WebUtil.getIP())) {
            return R.fail("success");
        } else {
            return R.success("success");
        }
    }

    /**
     * 是否需要点头摇头识别
     * 用于控制前端是否启用点头摇头识别功能
     * 支持全局控制和手机号个性化控制
     */
    @GetMapping("/checkIsHeadGesture")
    @ApiOperation(value = "检查是否需要点头摇头识别", notes = "返回是否需要启用点头摇头识别功能，支持全局控制和手机号个性化控制")
    public R checkIsHeadGesture(@ApiParam(value = "手机号") @RequestParam(required = false) String phone) {
        // 优先检查手机号个性化配置
        if (StrUtil.isNotBlank(phone)) {
            String phoneHeadGesture = Convert.toStr(redisUtil.get(CHECK_IS_HEAD_GESTURE + "_" + phone));
            if (StrUtil.isNotBlank(phoneHeadGesture)) {
                if (StrUtil.equals(phoneHeadGesture, "0")) {
                    return R.fail("点头摇头识别已关闭");
                } else {
                    return R.success("点头摇头识别已开启");
                }
            }
        }

        // 检查全局配置
        String isHeadGesture = Convert.toStr(redisUtil.get(CHECK_IS_HEAD_GESTURE));// isHeadGesture: 0-关闭点头摇头识别 1-开启点头摇头识别
        if (StrUtil.equals(isHeadGesture, "0")) {
            return R.fail("点头摇头识别已关闭（全局配置）");
        } else {
            return R.success("点头摇头识别已开启（全局配置）");
        }
    }

    /**
     * 设置手机号的点头摇头识别配置
     * 用于管理员针对特定手机号进行个性化配置
     */
    @PostMapping("/setHeadGestureByPhone")
    @ApiOperation(value = "设置手机号的点头摇头识别配置", notes = "为特定手机号设置点头摇头识别开关")
    public R setHeadGestureByPhone(@ApiParam(value = "手机号", required = true) @RequestParam String phone,
                                   @ApiParam(value = "是否开启点头摇头识别", required = true) @RequestParam String enable) {
        if (StrUtil.isBlank(phone)) {
            return R.fail("手机号不能为空");
        }

        if (!StrUtil.equals(enable, "0") && !StrUtil.equals(enable, "1")) {
            return R.fail("enable参数只能为0或1");
        }

        String key = CHECK_IS_HEAD_GESTURE + "_" + phone;
        redisUtil.set(key, enable, 30, TimeUnit.DAYS);

        String message = StrUtil.equals(enable, "1") ? "已为手机号 " + phone + " 开启点头摇头识别" : "已为手机号 " + phone + " 关闭点头摇头识别";
        return R.success(message);
    }

    /**
     * 删除手机号的点头摇头识别个性化配置
     * 删除后将使用全局配置
     */
    @DeleteMapping("/removeHeadGestureByPhone")
    @ApiOperation(value = "删除手机号的点头摇头识别个性化配置", notes = "删除特定手机号的个性化配置，恢复使用全局配置")
    public R removeHeadGestureByPhone(@ApiParam(value = "手机号", required = true) @RequestParam String phone) {
        if (StrUtil.isBlank(phone)) {
            return R.fail("手机号不能为空");
        }

        String key = CHECK_IS_HEAD_GESTURE + "_" + phone;
        redisUtil.del(key);

        return R.success("已删除手机号 " + phone + " 的个性化配置，将使用全局配置");
    }

    public boolean isCityIncluded(String ip) {
        try {
            if (StrUtil.isBlank(ip)) {
                return false;
            }
            Map<String, Object> cityInfo = WebUtil.getCityInfo(ip);
            String addr = Func.toStr(cityInfo.get("addr"));
            if (StrUtil.isBlank(addr)) {
                return false;
            }
            for (String city : CITIES) {
                if (addr.contains(city)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.info("isCityIncluded error:{}", e.getMessage());
            return false;
        }
    }

}
