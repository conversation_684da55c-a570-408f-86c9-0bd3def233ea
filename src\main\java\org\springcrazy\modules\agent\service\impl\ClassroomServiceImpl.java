package org.springcrazy.modules.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.core.mp.base.BaseServiceImpl;
import org.springcrazy.modules.agent.entity.Classroom;
import org.springcrazy.modules.agent.mapper.ClassroomMapper;
import org.springcrazy.modules.agent.service.IClassroomService;
import org.springcrazy.modules.system.entity.Post;
import org.springcrazy.modules.system.mapper.PostMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 教室信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Service
public class ClassroomServiceImpl extends BaseServiceImpl<ClassroomMapper, Classroom>  implements IClassroomService {

}
