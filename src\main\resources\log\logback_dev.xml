<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="60 seconds">

    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <!-- 日志输出级别 -->
    <root level="DEBUG">
        <appender-ref ref="STDOUT" />
    </root>

    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="druid.sql" level="INFO"/>


    <!-- MyBatis log configure -->
    <logger name="com.apache.ibatis" level="INFO"/>
    <logger name="org.mybatis.spring" level="DEBUG"/>
    <logger name="java.sql.Connection" level="DEBUG"/>
    <logger name="java.sql.Statement" level="DEBUG"/>
    <logger name="java.sql.PreparedStatement" level="DEBUG"/>

    <!-- 减少部分debug日志 -->
    <logger name="druid.sql" level="INFO"/>
    <logger name="org.apache.shiro" level="INFO"/>
    <logger name="org.mybatis.spring" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.context" level="WARN"/>
    <logger name="org.springframework.beans" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="INFO"/>
    <logger name="org.apache.ibatis.io" level="INFO"/>
    <logger name="org.apache.velocity" level="INFO"/>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="io.undertow" level="INFO"/>
    <logger name="org.xnio.nio" level="INFO"/>
    <logger name="org.thymeleaf" level="INFO"/>
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="org.hibernate.validator" level="INFO"/>
    <logger name="com.netflix.loadbalancer" level="INFO"/>
    <logger name="com.netflix.hystrix" level="INFO"/>
    <logger name="com.netflix.zuul" level="INFO"/>
    <logger name="de.codecentric" level="INFO"/>
    <!-- cache INFO -->
    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="org.springframework.cache" level="INFO"/>
    <!-- cloud -->
    <logger name="org.apache.http" level="INFO"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.netflix.eureka" level="INFO"/>
    <!-- 业务日志 -->
    <Logger name="org.springcrazy" level="DEBUG" />
    <Logger name="org.springcrazy.core.version" level="INFO"/>


</configuration>
