
package org.springcrazy.modules.agent.vo;

import org.springcrazy.modules.agent.entity.AgentAttendance;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 考勤机设备视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AgentAttendanceVO对象", description = "考勤机设备")
public class AgentAttendanceVO extends AgentAttendance {
	private static final long serialVersionUID = 1L;

}
