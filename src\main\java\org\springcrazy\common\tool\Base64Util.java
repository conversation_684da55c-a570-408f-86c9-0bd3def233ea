package org.springcrazy.common.tool;

import com.qiniu.util.Base64;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
public class Base64Util {
    private static String strNetImageToBase64;
    private static String strLocalImageToBase64;

//    public static void main(String[] args) {
//        //第一个:把网络图片装换成Base64
//        String netImagePath = "https://edu.gsjtsz.com/upload/article/20230713/1045780216530222.png";
//        //下面是网络图片转换Base64的方法
//        strNetImageToBase64 = netImageToBase64(netImagePath);
//        log.info(strNetImageToBase64);
//
//        //下面是本地图片转换Base64的方法
//        String imagePath = "F:\\test\\620123199004183710.png";
//        strLocalImageToBase64 = convertImageToBase64Str(imagePath);
//        log.info(strLocalImageToBase64);
//    }


    /**
     * 本地图片转换Base64的方法
     * @param imageFileName
     * @return
     */

    public static String convertImageToBase64Str(String imageFileName) {
        ByteArrayOutputStream baos = null;
        try {
            //获取图片类型
            String suffix = imageFileName.substring(imageFileName.lastIndexOf(".") + 1);
            //构建文件
            File imageFile = new File(imageFileName);
            //通过ImageIO把文件读取成BufferedImage对象
            BufferedImage bufferedImage = ImageIO.read(imageFile);
            //构建字节数组输出流
            baos = new ByteArrayOutputStream();
            //写入流
            ImageIO.write(bufferedImage, suffix, baos);
            //通过字节数组流获取字节数组
            byte[] bytes = baos.toByteArray();
            //获取JDK8里的编码器Base64.Encoder转为base64字符
            return java.util.Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (baos != null) {
                    baos.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 网络图片转换Base64的方法
     *
     * @param netImagePath
     */
    public static String netImageToBase64(String netImagePath) {
        final ByteArrayOutputStream data = new ByteArrayOutputStream();
        try {
            // 创建URL
            URL url = new URL(netImagePath);
            final byte[] by = new byte[1024];
            // 创建链接
            final HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(5000);


            InputStream is = conn.getInputStream();
            // 将内容读取内存中
            int len = -1;
            while ((len = is.read(by)) != -1) {
                data.write(by, 0, len);
            }
            // 对字节数组Base64编码
            strNetImageToBase64 = Base64.encodeToString(data.toByteArray(), Base64.NO_WRAP);
            // 关闭流
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return strNetImageToBase64;
    }

}
