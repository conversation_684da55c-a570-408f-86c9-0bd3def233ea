package org.springcrazy.modules.agent.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import org.springcrazy.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springcrazy.core.mp.base.TenantEntity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 教室信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@TableName("edu_agent_classroom")
@ApiModel(value = "Classroom对象", description = "教室信息")
public class Classroom extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "代理商用户ID")
    private Integer agentId;

    @ApiModelProperty(value = "教室名称")
    private String name;

    @ApiModelProperty(value = "教室编号")
    @TableField("codeNum")
    private String codeNum;

    @ApiModelProperty(value = "座位数量")
    @TableField("seatNum")
    private Integer seatNum;
}
