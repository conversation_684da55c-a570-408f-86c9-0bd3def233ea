<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.PointMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pointResultMap" type="org.springcrazy.modules.exam.entity.Point">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="add_time" property="addTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="subject_id" property="subjectId"/>
        <result column="exam_frequency" property="examFrequency"/>
        <result column="info" property="info"/>
        <result column="level" property="level"/>
        <result column="state" property="state"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springcrazy.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>


    <select id="selectPointPage" resultMap="pointResultMap">
        select * from exam_point where state = 0
    </select>


    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, name as title, id as 'value', id as 'key' from exam_point where state = 0 order by sort desc
    </select>
</mapper>
