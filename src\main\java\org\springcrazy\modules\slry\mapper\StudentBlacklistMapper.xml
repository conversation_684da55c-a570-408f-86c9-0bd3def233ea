<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.slry.mapper.StudentBlacklistMapper">
  <resultMap id="BaseResultMap" type="org.springcrazy.modules.slry.entity.StudentBlacklist">
    <!--@mbg.generated-->
    <!--@Table edu_student_blacklist-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="student_name" jdbcType="VARCHAR" property="studentName" />
    <result column="student_id_card_no" jdbcType="VARCHAR" property="studentIdCardNo" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="msg" jdbcType="VARCHAR" property="msg" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, student_name, student_id_card_no, company_name, msg, `status`, create_time, 
    create_user, update_time, update_user
  </sql>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="countByCompanyName" resultType="java.lang.Integer">
    select count(1)
    from edu_student_blacklist
    where company_name=#{companyName,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="countByStudentIdCardNo" resultType="java.lang.Integer">
    select count(1)
    from edu_student_blacklist
    where student_id_card_no=#{studentIdCardNo,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="findAllByCompanyName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from edu_student_blacklist
    where company_name=#{companyName,jdbcType=VARCHAR} and status=1
  </select>
</mapper>