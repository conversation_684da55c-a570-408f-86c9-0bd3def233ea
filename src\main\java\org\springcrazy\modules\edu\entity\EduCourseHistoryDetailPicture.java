package org.springcrazy.modules.edu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName edu_course_history_detail_picture
 */
@TableName(value ="edu_course_history_detail_picture")
@Data
public class EduCourseHistoryDetailPicture implements Serializable {
    /**
     * 编号
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * edu_course_history_detail外键
     */
    private Integer historyDetailId;

    /**
     * 扫脸图片网络地址
     */
    private String url;

    /**
     * 地址
     */
    private String address;

    /**
     * IP
     */
    private String ip;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Integer count;// 次数

    @TableField(exist = false)
    private String[] times;// 查询条件：时间

    @TableField(exist = false)
    private String endTime;
}