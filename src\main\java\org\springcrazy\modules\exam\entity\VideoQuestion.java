package org.springcrazy.modules.exam.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 视频课程对应弹出题目
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-14
 */
@Data
  @EqualsAndHashCode(callSuper = false)
  @Accessors(chain = true)
@TableName("exam_video_question")
@ApiModel(value="VideoQuestion对象", description="视频课程对应弹出题目")
public class VideoQuestion implements Serializable {

    private static final long serialVersionUID=1L;

      @ApiModelProperty(value = "id")
        @TableId(value = "id", type = IdType.AUTO)
      private Integer id;

      @ApiModelProperty(value = "课程id")
      private Integer courseInfoId;

      @ApiModelProperty(value = "题目内容")
      private String topic;

      @ApiModelProperty(value = "1单选,2多选,3判断")
      private String type;

      @ApiModelProperty(value = "正确答案")
      private String rightAnswer;

      @ApiModelProperty(value = "弹题时间")
      private String topicTime;

      @ApiModelProperty(value = "答错跳回时间")
      private String backTime;

      @TableLogic
      @ApiModelProperty(value = "是否已删除")
      private Integer isDeleted;

      @ApiModelProperty(value = "添加时间")
      @TableField(fill = FieldFill.INSERT)
      private LocalDateTime createTime;

      @ApiModelProperty(value = "更新时间")
      @TableField(fill = FieldFill.UPDATE)
      private LocalDateTime updateTime;

}
