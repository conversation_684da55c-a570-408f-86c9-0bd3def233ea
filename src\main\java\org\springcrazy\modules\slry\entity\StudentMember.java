
package org.springcrazy.modules.slry.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 员工账号实体类
 */
@Data
@TableName("edu_student_member")
@ApiModel(value = "StudentMember对象", description = "员工账号表")
public class StudentMember implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    protected Integer id;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    protected String realName;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    protected String idCardNo;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    protected String mobile;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    protected String password;

    /**
     * 视频/照片  1视频  2照片
     */
    @ApiModelProperty(value = "视频/照片  1视频  2照片")
    protected Integer mediaType;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    protected String qymc;

    /**
     * 企业统一社会信用代码
     */
    @ApiModelProperty(value = "企业统一社会信用代码")
    protected String qytyshxydm;


    /**
     * 是否线上支付  1是  2否
     */
    @ApiModelProperty(value = "是否线上支付  1是  2否")
    protected Integer paymentType;

    /**
     * 代理商名称
     */
    @ApiModelProperty(value = "代理商名称")
    protected String agentName;


    /**
     * 证书编号
     */
    @ApiModelProperty(value = "证书编号")
    protected String newzsbh;

    /**
     * 发证日期
     */
    @ApiModelProperty(value = "发证日期")
    protected String fzrq;


    /**
     * 证书有效期
     */
    @ApiModelProperty(value = "证书有效期")
    protected String zsyxq;

    /**
     * 人员类别
     */
    @ApiModelProperty(value = "人员类别")
    protected String rylb;

    /**
     * 证书类型
     */
    @ApiModelProperty(value = "证书类型")
    protected String zxtype;


    /**
     * 状态:0正常1删除
     */
    @TableLogic
    @ApiModelProperty(value = "状态:0正常1删除")
    private Integer isDeleted;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String[] timeArr;

    @TableField(exist = false)
    protected String paymentTypeStr;
}
