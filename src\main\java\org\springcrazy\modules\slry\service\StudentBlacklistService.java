package org.springcrazy.modules.slry.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springcrazy.modules.slry.mapper.StudentBlacklistMapper;
import org.springcrazy.modules.slry.entity.StudentBlacklist;
@Service
public class StudentBlacklistService extends ServiceImpl<StudentBlacklistMapper, StudentBlacklist> {


    @Autowired
    private StudentBlacklistMapper studentBlacklistMapper;

    public Integer countByCompanyName(String companyName) {
        return studentBlacklistMapper.countByCompanyName(companyName);
    }

    public Integer countByStudentIdCardNo(String studentIdCardNo) {
        return studentBlacklistMapper.countByStudentIdCardNo(studentIdCardNo);
    }
}
