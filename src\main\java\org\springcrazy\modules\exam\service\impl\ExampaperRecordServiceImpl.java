
package org.springcrazy.modules.exam.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.api.client.util.Lists;
import com.google.api.client.util.Maps;
import org.springcrazy.core.log.exception.ServiceException;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.jackson.JsonUtil;
import org.springcrazy.core.tool.utils.DateUtil;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.core.tool.utils.RedisUtil;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.edu.service.ICourseService;
import org.springcrazy.modules.edu.service.ICourseStudyhistoryService;
import org.springcrazy.modules.edu.service.ITrxorderDetailService;
import org.springcrazy.modules.exam.entity.*;
import org.springcrazy.modules.exam.mapper.ExampaperRecordMapper;
import org.springcrazy.modules.exam.service.*;
import org.springcrazy.modules.exam.vo.ExampaperRecordVO;
import org.springcrazy.modules.exam.vo.ExampaperVO;
import org.springcrazy.modules.exam.vo.QstmiddleVO;
import org.springcrazy.modules.msg.entity.MsgReceive;
import org.springcrazy.modules.msg.service.IMsgReceiveService;
import org.springcrazy.modules.slry.entity.StudentZhengshu;
import org.springcrazy.modules.slry.service.StudentZhengshuService;
import org.springcrazy.modules.system.excel.ExportExampaperRecordExcel;
import org.springcrazy.modules.user.entity.Student;
import org.springcrazy.modules.user.service.IStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 考试记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Service
public class ExampaperRecordServiceImpl extends ServiceImpl<ExampaperRecordMapper, ExampaperRecord> implements IExampaperRecordService {

	@Resource
	private ThreadPoolTaskExecutor taskExecutor;
	@Autowired
	IQuestionRecordService questionRecordService;

	@Autowired
	IExampaperRecordJsonService exampaperRecordJsonService;

	@Autowired
	IExampaperService exampaperService;

	@Autowired
	IErrorQuestionService errorQuestionService;

	@Autowired
	ITrxorderDetailService trxorderDetailService;

	@Autowired
	ICourseStudyhistoryService courseStudyhistoryService;

	@Autowired
	StudentZhengshuService zhengshuService;

	@Autowired
	ICourseService courseService;

	@Autowired
	IMsgReceiveService msgReceiveService;

	@Autowired
	IStudentService studentService;

	@Autowired
	RedisUtil redisUtil;

	@Value(("${exam.count}"))
	private Integer examCount;
	@Value("${slry.needWatchTime}")
	private int needWatchTime;// 需要观看的课时时长（秒）
	@Value("${slry.classHourTime}")
	private int classHourTime;// 每节课时的时长（分钟）

	public static final String EXAM_SUCCESS = "您已经通过考试，考试得分为：{}分，审核周期为1-2个工作日，审核结果会推送到系统消息中心(学习系统右上角)";
	public static final String EXAMINE_SUCCESS = "您的【{}】学习记录和考试记录已审核通过，请联系企业负责人前往甘肃省住房和城乡建设厅政务服务申报平台中及时办理延期！";
	public static final String EXAMINE_FAIL = "您的【{}】课程已学完，系统检测您在学习过程中存在违规行为，根据《住建行业安全生产管理人员继续教育》要求：将对您提交的继续信息记录退回，请企业负责人与我公司联系，核验时间为周一至周五早上9:00点至下午5:00。 联系方式：18152067968";

	@Override
	public IPage<ExampaperRecordVO> selectExampaperRecordPage(IPage<ExampaperRecordVO> page, ExampaperRecordVO exampaperRecord) {
		return page.setRecords(baseMapper.selectExampaperRecordPage(page, exampaperRecord));
	}

	@Override
	public ExampaperRecordVO getExamPaper(ExampaperRecord exampaperRecord) {
		return baseMapper.getExamPaper(exampaperRecord);
	}

	@Override
	public int getExamPaperNums(Map<String,Object> param) {
		return baseMapper.getExamPaperNums(param);
	}

	@Override
	public int getQuestionNums(Map<String,Object> param) {
		return baseMapper.getQuestionNums(param);
	}

	@Override
	public int getquestionErrorNums(Map<String,Object> param) {
		return baseMapper.getquestionErrorNums(param);
	}

	@Override
	public List<PaperMiddle> queryQuestionRecord(String paperId) {
		return null;
	}

	@Override
	public ExampaperRecord addExampaperRecordSave(ExampaperVO exampaperVO) {
		String key = "addExampaperRecordSave:" + SecureUtil.getUserId();
		if (redisUtil.hasKey(key)) {
			throw new ServiceException("提交试卷过于频繁，请稍后重试");
		}
		//创建试卷答题记录
		//正确题数
		Integer correctNum = 0;
		//已做题数
		Integer doneCount = 0;
		//用户分数
		BigDecimal userScore = new BigDecimal(0);
		//考试记录对象
		ExampaperRecord exampaperRecord = new ExampaperRecord();
		//用户id
		exampaperRecord.setUserId(exampaperVO.getUserId());
		//考试类型
		exampaperRecord.setExamType(exampaperVO.getType());
		//添加时间
		exampaperRecord.setAddTime(LocalDateTime.now());
		//正确题数
		exampaperRecord.setCorrectNum(correctNum);//
		//已做题数
		exampaperRecord.setDoneCount(doneCount);
		//用户分数
		exampaperRecord.setUserScore(userScore);
		//试卷id
		exampaperRecord.setEpId(exampaperVO.getId());
		//试卷名称
		exampaperRecord.setPaperName(exampaperVO.getName());
		//专业id
		exampaperRecord.setSubjectId(exampaperVO.getSubjectId());
		//截图
		exampaperRecord.setScreen(exampaperVO.getScreen());
		//非法移出次数
		exampaperRecord.setIllegalRemovalNum(exampaperVO.getIllegalRemovalNum());
		//人脸识别不通过次数
		exampaperRecord.setLeaveNum(exampaperVO.getLeaveNum());

		exampaperRecord.setType(1);
		//该用户考试所用时间单位是秒
		exampaperRecord.setTestTime(exampaperVO.getTestTime());
		//0为默认完成，1为未考完
		exampaperRecord.setStatus(0);
		//考试总分
		exampaperRecord.setQstScore(exampaperVO.getScore());
		//考试题数
		exampaperRecord.setQstCount(exampaperVO.getQstCount());
		Exampaper exampaper = exampaperService.getById(exampaperRecord.getEpId());
		Integer courseId = exampaper.getCourseId();
		Integer userId = exampaperRecord.getUserId();
		Integer epId = exampaperRecord.getEpId();
		// 检查是否可以考试
		checkExam(courseId, userId, epId);
		save(exampaperRecord);

		//创建试卷试题答题记录
		List<QuestionRecord> questionRecordList = Lists.newArrayList();
		//获取试卷下的试题对象
		Map<Integer,QstmiddleVO> paperMiddlesMap = exampaperService.queryQuestionReturnMap(exampaperVO.getId());
		List<PaperMiddle> list = exampaperVO.getList();
		//计算考试分数，已做题数，正确题数
		for (PaperMiddle paperMiddle : list) {
			List<QstmiddleVO> questionArr = paperMiddle.getQuestionArr();
			for (QstmiddleVO qstmiddle : questionArr) {
				//把试题标题和选项放到list对象中
				if(Func.isNotEmpty(qstmiddle)){
					QstmiddleVO qstmiddleVO = paperMiddlesMap.get(qstmiddle.getQstId());
					qstmiddle.setQstContent(qstmiddleVO.getQstContent());
					qstmiddle.setOptionList(qstmiddleVO.getOptionList());
					qstmiddle.setIsAsr(qstmiddleVO.getIsAsr());
				}


				//判断是否答题
				Integer qusFlag = 1;//0 为正确 1为错误
				if(Func.isNotBlank(qstmiddle.getUseranswer())){
					//已做题数加1
					doneCount++;
					//作对了
					if(Func.equals(qstmiddle.getUseranswer(),qstmiddle.getIsAsr())){
						correctNum++;
						userScore = userScore.add(new BigDecimal(paperMiddle.getScore()));
						qusFlag = 0;
					}
				}
				//创建答题记录
				QuestionRecord questionRecord = new QuestionRecord();
				questionRecord.setAddtime(LocalDateTime.now());
				questionRecord.setPaperRecordId(exampaperRecord.getId());
				questionRecord.setPapermiddleId(paperMiddle.getId());
				questionRecord.setQstType(qstmiddle.getQstType());
				questionRecord.setCusId(exampaperVO.getUserId());
				questionRecord.setScore(new BigDecimal(paperMiddle.getScore()));
				questionRecord.setPaperId(exampaperVO.getId());
				questionRecord.setQstId(qstmiddle.getQstId());
				questionRecord.setStatus(qusFlag);
				questionRecord.setUseranswer(qstmiddle.getUseranswer());
				questionRecord.setState(0);
				questionRecordList.add(questionRecord);
			}
		}
		if(questionRecordList.size() > 0){
			//处理错题
			handleErrorQues(questionRecordList,exampaperVO.getUserId());
			questionRecordService.saveBatch(questionRecordList);
		}

		//创建试卷答题json
		ExampaperRecordJson exampaperRecordJson = new ExampaperRecordJson();
		exampaperRecordJson.setExamRecordId(exampaperRecord.getId());
		exampaperRecordJson.setAnalysisJson(JsonUtil.toJson(exampaperVO));
		exampaperRecordJsonService.save(exampaperRecordJson);

		//更新exampaperRecord数据
		exampaperRecord.setCorrectNum(correctNum);//
		exampaperRecord.setDoneCount(doneCount);
		exampaperRecord.setUserScore(userScore);

		//计算正确率
		exampaperRecord.setAccuracy(0f);
		if(exampaperVO.getQstCount() > 0 ){
			BigDecimal correctNumDecimal = new BigDecimal(correctNum);
			BigDecimal qstCountDecimal = new BigDecimal(exampaperVO.getQstCount());
			BigDecimal accuracy = correctNumDecimal.divide(qstCountDecimal, 2, BigDecimal.ROUND_HALF_UP);
			exampaperRecord.setAccuracy(accuracy.floatValue()*100);//正确率
		}

		TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
				.eq(TrxorderDetail::getUserId, userId)
				.eq(TrxorderDetail::getCourseId, courseId)
				.eq(TrxorderDetail::getClearType,0)
		);
		exampaperRecord.setTrxorderDetailId(trxorderDetail.getId());
		// 判断成绩是否大于等于60分
		if (exampaperRecord.getUserScore().compareTo(new BigDecimal(60)) >= 0) {
			LambdaUpdateWrapper<TrxorderDetail> trxorderDetailLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
			trxorderDetailLambdaUpdateWrapper.eq(TrxorderDetail::getId, trxorderDetail.getId());
			trxorderDetailLambdaUpdateWrapper.eq(TrxorderDetail::getCourseId, exampaper.getCourseId());
			trxorderDetailLambdaUpdateWrapper.eq(TrxorderDetail::getUserId, exampaperVO.getUserId());
			trxorderDetailLambdaUpdateWrapper.eq(TrxorderDetail::getClearType, 0);
			trxorderDetailLambdaUpdateWrapper.set(TrxorderDetail::getExamSucces, 1);
			trxorderDetailLambdaUpdateWrapper.set(TrxorderDetail::getExamScore, exampaperRecord.getUserScore());
			trxorderDetailLambdaUpdateWrapper.set(TrxorderDetail::getExamSuccessTime, DateUtil.now());
			trxorderDetailService.update(trxorderDetailLambdaUpdateWrapper);
		}

		updateById(exampaperRecord);
		Student student = studentService.getById(userId);
		String ksDetectLivingFaceRedisKey = userId + "_ksDetectLivingFace_" + student.getMobile();
		redisUtil.del(ksDetectLivingFaceRedisKey);
		redisUtil.set(key, "Y", 15);
		return exampaperRecord;
	}

	@Override
	public Map<String, Object> getQuesData(Map<String,Object> param) {
		Map<String,Object> result = Maps.newHashMap();
		List<Map<String, Object>> quesData = baseMapper.getQuesData(param);
		List<String> days = Lists.newArrayList();
		//答题数据数组
		List<String> data1 = Lists.newArrayList();
		//错题数据数组
		List<String> data2 = Lists.newArrayList();
		for (int i = 8; i > 0; i--) {
			Date date = DateUtil.minusDays(DateUtil.now(), i);
			String day = DateUtil.formatDate(date);
			days.add(day);
			List<Map<String, Object>> dayList = quesData.stream().filter(e -> Func.equals(e.get("day"), day)).collect(Collectors.toList());
			if(dayList.size() > 0 ){
				data1.add(Func.toStr(dayList.get(0).get("count"),"0"));
				data2.add(Func.toStr(dayList.get(0).get("errorCount"),"0"));
			}else{
				data1.add("0");
				data2.add("0");
			}
		}
		result.put("days",days);
		result.put("data1",data1);
		result.put("data2",data2);
		return result;
	}

	/**
	 * 处理错题
	 * @param questionRecordList
	 */
	public void handleErrorQues(List<QuestionRecord> questionRecordList,Integer userId){
		taskExecutor.execute(()->{
			List<ErrorQuestion> errorQuestiondList = Lists.newArrayList();
			QueryWrapper<ErrorQuestion> errorQuestionQueryWrapper = new QueryWrapper<>();
			errorQuestionQueryWrapper.lambda().eq(ErrorQuestion::getUserId,userId);
			List<ErrorQuestion> list = errorQuestionService.list(errorQuestionQueryWrapper);

			for (QuestionRecord questionRecord : questionRecordList) {
				List<ErrorQuestion> collect = list.stream().filter(e -> Func.equals(e.getQstId(), questionRecord.getQstId())).collect(Collectors.toList());
				//未创建错题
				if(collect.size() == 0){
					//创建错题
					if(questionRecord.getStatus() == 1){
						ErrorQuestion errorQuestion = new ErrorQuestion();
						errorQuestion.setAddtime(LocalDateTime.now());
						errorQuestion.setUserId(userId);
						errorQuestion.setPaperRecordId(questionRecord.getPaperRecordId());
						errorQuestion.setPaperId(questionRecord.getPaperId());
						errorQuestion.setQstId(questionRecord.getQstId());
						errorQuestion.setCount(0);
						errorQuestion.setErrorCount(0);
						errorQuestiondList.add(errorQuestion);
					}
				}else{
					//已创建错题
					for (ErrorQuestion errorQuestion : collect) {
						if(questionRecord.getStatus() == 1){
							Integer errorCount = errorQuestion.getErrorCount() == null?0:errorQuestion.getErrorCount();
							errorQuestion.setErrorCount(errorCount+1);
						}
						Integer count = errorQuestion.getCount() == null?0:errorQuestion.getCount();
						errorQuestion.setCount(count+1);
						errorQuestiondList.add(errorQuestion);
					}
				}
			}
			errorQuestionService.saveOrUpdateBatch(errorQuestiondList);
		});

	}

	@Override
	public void exportExampaperRecord(HttpServletResponse response , ExampaperRecordVO exampaperRecord) {
		ExcelWriter excelWriter = null;
		try {
			Integer size = 3000;

			excelWriter = EasyExcel.write(response.getOutputStream(), ExportExampaperRecordExcel.class).build();
			WriteSheet writeSheet = EasyExcel.writerSheet("考试记录数据表").build();

			Query query = new Query();
			Page<ExportExampaperRecordExcel> page = new Page<>();
			List<ExportExampaperRecordExcel> list = baseMapper.exportExampaperRecord(page, exampaperRecord);
			for (int i = 1; i <= (page.getTotal()/size)+1; i++) {
				page.setCurrent(i);
				page.setSize(size);
				list = baseMapper.exportExampaperRecord(page, exampaperRecord);
				excelWriter.write(list, writeSheet);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if (excelWriter != null) {
				excelWriter.finish();
			}
		}
	}


	/**
	 * 检查是否可以考试
	 *
	 * @param courseId
	 * @param userId
	 * @param epId
	 */
	@Override
	public void checkExam(Integer courseId, Integer userId, Integer epId) {
		TrxorderDetail trxorderDetail = trxorderDetailService.getOne(new LambdaQueryWrapper<TrxorderDetail>()
				.eq(TrxorderDetail::getUserId, userId)
				.eq(TrxorderDetail::getCourseId, courseId)
				.eq(TrxorderDetail::getClearType,0)
		);
		// 判断证书是否过期
		String zhengshuYxq = trxorderDetail.getZhengshuYxq();
		Date zhengshuYxqDate = DateUtil.parse(zhengshuYxq, "yyyy年MM月dd日");
		boolean isBefore = zhengshuYxqDate.before(cn.hutool.core.date.DateUtil.beginOfDay(new Date()));
		if (isBefore) {
			throw new ServiceException("您的证书已过期,无法参加考试");
		}
		// 待审核并且成绩合格就不能再考试
		if (Func.isNotEmpty(trxorderDetail) && trxorderDetail.getExamineType() == 0 && trxorderDetail.getExamSucces() == 1) {
			throw new ServiceException(StrUtil.format(EXAM_SUCCESS, trxorderDetail.getExamScore()));
		}
		// 审核通过就不能再考试
		if (Func.isNotEmpty(trxorderDetail) && trxorderDetail.getExamineType() == 1) {
			throw new ServiceException(StrUtil.format(EXAMINE_SUCCESS, trxorderDetail.getSubjectName()));
		}
        // 审核失败就不能再考试
        if (Func.isNotEmpty(trxorderDetail) && trxorderDetail.getExamineType() == 2) {
            throw new ServiceException(StrUtil.format(EXAMINE_FAIL, trxorderDetail.getSubjectName()));
        }

		// 必须学习完课程才能考试
//		int y = courseStudyhistoryService.getstudykpoint(courseId);// 应学章节数
//		int v = courseStudyhistoryService.getstudystudyisok(userId, courseId, 0);// 已学章节数
//		if (y == v) {
//			// 如果学习的章节数等于已学习的章节数则修改学习完成状态
//			trxorderDetail.setStudySuccess(1);
//			trxorderDetail.setStudySuccessTime(DateUtil.now());
//			trxorderDetail.setLastUpdateTime(DateUtil.now());
//			trxorderDetailService.updateById(trxorderDetail);
//		} else {
//			throw new ServiceException("您还没有学完课程,无法参加考试");
//		}
		// 2024.10.15 改为学员只需在小程序中选择相应课程，累计学习时间达到24课时（每课时45分钟，总计1080分钟）即可参加继续教育在线考试。
		// 观看时长（秒）
		int watchTime = courseStudyhistoryService.getstudystudyisokWatchTime(userId, courseId, 0);
		if (watchTime >= needWatchTime) {
			if (trxorderDetail.getStudySuccess() == 0) {
				// 如果学习的章节数等于已学习的章节数则修改学习完成状态
				trxorderDetail.setStudySuccess(1);
				trxorderDetail.setStudySuccessTime(DateUtil.now());
				trxorderDetail.setLastUpdateTime(DateUtil.now());
				trxorderDetailService.updateById(trxorderDetail);
			}
		} else {
			throw new ServiceException("您还没有学完课程,无法参加考试");
		}

		// 只能考三次
		int exampaperRecordCount = count(new LambdaQueryWrapper<ExampaperRecord>()
				.eq(ExampaperRecord::getUserId, userId)
				.eq(ExampaperRecord::getEpId, epId)
				.eq(ExampaperRecord::getTrxorderDetailId,trxorderDetail.getId())
		);
		if (exampaperRecordCount >= examCount) {
			if(trxorderDetail.getClearType() == 0){
				MsgReceive msgReceive = new MsgReceive();
				msgReceive.setCreateTime(new Date());
				msgReceive.setCusId(1);
				msgReceive.setReceivingCusid(trxorderDetail.getUserId());
				msgReceive.setContent("您的【" +trxorderDetail.getCourseName()+"】考试三次未通过，课时已被系统清除，请重新学习！");
				msgReceive.setType("1");
				msgReceive.setStatus("1");
				msgReceiveService.save(msgReceive);
			}
			// 清除课时
			trxorderDetail.setClearType(1);
			trxorderDetail.setClearedType(2);
			trxorderDetail.setClearUserId(1);
			trxorderDetail.setClearTime(DateUtil.now());
			trxorderDetail.setLastUpdateTime(DateUtil.now());
			trxorderDetail.setExamineMsg("考试三次未过，系统清除");
			trxorderDetailService.updateById(trxorderDetail);
			// 重新绑定新的课程
			String zhengshuCode = trxorderDetail.getZhengshuCode();
			StudentZhengshu zhengshu = zhengshuService.getOne(new LambdaQueryWrapper<StudentZhengshu>()
					.eq(StudentZhengshu::getStatus, 1)
					.eq(StudentZhengshu::getNewzsbh, zhengshuCode)
			);
			courseService.giveCourse(Convert.toStr(userId), Convert.toStr(courseId), zhengshu);
            throw new ServiceException("您的【" + trxorderDetail.getCourseName() + "】考试三次未通过，课时已被系统清除，请重新学习！");
		}
	}
}
