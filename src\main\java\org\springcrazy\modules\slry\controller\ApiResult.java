package org.springcrazy.modules.slry.controller;

import lombok.Data;

/**
 * <AUTHOR>
 * <p>des</p>
 **/
@Data
public class ApiResult {


    private String msg;

    private int code;

    private Object data;

    public static final Integer SUCCESS_CODE = 0;

    public static final Integer ERROR_CODE = -1;

    public ApiResult setCode(int code) {
        this.code = code;
        return this;
    }

    public ApiResult setData(Object data) {
        this.data = data;
        return this;
    }

    public ApiResult setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public static ApiResult error() {
        return new ApiResult().setCode(ERROR_CODE);
    }

    public static ApiResult error(Object data) {
        return new ApiResult().setCode(ERROR_CODE).setData(data);
    }

    public static ApiResult error(String msg) {
        return new ApiResult().setCode(ERROR_CODE).setMsg(msg);
    }

    public static ApiResult error(Object data, String msg) {
        return new ApiResult().setCode(ERROR_CODE).setData(data).setMsg(msg);
    }

    public static ApiResult success() {
        return new ApiResult().setCode(SUCCESS_CODE);
    }

    public static ApiResult success(Object data) {
        return new ApiResult().setCode(SUCCESS_CODE).setData(data);
    }

    public static ApiResult success(String msg) {
        return new ApiResult().setCode(SUCCESS_CODE).setMsg(msg);
    }

    public static ApiResult success(Object data, String msg) {
        return new ApiResult().setCode(SUCCESS_CODE).setData(data).setMsg(msg);
    }
}
