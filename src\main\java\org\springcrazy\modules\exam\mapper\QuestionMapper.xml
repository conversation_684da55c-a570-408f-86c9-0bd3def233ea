<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.QuestionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="questionResultMap" type="org.springcrazy.modules.exam.entity.Question">
        <id column="id" property="id"/>
        <result column="qst_content" property="qstContent"/>
        <result column="isAsr" property="isAsr"/>
        <result column="qstType" property="qstType"/>
        <result column="level" property="level"/>
        <result column="addtime" property="createTime"/>
        <result column="author" property="author"/>
        <result column="qst_analyze" property="qstAnalyze"/>
        <result column="updatetime" property="updateTime"/>
        <result column="subject_id" property="subjectId"/>
        <result column="status" property="status"/>
        <result column="point_id" property="pointId"/>
        <result column="complex_falg" property="complexFalg"/>
        <result column="time" property="time"/>
        <result column="right_time" property="rightTime"/>
        <result column="error_time" property="errorTime"/>
        <result column="accuracy" property="accuracy"/>
        <result column="sort" property="sort"/>
        <result column="vacancyType" property="vacancyType"/>
        <result column="option_list" property="optionList"/>
    </resultMap>


    <select id="selectQuestionPage" resultMap="questionResultMap">
        select * from exam_question where is_deleted = 0
    </select>

</mapper>
