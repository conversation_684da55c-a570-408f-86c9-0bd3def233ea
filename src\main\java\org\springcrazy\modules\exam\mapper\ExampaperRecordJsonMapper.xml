<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.exam.mapper.ExampaperRecordJsonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="exampaperRecordJsonResultMap" type="org.springcrazy.modules.exam.entity.ExampaperRecordJson">
        <id column="id" property="id"/>
        <result column="exam_record_id" property="examRecordId"/>
        <result column="analysis_json" property="analysisJson"/>
    </resultMap>


    <select id="selectExampaperRecordJsonPage" resultMap="exampaperRecordJsonResultMap">
        select * from exam_exampaper_record_json where is_deleted = 0
    </select>

</mapper>
