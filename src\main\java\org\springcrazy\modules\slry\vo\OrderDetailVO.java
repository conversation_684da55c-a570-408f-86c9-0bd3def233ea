package org.springcrazy.modules.slry.vo;

import lombok.Builder;
import lombok.Data;

import java.util.Date;


@Data
@Builder
public class OrderDetailVO {
    private long userId;
    private String userName;
    private String nickName;
    private String email;
    private String phonenumber;
    private String cardNumber;
    private String sex;
    private Date createTime;
    private Date updateTime;
    private String remark;
    private boolean finish;
    private String code;
    private Date finishTime;
}
