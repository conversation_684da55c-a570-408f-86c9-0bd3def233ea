package org.springcrazy.modules.user.utils;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import sun.misc.BASE64Decoder;

import java.io.*;
import java.util.Base64;
import java.util.Date;

@Slf4j
@Component
public class Base64StrToImage {


    @Value("${face.picture-url}")
    private String facePictureUrl;

    @Value("${face.local-url}")
    private String localUrl;


    //BASE64解码成File文件
    public String base64ToFile(String base64, String fileName) {
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        String picUrl = facePictureUrl + "successImg/" + date + "/" + fileName + ".jpg";
        File file = null;
        //创建文件目录
        String filePath = localUrl + "/successImg/" + date;
        File dir = new File(filePath);
        if (!dir.exists() && !dir.isDirectory()) {
            dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            //截取base64头部，获取文件类型
            String fileType = ".jpg";
            //去掉头部，防止转换文件后打开显示文件损坏
            String s = base64.substring(base64.indexOf(",") + 1);
            byte[] bytes = new BASE64Decoder().decodeBuffer(s);
            file = new File(filePath + "/" + fileName + fileType);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (IOException e) {
            log.info("截图保存失败: {}", e.getMessage());
            picUrl = "";
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    picUrl = "";
                    log.info("关闭字节流失败: {}", e.getMessage());
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    picUrl = "";
                    log.info("关闭文件流失败: {}", e.getMessage());
                }
            }
        }
        return picUrl;
    }

    public String savaFaceErrorImg(String base64, String ywName, String mobile) {
        String date = DateUtil.format(new Date(), "yyyy-MM-dd");
        String fileName = DateUtil.format(new Date(), "HHmmss");
        //String picUrl = facePictureUrl + "errorImg/" + date + "/" + mobile + "/" + date1 + "/" + fileName + ".png";
        String picUrl = facePictureUrl + "errorImg/" + date + "/" + ywName + "/" + mobile + "/" + fileName + ".jpg";
        File file = null;
        //创建文件目录
        String filePath = localUrl + "/errorImg/" + date + "/" + ywName + "/" + mobile;
        File dir = new File(filePath);
        if (!dir.exists() && !dir.isDirectory()) {
            dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            //截取base64头部，获取文件类型
            String fileType = ".jpg";
            //去掉头部，防止转换文件后打开显示文件损坏
            String s = base64.substring(base64.indexOf(",") + 1);
            byte[] bytes = new BASE64Decoder().decodeBuffer(s);
            file = new File(filePath + "/" + fileName + fileType);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (IOException e) {
            log.info("截图保存失败: {}", e.getMessage());
            picUrl = "";
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    picUrl = "";
                    log.info("关闭字节流失败: {}", e.getMessage());
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    picUrl = "";
                    log.info("关闭文件流失败: {}", e.getMessage());
                }
            }
        }
        return picUrl;
    }


    //File转MultipartFile
    public static MultipartFile getMultipartFile(File file) {
        FileItem item = new DiskFileItemFactory().createItem("file"
                , MediaType.MULTIPART_FORM_DATA_VALUE
                , true
                , file.getName());
        try (InputStream input = new FileInputStream(file);
             OutputStream os = ((FileItem) item).getOutputStream()) {
            // 流转移
            IOUtils.copy(input, os);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid file: " + e, e);
        }

        return new CommonsMultipartFile(item);
    }


    /**
     * 将图片转换成Base64编码
     *
     * @param imgFile 待处理图片
     * @return
     */
    public static String ImgToBase64(String imgFile) {
        //将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        InputStream in = null;
        byte[] data = null;
        //读取图片字节数组
        try {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return new String(Base64.getEncoder().encode(data));
    }
}
