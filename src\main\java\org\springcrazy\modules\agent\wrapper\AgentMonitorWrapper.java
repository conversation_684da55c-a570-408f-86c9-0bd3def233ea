
package org.springcrazy.modules.agent.wrapper;

import org.springcrazy.core.mp.support.BaseEntityWrapper;
import org.springcrazy.core.tool.utils.BeanUtil;
import org.springcrazy.modules.agent.entity.AgentMonitor;
import org.springcrazy.modules.agent.vo.AgentMonitorVO;

/**
 * 监控设备包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
public class AgentMonitorWrapper extends BaseEntityWrapper<AgentMonitor, AgentMonitorVO>  {

    public static AgentMonitorWrapper build() {
        return new AgentMonitorWrapper();
    }

	@Override
	public AgentMonitorVO entityVO(AgentMonitor agentMonitor) {
		AgentMonitorVO agentMonitorVO = BeanUtil.copy(agentMonitor, AgentMonitorVO.class);

		return agentMonitorVO;
	}

}
