<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.StatDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="statDataResultMap" type="org.springcrazy.modules.web.entity.StatData">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="data" property="data"/>
        <result column="day" property="day"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectStatDataPage" resultMap="statDataResultMap">
        select * from web_stat_data where is_deleted = 0
    </select>

    <select id="selectStudentByArea" resultType="java.util.Map">
        select province,count(id) as count from web_stat_user_area
        group by province
    </select>

</mapper>
