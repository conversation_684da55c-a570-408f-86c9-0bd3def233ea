<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机号管理工具</title>
    <!-- Cloudflare Turnstile -->
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 16px;
        }

        .tabs {
            display: flex;
            margin-bottom: 30px;
            background: #f1f3f4;
            border-radius: 12px;
            padding: 4px;
        }

        .tab {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: transparent;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .radio-group {
            display: flex;
            gap: 15px;
            margin-top: 8px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .radio-option input[type="radio"] {
            width: auto;
            margin: 0;
        }

        .btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            display: none;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 15px;
        }

        .loading .spinner {
            width: 30px;
            height: 30px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tips {
            background: #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 13px;
            color: #6c757d;
        }

        .tips h4 {
            color: #495057;
            margin-bottom: 8px;
            font-size: 14px;
        }

        @media (max-width: 480px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 24px;
            }

            .radio-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 手机号管理工具</h1>
            <p>修复实名制状态 | 更新活体时长 | 头部手势识别管理</p>
        </div>

        <!-- 标签页 -->
        <div class="tabs">
            <button class="tab active" onclick="switchTab('fix-mobile')">修复实名制</button>
            <button class="tab" onclick="switchTab('update-ht')">更新活体时长</button>
            <button class="tab" onclick="switchTab('head-gesture')">头部手势识别</button>
        </div>

        <!-- 修复实名制功能 -->
        <div id="fix-mobile" class="tab-content active">
            <form id="fixForm">
                <div class="form-group">
                    <label for="mobile">手机号码</label>
                    <input type="tel" id="mobile" name="mobile" placeholder="请输入11位手机号码" required>
                </div>
                
                <!-- Cloudflare Turnstile 验证 -->
                <div class="form-group">
                    <label>安全验证</label>
                    <div class="cf-turnstile" 
                         data-sitekey="0x4AAAAAABhKzHPZYk9acnJv" 
                         data-callback="onTurnstileSuccess"
                         data-expired-callback="onTurnstileExpired"
                         data-error-callback="onTurnstileError"
                         data-theme="light"
                         data-size="flexible"
                         id="turnstile-container">
                    </div>
                </div>
                
                <button type="submit" class="btn" id="submitBtn" disabled>
                    🔧 修复实名制状态
                </button>
            </form>

            <div class="tips">
                <h4>💡 使用说明</h4>
                <p>• 输入需要修复的手机号码</p>
                <p>• 完成安全验证（点击验证框）</p>
                <p>• 点击修复按钮等待处理结果</p>
                <p>• 修复成功后该手机号的实名制状态将被更新</p>
                <p>• 授权码已内置，无需手动输入</p>
            </div>
        </div>

        <!-- 更新活体时长功能 -->
        <div id="update-ht" class="tab-content">
            <form id="updateHTForm">
                <div class="form-group">
                    <label for="htMobile">手机号码</label>
                    <input type="tel" id="htMobile" name="mobile" placeholder="请输入11位手机号码" required>
                </div>
                
                <div class="form-group">
                    <label>活体检测类型</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="type1" name="type" value="1" checked>
                            <label for="type1">观看视频</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="type2" name="type" value="2">
                            <label for="type2">考试</label>
                        </div>
                    </div>
                </div>
                
                <!-- Cloudflare Turnstile 验证 -->
                <div class="form-group">
                    <label>安全验证</label>
                    <div class="cf-turnstile" 
                         data-sitekey="0x4AAAAAABhKzHPZYk9acnJv" 
                         data-callback="onTurnstileSuccessHT"
                         data-expired-callback="onTurnstileExpiredHT"
                         data-error-callback="onTurnstileErrorHT"
                         data-theme="light"
                         data-size="flexible"
                         id="turnstile-container-ht">
                    </div>
                </div>
                
                <button type="submit" class="btn" id="submitBtnHT" disabled>
                    ⏱️ 更新活体时长
                </button>
            </form>

            <div class="tips">
                <h4>💡 使用说明</h4>
                <p>• 输入需要更新活体时长的手机号码</p>
                <p>• 选择活体检测类型（观看视频或考试）</p>
                <p>• 完成安全验证（点击验证框）</p>
                <p>• 点击更新按钮等待处理结果</p>
                <p>• 成功后将重置该手机号对应的活体检测时长</p>
            </div>
        </div>

        <!-- 头部手势识别管理功能 -->
        <div id="head-gesture" class="tab-content">
            <form id="headGestureForm">
                <div class="form-group">
                    <label for="hgMobile">手机号码</label>
                    <input type="tel" id="hgMobile" name="mobile" placeholder="请输入11位手机号码" required>
                </div>

                <div class="form-group">
                    <label>头部手势识别设置</label>
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="enable1" name="enable" value="1" checked>
                            <label for="enable1">开启识别</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="enable0" name="enable" value="0">
                            <label for="enable0">关闭识别</label>
                        </div>
                    </div>
                </div>

                <!-- Cloudflare Turnstile 验证 -->
                <div class="form-group">
                    <label>安全验证</label>
                    <div class="cf-turnstile"
                         data-sitekey="0x4AAAAAABhKzHPZYk9acnJv"
                         data-callback="onTurnstileSuccessHG"
                         data-expired-callback="onTurnstileExpiredHG"
                         data-error-callback="onTurnstileErrorHG"
                         data-theme="light"
                         data-size="flexible"
                         id="turnstile-container-hg">
                    </div>
                </div>

                <button type="submit" class="btn" id="submitBtnHG" disabled>
                    🎯 设置头部手势识别
                </button>
            </form>

            <div class="tips">
                <h4>💡 使用说明</h4>
                <p>• 输入需要设置的手机号码</p>
                <p>• 选择是否开启头部手势识别功能</p>
                <p>• 完成安全验证（点击验证框）</p>
                <p>• 点击设置按钮等待处理结果</p>
                <p>• 成功后该手机号将使用个性化的头部手势识别配置</p>
                <p>• 如需恢复全局配置，请联系管理员</p>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在处理中，请稍候...</p>
        </div>

        <div class="result" id="result"></div>
    </div>

    <script>
        // 授权码写死
        const AUTH_KEY = 'b2b04e9a1ce7e973accc83f4dc98f39f';
        
        // Turnstile 验证状态
        let turnstileToken = null;
        let turnstileTokenHT = null;
        let turnstileTokenHG = null;

        // 当前活动的标签页
        let currentTab = 'fix-mobile';

        // 获取页面元素
        const fixForm = document.getElementById('fixForm');
        const updateHTForm = document.getElementById('updateHTForm');
        const headGestureForm = document.getElementById('headGestureForm');
        const submitBtn = document.getElementById('submitBtn');
        const submitBtnHT = document.getElementById('submitBtnHT');
        const submitBtnHG = document.getElementById('submitBtnHG');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        const mobileInput = document.getElementById('mobile');
        const htMobileInput = document.getElementById('htMobile');
        const hgMobileInput = document.getElementById('hgMobile');

        // 标签页切换
        function switchTab(tabName) {
            // 更新标签按钮状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName).classList.add('active');

            currentTab = tabName;
            
            // 清除结果和重置表单状态
            hideResult();
            setLoading(false);
            
            // 重置对应的 Turnstile
            if (tabName === 'fix-mobile') {
                resetTurnstile();
            } else if (tabName === 'update-ht') {
                resetTurnstileHT();
            } else if (tabName === 'head-gesture') {
                resetTurnstileHG();
            }
        }

        // 手机号验证
        function validateMobile(mobile) {
            const mobileRegex = /^1[3-9]\d{9}$/;
            return mobileRegex.test(mobile);
        }

        // 显示结果
        function showResult(message, isSuccess) {
            result.style.display = 'block';
            result.className = `result ${isSuccess ? 'success' : 'error'}`;
            result.textContent = message;
        }

        // 隐藏结果
        function hideResult() {
            result.style.display = 'none';
        }

        // 设置加载状态
        function setLoading(isLoading) {
            if (isLoading) {
                loading.style.display = 'block';
                submitBtn.disabled = true;
                submitBtnHT.disabled = true;
                submitBtnHG.disabled = true;
                if (currentTab === 'fix-mobile') {
                    submitBtn.textContent = '处理中...';
                } else if (currentTab === 'update-ht') {
                    submitBtnHT.textContent = '处理中...';
                } else if (currentTab === 'head-gesture') {
                    submitBtnHG.textContent = '处理中...';
                }
                hideResult();
            } else {
                loading.style.display = 'none';
                // 根据当前标签页和验证状态设置按钮状态
                if (currentTab === 'fix-mobile') {
                    submitBtn.disabled = !turnstileToken;
                    submitBtn.textContent = '🔧 修复实名制状态';
                } else if (currentTab === 'update-ht') {
                    submitBtnHT.disabled = !turnstileTokenHT;
                    submitBtnHT.textContent = '⏱️ 更新活体时长';
                } else if (currentTab === 'head-gesture') {
                    submitBtnHG.disabled = !turnstileTokenHG;
                    submitBtnHG.textContent = '🎯 设置头部手势识别';
                }
            }
        }

        // 修复实名制表单提交处理
        fixForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const mobile = mobileInput.value.trim();

            // 基本验证
            if (!mobile) {
                showResult('请输入手机号码', false);
                return;
            }

            if (!validateMobile(mobile)) {
                showResult('请输入正确的11位手机号码', false);
                return;
            }

            // 验证 Turnstile
            if (!turnstileToken) {
                showResult('请完成安全验证', false);
                return;
            }

            // 开始处理
            setLoading(true);

            try {
                // 构建请求URL和参数
                const baseUrl = 'https://frag.gsjtsz.com/api/agryApi/fixMobileCheck';
                const formData = new FormData();
                formData.append('mobile', mobile);
                formData.append('authKey', AUTH_KEY);
                formData.append('turnstileToken', turnstileToken);

                const response = await fetch(baseUrl, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showResult(`✅ ${data.msg || '修复成功'}`, true);
                    // 清空表单
                    fixForm.reset();
                    // 重置 Turnstile
                    resetTurnstile();
                } else {
                    showResult(`❌ ${data.msg || '修复失败，请检查参数'}`, false);
                    // 失败时也重置 Turnstile，防止重复使用同一个 token
                    resetTurnstile();
                }
            } catch (error) {
                console.error('请求失败:', error);
                showResult('❌ 网络请求失败，请检查网络连接或服务器状态', false);
                // 网络错误时也重置 Turnstile
                resetTurnstile();
            } finally {
                setLoading(false);
            }
        });

        // 更新活体时长表单提交处理
        updateHTForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const mobile = htMobileInput.value.trim();
            const type = document.querySelector('input[name="type"]:checked').value;

            // 基本验证
            if (!mobile) {
                showResult('请输入手机号码', false);
                return;
            }

            if (!validateMobile(mobile)) {
                showResult('请输入正确的11位手机号码', false);
                return;
            }

            // 验证 Turnstile
            if (!turnstileTokenHT) {
                showResult('请完成安全验证', false);
                return;
            }

            // 开始处理
            setLoading(true);

            try {
                // 构建请求URL和参数
                const baseUrl = `https://agry.gsjtsz.com/api/agryApi/updateHT?mobile=${encodeURIComponent(mobile)}&type=${type}&authKey=${AUTH_KEY}`;

                const response = await fetch(baseUrl, {
                    method: 'GET'
                });

                const data = await response.json();

                if (data.success) {
                    const typeText = type === '1' ? '观看视频' : '考试';
                    showResult(`✅ ${typeText}活体时长更新成功`, true);
                    // 清空表单
                    updateHTForm.reset();
                    // 重新选中第一个选项
                    document.getElementById('type1').checked = true;
                    // 重置 Turnstile
                    resetTurnstileHT();
                } else {
                    showResult(`❌ ${data.msg || '更新失败，请检查参数'}`, false);
                    // 失败时也重置 Turnstile
                    resetTurnstileHT();
                }
            } catch (error) {
                console.error('请求失败:', error);
                showResult('❌ 网络请求失败，请检查网络连接或服务器状态', false);
                // 网络错误时也重置 Turnstile
                resetTurnstileHT();
            } finally {
                setLoading(false);
            }
        });

        // 头部手势识别表单提交处理
        headGestureForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const mobile = hgMobileInput.value.trim();
            const enable = document.querySelector('input[name="enable"]:checked').value;

            // 基本验证
            if (!mobile) {
                showResult('请输入手机号码', false);
                return;
            }

            if (!validateMobile(mobile)) {
                showResult('请输入正确的11位手机号码', false);
                return;
            }

            // 验证 Turnstile
            if (!turnstileTokenHG) {
                showResult('请完成安全验证', false);
                return;
            }

            // 开始处理
            setLoading(true);

            try {
                // 构建请求URL和参数
                const baseUrl = 'https://agry.gsjtsz.com/api/crazy-auth/setHeadGestureByPhone';
                const formData = new FormData();
                formData.append('phone', mobile);
                formData.append('enable', enable);

                const response = await fetch(baseUrl, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    const enableText = enable === '1' ? '开启' : '关闭';
                    showResult(`✅ 已为手机号 ${mobile} ${enableText}头部手势识别`, true);
                    // 清空表单
                    headGestureForm.reset();
                    // 重新选中第一个选项
                    document.getElementById('enable1').checked = true;
                    // 重置 Turnstile
                    resetTurnstileHG();
                } else {
                    showResult(`❌ ${data.msg || '设置失败，请检查参数'}`, false);
                    // 失败时也重置 Turnstile
                    resetTurnstileHG();
                }
            } catch (error) {
                console.error('请求失败:', error);
                showResult('❌ 网络请求失败，请检查网络连接或服务器状态', false);
                // 网络错误时也重置 Turnstile
                resetTurnstileHG();
            } finally {
                setLoading(false);
            }
        });

        // 输入时清除之前的结果
        mobileInput.addEventListener('input', hideResult);
        htMobileInput.addEventListener('input', hideResult);
        hgMobileInput.addEventListener('input', hideResult);

        // 手机号格式化输入
        function formatMobileInput(input) {
            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 11) {
                    value = value.substring(0, 11);
                }
                e.target.value = value;
            });
        }

        formatMobileInput(mobileInput);
        formatMobileInput(htMobileInput);
        formatMobileInput(hgMobileInput);

        // Cloudflare Turnstile 回调函数 - 修复实名制
        window.onTurnstileSuccess = function(token) {
            console.log('Turnstile 验证成功:', token);
            turnstileToken = token;
            if (currentTab === 'fix-mobile') {
                submitBtn.disabled = false;
            }
            hideResult();
        };

        window.onTurnstileExpired = function() {
            console.log('Turnstile 验证过期');
            turnstileToken = null;
            if (currentTab === 'fix-mobile') {
                submitBtn.disabled = true;
                showResult('安全验证已过期，请重新验证', false);
            }
        };

        window.onTurnstileError = function(error) {
            console.error('Turnstile 验证错误:', error);
            turnstileToken = null;
            if (currentTab === 'fix-mobile') {
                submitBtn.disabled = true;
                showResult('安全验证失败，请刷新页面重试', false);
            }
        };

        // Cloudflare Turnstile 回调函数 - 更新活体时长
        window.onTurnstileSuccessHT = function(token) {
            console.log('Turnstile HT 验证成功:', token);
            turnstileTokenHT = token;
            if (currentTab === 'update-ht') {
                submitBtnHT.disabled = false;
            }
            hideResult();
        };

        window.onTurnstileExpiredHT = function() {
            console.log('Turnstile HT 验证过期');
            turnstileTokenHT = null;
            if (currentTab === 'update-ht') {
                submitBtnHT.disabled = true;
                showResult('安全验证已过期，请重新验证', false);
            }
        };

        window.onTurnstileErrorHT = function(error) {
            console.error('Turnstile HT 验证错误:', error);
            turnstileTokenHT = null;
            if (currentTab === 'update-ht') {
                submitBtnHT.disabled = true;
                showResult('安全验证失败，请刷新页面重试', false);
            }
        };

        // Cloudflare Turnstile 回调函数 - 头部手势识别
        window.onTurnstileSuccessHG = function(token) {
            console.log('Turnstile HG 验证成功:', token);
            turnstileTokenHG = token;
            if (currentTab === 'head-gesture') {
                submitBtnHG.disabled = false;
            }
        };

        window.onTurnstileExpiredHG = function() {
            console.log('Turnstile HG 验证过期');
            turnstileTokenHG = null;
            if (currentTab === 'head-gesture') {
                submitBtnHG.disabled = true;
                showResult('安全验证已过期，请重新验证', false);
            }
        };

        window.onTurnstileErrorHG = function(error) {
            console.error('Turnstile HG 验证错误:', error);
            turnstileTokenHG = null;
            if (currentTab === 'head-gesture') {
                submitBtnHG.disabled = true;
                showResult('安全验证失败，请刷新页面重试', false);
            }
        };

        // 重置 Turnstile - 修复实名制
        function resetTurnstile() {
            turnstileToken = null;
            submitBtn.disabled = true;
            // 使用 Turnstile API 重置组件
            if (window.turnstile) {
                window.turnstile.reset('#turnstile-container');
            }
        }

        // 重置 Turnstile - 更新活体时长
        function resetTurnstileHT() {
            turnstileTokenHT = null;
            submitBtnHT.disabled = true;
            // 使用 Turnstile API 重置组件
            if (window.turnstile) {
                window.turnstile.reset('#turnstile-container-ht');
            }
        }

        // 重置 Turnstile - 头部手势识别
        function resetTurnstileHG() {
            turnstileTokenHG = null;
            submitBtnHG.disabled = true;
            // 使用 Turnstile API 重置组件
            if (window.turnstile) {
                window.turnstile.reset('#turnstile-container-hg');
            }
        }

        // 页面加载完成后，确保按钮处于禁用状态
        document.addEventListener('DOMContentLoaded', function() {
            submitBtn.disabled = true;
            submitBtnHT.disabled = true;
            submitBtnHG.disabled = true;
        });
    </script>
</body>
</html> 