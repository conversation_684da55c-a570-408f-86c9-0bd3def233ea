
package org.springcrazy.modules.agent.vo;

import org.springcrazy.modules.agent.entity.Classroom;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 教室信息视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ClassroomVO对象", description = "教室信息")
public class ClassroomVO extends Classroom {
	private static final long serialVersionUID = 1L;

}
