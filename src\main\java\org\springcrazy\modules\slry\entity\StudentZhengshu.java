package org.springcrazy.modules.slry.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName(value = "edu_student_zhengshu")
public class StudentZhengshu {
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    /**
     * 人员姓名
     */
    @TableField(value = "RYXM")
    private String ryxm;

    /**
     * 人员id(身份证)
     */
    @TableField(value = "RYID")
    private String ryid;

    /**
     * 证书有效状态
     */
    @TableField(value = "ZCZT")
    private String zczt;

    /**
     * 发证日期
     */
    @TableField(value = "FZRQ")
    private String fzrq;

    /**
     * 证书有效期
     */
    @TableField(value = "ZSYXQ")
    private String zsyxq;

    /**
     * 证书编号
     */
    @TableField(value = "NEWZSBH")
    private String newzsbh;

    /**
     * 企业名称
     */
    @TableField(value = "QYMC")
    private String qymc;

    /**
     * 人员性别
     */
    @TableField(value = "RYXB")
    private String ryxb;

    /**
     * 人员类别
     */
    @TableField(value = "RYLB")
    private String rylb;

    @TableField(value = "SSQYTYSHXYDM")
    private String ssqytyshxydm;

    /**
     * 职务
     */
    @TableField(value = "ZHIWU")
    private String zhiwu;

    /**
     * 职称
     */
    @TableField(value = "ZHICHENG")
    private String zhicheng;

    @TableField(value = "FILEGUID")
    private String fileguid;

    /**
     * 证书信息
     */
    @TableField(value = "ZSXX")
    private String zsxx;

    /**
     * 证书类别，取的是NEWZSBH的字母
     */
    @TableField(value = "ZXTYPE")
    private String zxtype;

    /**
     * 创建时间
     */
    @TableField(value = "CREATETIME")
    private Date createtime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATETIME")
    private Date updatetime;

    /**
     * 0: 失效 1: 有效
     */
    @TableField(value = "`STATUS`")
    private Integer status;

    /**
     * 科目名称
     */
    @TableField(exist = false)
    private String subjectName;
}