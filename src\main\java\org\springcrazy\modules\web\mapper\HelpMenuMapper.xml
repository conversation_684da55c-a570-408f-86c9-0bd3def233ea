<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.web.mapper.HelpMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="helpMenuResultMap" type="org.springcrazy.modules.web.entity.HelpMenu">
        <id column="id" property="id"/>
        <result column="parentid" property="parentid"/>
        <result column="name" property="name"/>
        <result column="create_time" property="createTime"/>
        <result column="content" property="content"/>
        <result column="sort" property="sort"/>
        <result column="link_building" property="linkBuilding"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springcrazy.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <select id="selectHelpMenuPage" resultMap="helpMenuResultMap">
        select * from web_help_menu where is_deleted = 0
    </select>


    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, name as title, id as 'value', id as 'key' from web_help_menu ORDER BY sort DESC
    </select>
</mapper>
