package org.springcrazy.modules.user.dto;

import lombok.Data;

/**
 * 人脸比对结果
 * <a href="https://www.shumaiapi.com/productDetail/14">人脸身份证比对</a>
 */
@Data
public class FaceCardDTO {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 比较结果的描述
     */
    private String msg;
    /**
     * 生日
     */
    private String birthday;
    /**
     * 比较结果分值，0-1之间的小数，参考指标只有 0.40以下 系统判断为不同人； 0.40-0.45 不能确定是否为同一人 ； 0.45以上 系统判断为同一人
     */
    private double score;
    /**
     * 籍贯
     */
    private String address;
    /**
     * 比较结果返回码
     */
    private int incorrect;
    /**
     * 性别
     */
    private String sex;
}