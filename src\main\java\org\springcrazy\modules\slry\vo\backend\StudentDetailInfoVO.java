package org.springcrazy.modules.slry.vo.backend;

import lombok.Data;
import org.springcrazy.modules.edu.entity.TrxorderDetail;
import org.springcrazy.modules.user.entity.Student;

import java.util.List;

@Data
public class StudentDetailInfoVO {
    /**
     * 学员信息
     */
    Student student;

    List<TrxorderDetail> trxorderDetails;

    /**
     * 学员地域信息
     */
    List<StudentAddressInfoVO> studentAddressInfos;

    /**
     * 课程所有的学习抓拍照片
     */
    List<StudentDetailPictureVO> detailPictures;

}
