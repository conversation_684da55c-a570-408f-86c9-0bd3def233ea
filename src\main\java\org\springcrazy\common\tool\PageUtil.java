/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package org.springcrazy.common.tool;

import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 分页工具
 * @date 2018-12-10
 */
public class PageUtil extends cn.hutool.core.util.PageUtil {

    /**
     * List 分页
     */
    public static List toPage(int page, int size , List list) {
        int fromIndex = page * size;
        int toIndex = page * size + size;
        if(fromIndex > list.size()){
            return new ArrayList();
        } else if(toIndex >= list.size()) {
            return list.subList(fromIndex,list.size());
        } else {
            return list.subList(fromIndex,toIndex);
        }
    }

    public static <T> List<T> getPageData(List<T> data, int curPage, int pageSize) {
        if (data == null || data.isEmpty()) {
            return new ArrayList<>();
        } else if (curPage <= 0) {
            throw new RuntimeException("curPage不能小于等于0");
        } else if (pageSize <= 0) {
            throw new RuntimeException("pageSize不能小于等于0");
        }
        if (data.size() <= pageSize) {
            return data;
        }

        //总页数
        int totalPage = getTotalPage(data, pageSize);

        // 1 获取第一页
        if (curPage == 1) {
            return data.subList(0, pageSize);
        }
        // 2 中间页
        else if (curPage < totalPage) {
            return data.subList((curPage - 1) * pageSize, curPage * pageSize);
        }
        // 3 获取最后一页
        else if (curPage == totalPage) {
            return data.subList((curPage - 1) * pageSize, data.size());
        }
        // 4 非法页
        else {
            return new ArrayList<>();
        }
    }


    public static <T> int getTotalPage(List<T> data, int pageSize) {
        if (data == null || data.isEmpty()) {
            return 0;
        } else if (pageSize <= 0) {
            throw new RuntimeException("pageSize不能小于等于0");
        }
        return data.size() % pageSize == 0 ? data.size() / pageSize : data.size() / pageSize + 1;
    }



    /**
     * Page 数据处理，预防redis反序列化报错
     */
    public static Map<String,Object> toPage(Page page) {
        Map<String,Object> map = new LinkedHashMap<>(2);
        map.put("records",page.getContent());
        map.put("total",page.getTotalElements());
        return map;
    }

    /**
     * 自定义分页
     */
    public static Map<String,Object> toPage(Object object, Object totalElements) {
        Map<String,Object> map = new LinkedHashMap<>(2);
        map.put("records",object);
        map.put("total",totalElements);
        return map;
    }

}
