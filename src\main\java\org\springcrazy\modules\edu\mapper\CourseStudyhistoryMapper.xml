<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.edu.mapper.CourseStudyhistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="courseStudyhistoryResultMap" type="org.springcrazy.modules.edu.entity.CourseStudyhistory">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="kpoint_id" property="kpointId"/>
        <result column="playercount" property="playercount"/>
        <result column="course_name" property="courseName"/>
        <result column="kpoint_name" property="kpointName"/>
        <result column="databack" property="databack"/>
        <result column="update_time" property="updateTime"/>
        <result column="complete" property="complete"/>
        <result column="watch_time" property="watchTime"/>
        <result column="study_learning" property="studyLearning"/>
        <result column="mobile" property="mobile"/>
        <result column="user_name" property="userName"/>
        <result column="show_name" property="showName"/>
        <result column="realName" property="realName"/>
        <result column="videoTime" property="videoTime"/>
        <result column="kpoint_course_id" property="kpointCouserId" />
        <result column="recognize_face_time" property="recognizeFaceTime" />
    </resultMap>

    <resultMap id="courseStudyKpointMap" type="org.springcrazy.modules.edu.entity.CourseStudyhistory">
        <result column="id" property="id"/>
        <result column="course_id" property="courseId"/>
        <result column="name" property="kpointName"/>
        <result column="course_name" property="courseName"/>
        <result column="videoTime" property="videoTime"/>
        <result column="parent_id" property="parentId"/>
        <result column="sell_type" property="popeType"/>
        <result column="user_id" property="userId"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="studentDetailPictureMap" type="org.springcrazy.modules.slry.vo.backend.StudentDetailPictureVO">
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="pictures" property="pictures"/>
        <result column="address" property="address"/>
        <result column="ip" property="ip"/>
        <result column="formatted_datetime" property="formattedDatetime"/>
    </resultMap>

    <resultMap id="studentAddressInfosMap" type="org.springcrazy.modules.slry.vo.backend.StudentAddressInfoVO">
        <result column="address" property="address"/>
        <result column="ip" property="ip"/>
    </resultMap>

    <sql id="eduCourseStudyhistory">
    edu_course_studyhistory.id,
    edu_course_studyhistory.user_id,
    edu_course_studyhistory.course_id,
    edu_course_studyhistory.kpoint_id,
    edu_course_studyhistory.playercount,
    edu_course_studyhistory.course_name,
    edu_course_studyhistory.kpoint_name,
    edu_course_studyhistory.databack,
    edu_course_studyhistory.update_time,
    edu_course_studyhistory.complete,
    edu_course_studyhistory.watch_time,
    edu_course_studyhistory.study_learning
    </sql>

    <select id="selectCourseStudyhistoryPage" resultMap="courseStudyhistoryResultMap">
        select * from edu_course_studyhistory where is_deleted = 0
    </select>

    <select id="selectCourseTrxorder" resultMap="courseStudyhistoryResultMap">
         SELECT
         course_id,
        edu_course.sell_type sellType,
         MAX(auth_time) as authTime,
         pay_time as payTime,
         edu_course.course_name,
         edu_trxorder_detail.user_id
         FROM edu_trxorder_detail
	      LEFT JOIN edu_course ON edu_course.id=edu_trxorder_detail.course_id
            <where>
                edu_trxorder_detail.user_id=#{e.userId}
                AND edu_trxorder_detail.auth_status=2
                <if test="e.courseName!=null and e.courseName!=''">
                    AND edu_trxorder_detail.course_name like  concat('%',#{e.courseName},'%')
                </if>
                <if test="e.sellType!=null and e.sellType!=0">
                    AND edu_course.sell_type =  #{e.sellType}
                </if>
            </where>
           GROUP BY course_id
           ORDER BY edu_trxorder_detail.id DESC
    </select>

    <select id="getStudyTime" resultType="CourseStudyhistory">
       SELECT SUM(watch_time) as watchTime
       ,MAX(update_time) as updateTime
       FROM edu_course_studyhistory
       WHERE edu_course_studyhistory.course_id=#{courseId}
       and edu_course_studyhistory.user_id=#{userId}
    </select>

    <select id="getPackageStudyTime" resultType="CourseStudyhistory">
       SELECT SUM(watch_time) as watchTime
       ,MAX(update_time) as updateTime
       FROM edu_course_studyhistory
        <where>
            <if test="coursePackageId!=null and coursePackageId!=0">
                and edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
            <if test="courseId!=null and courseId!=0">
                and edu_course_studyhistory.course_id=#{courseId}
            </if>
            <if test="userId!=null and userId!=0">
                and edu_course_studyhistory.user_id=#{userId}
            </if>
        </where>
    </select>

    <select id="getstudystudyisok" resultType="java.lang.String">
       SELECT  COUNT(1)
            FROM edu_course_studyhistory
            LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
        <where>
            edu_course_studyhistory.complete='2'
            AND edu_trxorder_detail.auth_time >= NOW()
            AND edu_trxorder_detail.auth_status = '2'
            AND edu_trxorder_detail.clear_type = 0
            <if test="courseId!=null and courseId!=0">
                AND edu_course_studyhistory.course_id=#{courseId}
            </if>
            <if test="userId!=null and userId!=0">
                AND edu_course_studyhistory.user_id=#{userId}
            </if>
            <if test="coursePackageId!=null and coursePackageId!=0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>

    </select>

    <select id="getstudystudyisokWatchTime" resultType="java.lang.String">
        SELECT IFNULL(SUM(watch_time),0) as watchTimes
        FROM edu_course_studyhistory
        LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
        <where>
            AND edu_trxorder_detail.auth_time >= NOW()
            AND edu_trxorder_detail.auth_status = '2'
            AND edu_trxorder_detail.clear_type = 0
            <if test="courseId!=null and courseId!=0">
                AND edu_course_studyhistory.course_id=#{courseId}
            </if>
            <if test="userId!=null and userId!=0">
                AND edu_course_studyhistory.user_id=#{userId}
            </if>
            <if test="coursePackageId!=null and coursePackageId!=0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>
    </select>


    <select id="getstudystudyisokA" resultType="java.lang.String">
        SELECT  COUNT(1)
        FROM edu_course_studyhistory
        LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
        <where>
            edu_course_studyhistory.complete='2'
            AND edu_trxorder_detail.auth_time >= NOW()
            AND edu_trxorder_detail.auth_status = '2'
            AND edu_trxorder_detail.id = #{trxorderDetailId}
            <if test="courseId!=null and courseId!=0">
                AND edu_course_studyhistory.course_id=#{courseId}
            </if>
            <if test="userId!=null and userId!=0">
                AND edu_course_studyhistory.user_id=#{userId}
            </if>
            <if test="coursePackageId!=null and coursePackageId!=0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>

    </select>

    <select id="getstudykpoint" resultType="java.lang.String">
      SELECT
          COUNT(1)
        FROM
          edu_course_kpoint
        <where>
            edu_course_kpoint.parent_id != 0
            <if test="courseId!=null and courseId!=0">
                AND edu_course_kpoint.course_id = #{courseId}
            </if>
        </where>
    </select>

    <select id="getClassAllHours" resultType="java.lang.String">
        SELECT
        IFNULL(SUM(videoTime),0) as videoTimes
        FROM
        edu_course_kpoint
        <where>
            edu_course_kpoint.parent_id != 0
            <if test="courseId!=null and courseId!=0">
                AND edu_course_kpoint.course_id = #{courseId}
            </if>
        </where>
    </select>

    <select id="getStudyHours" resultType="java.lang.Integer">
        SELECT count(0)
        FROM edu_course_kpoint a
                 LEFT JOIN edu_course b ON a.course_id = b.id
                 LEFT JOIN edu_trxorder_detail c ON a.course_id = c.course_id
                 LEFT JOIN edu_student_zhengshu d ON c.zhengshu_code = d.NEWZSBH AND c.zhengshu_yxq = d.ZSYXQ
        WHERE c.auth_time >= NOW()
          AND c.auth_status = '2'
          AND d.STATUS = 1
          AND a.parent_id != 0
          AND c.clear_type = 0
          AND c.user_id = #{userId}
    </select>

    <select id="getStudyHoursNew" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(videoTime),0) as videoTimes
        FROM edu_course_kpoint a
                 LEFT JOIN edu_course b ON a.course_id = b.id
                 LEFT JOIN edu_trxorder_detail c ON a.course_id = c.course_id
                 LEFT JOIN edu_student_zhengshu d ON c.zhengshu_code = d.NEWZSBH AND c.zhengshu_yxq = d.ZSYXQ
        WHERE c.auth_time >= NOW()
          AND c.auth_status = '2'
          AND d.STATUS = 1
          AND a.parent_id != 0
          AND c.clear_type = 0
          AND c.user_id = #{userId}
    </select>

    <select id="getStudyHour" resultType="java.lang.Integer">
        SELECT count(0)
        FROM edu_course_studyhistory a
                 LEFT JOIN edu_course b ON a.course_id = b.id
                 LEFT JOIN edu_trxorder_detail c ON a.user_id = c.user_id and a.trxorder_detail_id = c.id
                 LEFT JOIN edu_student_zhengshu d ON c.zhengshu_code = d.NEWZSBH AND c.zhengshu_yxq = d.ZSYXQ
        WHERE c.user_id = #{userId}
          AND c.auth_time >= NOW()
          AND c.auth_status = '2'
          AND c.clear_type = 0
          AND a.complete = 2
          AND d.STATUS = 1
          AND a.kpoint_course_id = #{kpointCourseId}
    </select>

    <select id="getStudyHourNew" resultType="java.lang.Integer">
        SELECT IFNULL(SUM(watch_time),0) as watchTimes
        FROM edu_course_studyhistory a
                 LEFT JOIN edu_course b ON a.course_id = b.id
                 LEFT JOIN edu_trxorder_detail c ON a.user_id = c.user_id and a.trxorder_detail_id = c.id
                 LEFT JOIN edu_student_zhengshu d ON c.zhengshu_code = d.NEWZSBH AND c.zhengshu_yxq = d.ZSYXQ
        WHERE c.user_id = #{userId}
          AND c.auth_time >= NOW()
          AND c.auth_status = '2'
          AND c.clear_type = 0
          AND d.STATUS = 1
          AND a.kpoint_course_id = #{kpointCourseId}
    </select>

    <select id="getPackageStudykpoint" resultType="java.lang.String">
      SELECT
          COUNT(1)
        FROM
          edu_course_kpoint
          left join edu_course_package on edu_course_package.course_id = edu_course_kpoint.course_id
        <where>
            edu_course_kpoint.parent_id != 0
            <if test="coursePackageId!=null and coursePackageId!=0">
                AND edu_course_package.parent_course_id = #{coursePackageId}
            </if>
        </where>
    </select>

    <select id="getCourseList" resultMap="courseStudyKpointMap">
        SELECT
          edu_course_kpoint.course_id,
          edu_course_kpoint.id kpointId,
          edu_course_kpoint.id,
          edu_course_kpoint.name ,
          edu_course_kpoint.videoTime,
          edu_course_kpoint.parent_id,
          edu_course.sell_type
         FROM edu_course_kpoint
          left join edu_course
            on edu_course.id=edu_course_kpoint.course_id
            WHERE
            edu_course_kpoint.course_id =#{e.courseId}
            ORDER BY edu_course_kpoint.sort DESC
    </select>


    <select id="getCourseListByCourseId" resultMap="courseStudyKpointMap">
        SELECT
        edu_course_kpoint.course_id,
        edu_course_kpoint.id kpointId,
        edu_course_kpoint.id,
        edu_course_kpoint.name ,
        edu_course_kpoint.videoTime,
        edu_course_kpoint.parent_id,
        edu_course.sell_type,
        edu_course.course_name
        FROM edu_course_kpoint
        left join edu_course
        on edu_course.id=edu_course_kpoint.course_id
        WHERE
        edu_course_kpoint.course_id =#{courseId} and parent_id != 0
        ORDER BY edu_course_kpoint.sort ASC
    </select>

    <select id="getCourseListSort" resultMap="courseStudyKpointMap">
        SELECT
          edu_course_kpoint.course_id,
          edu_course_kpoint.id kpointId,
          edu_course_kpoint.id,
          edu_course_kpoint.name ,
          edu_course_kpoint.videoTime,
          edu_course_kpoint.parent_id,
          edu_course.sell_type
         FROM edu_course_kpoint
          left join edu_course
            on edu_course.id=edu_course_kpoint.course_id
            WHERE
            edu_course_kpoint.course_id =#{e.courseId}
    </select>

    <select id="getCoursepackageList" resultMap="courseStudyKpointMap">
        SELECT
        edu_course_package.course_id,
        edu_course.course_name,
        edu_course.sell_type
        FROM
        edu_course_package
        LEFT JOIN edu_course
        ON edu_course.id=edu_course_package.course_id
        WHERE
        edu_course_package.parent_course_id=#{e.courseId}
        and edu_course.id is not null
    </select>

    <select id="getStudyHistory" resultType="CourseStudyhistory">
       SELECT edu_course_studyhistory.*
       FROM edu_course_studyhistory
       LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
       <where>
           edu_trxorder_detail.auth_time >= NOW()
           AND edu_trxorder_detail.auth_status = '2'
           AND edu_trxorder_detail.clear_type = 0
           <if test="kpointId !=null and kpointId !=0 ">
                and edu_course_studyhistory.kpoint_id=#{kpointId}
           </if>
            <if test="userId !=null and userId !=0 ">
                and edu_course_studyhistory.user_id=#{userId}
           </if>
            <if test="coursePackageId !=null and coursePackageId !=0 ">
                and edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
           </if>
           <if test="studyType!=0">
               and edu_course_studyhistory.course_type=#{studyType}
           </if>
       </where>
    </select>


    <select id="getStudyHistoryA" resultType="CourseStudyhistory">
        SELECT edu_course_studyhistory.*
        FROM edu_course_studyhistory
        LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
        <where>
            edu_trxorder_detail.auth_time >= NOW()
            AND edu_trxorder_detail.auth_status = '2'
            and edu_trxorder_detail.id = #{trxorderDetailId}
            <if test="kpointId !=null and kpointId !=0 ">
                and edu_course_studyhistory.kpoint_id=#{kpointId}
            </if>
            <if test="userId !=null and userId !=0 ">
                and edu_course_studyhistory.user_id=#{userId}
            </if>
            <if test="coursePackageId !=null and coursePackageId !=0 ">
                and edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
            <if test="studyType!=0">
                and edu_course_studyhistory.course_type=#{studyType}
            </if>
        </where>
    </select>

    <select id="getOnesStudyHistory" resultType="CourseStudyhistory">
        SELECT edu_course_studyhistory.*
        FROM edu_course_studyhistory
        LEFT JOIN edu_trxorder_detail ON edu_trxorder_detail.id = edu_course_studyhistory.trxorder_detail_id
        <where>
            edu_trxorder_detail.auth_time >= NOW()
            AND edu_trxorder_detail.auth_status = '2'
            AND edu_trxorder_detail.clear_type = 0
            AND edu_course_studyhistory.kpoint_id=#{kpointId}
            and edu_course_studyhistory.user_id=#{userId}
            and edu_course_studyhistory.course_type=#{studyType}
            and edu_course_studyhistory.kpoint_course_id=#{packAgee}
        </where>
    </select>

    <select id="getWatchNum" resultType="java.lang.String">
      SELECT
          IFNULL(sum(playercount),0)
        FROM
          edu_course_studyhistory
        <where>
            edu_course_studyhistory.course_id = #{courseId}
            AND edu_course_studyhistory.kpoint_id = #{kpointId}
            <if test="coursePackageId==0">
                AND edu_course_studyhistory.kpoint_course_id is null
            </if>
            <if test="coursePackageId!=null and coursePackageId>0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>
    </select>

    <select id="getCourseKpointStudyHistoryDateToMap" resultType="java.util.Map">
        SELECT
        edu_course_studyhistory.kpoint_id kpointId,
        IFNULL(COUNT(edu_course_studyhistory.id),0) studyNum,
        COUNT(IF(edu_course_studyhistory.complete=2,TRUE,NULL)) studyFinishNum,
        IFNULL(sum(playercount),0) sumPlayerCount
        FROM
        edu_course_studyhistory
        <where>
            edu_course_studyhistory.course_id = #{courseId}
            <if test="coursePackageId==0">
                AND edu_course_studyhistory.kpoint_course_id = 0
            </if>
            <if test="coursePackageId>0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>
        group by edu_course_studyhistory.kpoint_id
    </select>

    <select id="getcoursepepo" resultType="java.lang.String">
      SELECT
            IFNULL(COUNT(DISTINCT user_id),0)
            FROM
            edu_trxorder_detail
            <where>
                 edu_trxorder_detail.course_id = #{courseId}
                AND auth_status=2
            </where>
    </select>

    <select id="getWatchShould" resultType="java.lang.String">
      SELECT
          IFNULL(COUNT(*),0)
        FROM
          edu_course_studyhistory
        <where>
            edu_course_studyhistory.course_id = #{courseId}
            AND edu_course_studyhistory.kpoint_id = #{kpointId}
            <if test="coursePackageId==0">
                AND edu_course_studyhistory.kpoint_course_id is null
            </if>
            <if test="coursePackageId!=null and coursePackageId>0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>
    </select>

    <select id="getWatchComplete" resultType="java.lang.String">
      SELECT
          IFNULL(COUNT(*),0)
        FROM
          edu_course_studyhistory
        <where>
            edu_course_studyhistory.course_id = #{courseId}
            AND edu_course_studyhistory.kpoint_id = #{kpointId}
            and edu_course_studyhistory.complete=2
            <if test="coursePackageId==0">
                AND edu_course_studyhistory.kpoint_course_id is null
            </if>
            <if test="coursePackageId!=null and coursePackageId>0">
                AND edu_course_studyhistory.kpoint_course_id=#{coursePackageId}
            </if>
        </where>
    </select>

    <select id="selectCourseUser" resultMap="courseStudyhistoryResultMap">
      SELECT
         edu_trxorder_detail.user_id,
         edu_student.mobile,
         edu_student.user_name,
         edu_student.show_name,
         edu_student.realName,
         edu_course.id as course_id,
         edu_course.course_name,
         edu_course_kpoint.id as kpoint_id,
         edu_course_kpoint.name as kpoint_name,
         edu_course_kpoint.videoTime
         FROM edu_trxorder_detail
             LEFT JOIN edu_student
                ON edu_trxorder_detail.user_id=edu_student.id
             LEFT JOIN edu_course
                ON edu_trxorder_detail.course_id=edu_course.id
             LEFT JOIN edu_course_kpoint
                ON edu_course_kpoint.course_id=edu_course.id
              <where> edu_trxorder_detail.course_id=#{e.courseId}
                    AND edu_course_kpoint.id=#{e.kpointId}
                    AND edu_trxorder_detail.auth_status=2
                    AND (edu_trxorder_detail.trxorder_type='COURSE' OR edu_trxorder_detail.trxorder_type='LIVE' OR edu_trxorder_detail.trxorder_type='PACKAGE')
                    <if test="e.popeType=='NO'">
                        AND edu_trxorder_detail.user_id NOT IN (#{e.userIdList})
                    </if>
                  <if test="e.mobile!=null and e.mobile!=''">
                      and edu_student.mobile like concat('%',#{e.mobile},'%')
                  </if>
                  <if test="e.userName!=null and e.userName!=''">
                      and edu_student.user_name like concat('%',#{e.userName},'%')
                  </if>
                  <if test="e.showName!=null and e.showName!=''">
                      and edu_student.show_name like concat('%',#{e.showName},'%')
                  </if>
                  <if test="e.realName!=null and e.realName!=''">
                      and edu_student.realName like concat('%',#{e.realName},'%')
                  </if>
              </where>
        GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="selectCourseUserNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_trxorder_detail.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName,
        edu_course.id as course_id,
        edu_course.course_name,
        edu_course_kpoint.id as kpoint_id,
        edu_course_kpoint.name as kpoint_name,
        edu_course_kpoint.videoTime
        FROM edu_trxorder_detail
        LEFT JOIN edu_student
        ON edu_trxorder_detail.user_id=edu_student.id
        LEFT JOIN edu_course
        ON edu_trxorder_detail.course_id=edu_course.id
        LEFT JOIN edu_course_kpoint
        ON edu_course_kpoint.course_id=edu_course.id
        <where> edu_trxorder_detail.course_id=#{e.courseId}
            AND edu_course_kpoint.id=#{e.kpointId}
            AND edu_trxorder_detail.auth_status=2
            AND (edu_trxorder_detail.trxorder_type='COURSE' OR edu_trxorder_detail.trxorder_type='LIVE' OR edu_trxorder_detail.trxorder_type='PACKAGE')
            <if test="e.popeType=='NO'">
                AND edu_trxorder_detail.user_id NOT IN (#{e.userIdList})
            </if>
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
        GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="selectStudentShouldAndComplete" resultMap="courseStudyhistoryResultMap">
      SELECT
         edu_course_studyhistory.user_id,
         edu_student.mobile,
         edu_student.user_name,
         edu_student.show_name,
         edu_student.realName,
         edu_course.id as course_id,
         edu_course.course_name,
         edu_course_kpoint.id as kpoint_id,
         edu_course_kpoint.name as kpoint_name,
         edu_course_kpoint.videoTime
         FROM edu_course_studyhistory
         LEFT JOIN edu_student
		ON edu_course_studyhistory.user_id=edu_student.id
         LEFT JOIN edu_course
            ON edu_course_studyhistory.course_id=edu_course.id
         LEFT JOIN edu_course_kpoint
		ON edu_course_studyhistory.kpoint_id=edu_course_kpoint.id
		<where>
              edu_course_studyhistory.course_id=#{e.courseId}
		      AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
              AND edu_course_studyhistory.course_type=1
            <if test="e.popeType=='COMPLETE'">
                AND edu_course_studyhistory.complete=2
            </if>
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
    </select>

    <select id="selectStudentShouldAndCompleteNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_course_studyhistory.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName,
        edu_course.id as course_id,
        edu_course.course_name,
        edu_course_kpoint.id as kpoint_id,
        edu_course_kpoint.name as kpoint_name,
        edu_course_kpoint.videoTime
        FROM edu_course_studyhistory
        LEFT JOIN edu_student
        ON edu_course_studyhistory.user_id=edu_student.id
        LEFT JOIN edu_course
        ON edu_course_studyhistory.course_id=edu_course.id
        LEFT JOIN edu_course_kpoint
        ON edu_course_studyhistory.kpoint_id=edu_course_kpoint.id
        <where>
            edu_course_studyhistory.course_id=#{e.courseId}
            AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
            AND edu_course_studyhistory.course_type=1
            <if test="e.popeType=='COMPLETE'">
                AND edu_course_studyhistory.complete=2
            </if>
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
    </select>

    <select id="getPackageList" resultMap="courseStudyKpointMap">
        SELECT
        user_id
        FROM
        edu_trxorder_detail
        WHERE edu_trxorder_detail.course_id=#{e.courseId}
        AND edu_trxorder_detail.auth_status=2
        AND trxorder_type='PACKAGE'
        GROUP BY user_id
    </select>

    <select id="getCourseWatchNum" resultType="java.lang.String">
        SELECT
            IFNULL(sum(playercount),0)
            FROM edu_course_studyhistory
            WHERE course_id=#{courseId}
              AND user_id IN (#{userList})
              AND course_type=2
    </select>

    <select id="getCourseWatchTime" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(videoTime),0)
            FROM edu_course_kpoint
            WHERE course_id=#{courseId}
    </select>

    <select id="selectNoStudy" resultMap="courseStudyhistoryResultMap">
               SELECT
                 edu_trxorder_detail.user_id,
                 edu_student.mobile,
                 edu_student.user_name,
                 edu_student.show_name,
                 edu_student.realName,
                 edu_course.id AS course_id,
                 edu_course.course_name,
                 edu_course_kpoint.id AS kpoint_id,
                 edu_course_kpoint.name AS kpoint_name,
                 edu_course_kpoint.videoTime
                    FROM
                      edu_trxorder_detail
                        LEFT JOIN edu_course_studyhistory
                        ON edu_course_studyhistory.user_id  = edu_trxorder_detail.user_id   AND edu_course_studyhistory.course_id =#{e.courseId}  AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
                        LEFT JOIN edu_student
                        ON edu_student.id  = edu_trxorder_detail.user_id
                        LEFT JOIN edu_course
                        ON edu_trxorder_detail.course_id=edu_course.id
                        LEFT JOIN edu_course_kpoint
                        ON edu_course_kpoint.course_id=edu_course.id
                            <where> edu_trxorder_detail.course_id = #{e.courseId}
                              AND edu_trxorder_detail.auth_status = 2
                              AND edu_course_studyhistory.id IS NULL
                                <if test="e.mobile!=null and e.mobile!=''">
                                    and edu_student.mobile like concat('%',#{e.mobile},'%')
                                </if>
                                <if test="e.userName!=null and e.userName!=''">
                                    and edu_student.user_name like concat('%',#{e.userName},'%')
                                </if>
                                <if test="e.showName!=null and e.showName!=''">
                                    and edu_student.show_name like concat('%',#{e.showName},'%')
                                </if>
                                <if test="e.realName!=null and e.realName!=''">
                                    and edu_student.realName like concat('%',#{e.realName},'%')
                                </if>
                            </where>
                              GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="selectNoStudyNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_trxorder_detail.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName,
        edu_course.id AS course_id,
        edu_course.course_name,
        edu_course_kpoint.id AS kpoint_id,
        edu_course_kpoint.name AS kpoint_name,
        edu_course_kpoint.videoTime
        FROM
        edu_trxorder_detail
        LEFT JOIN edu_course_studyhistory
        ON edu_course_studyhistory.user_id  = edu_trxorder_detail.user_id   AND edu_course_studyhistory.course_id =#{e.courseId}  AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
        LEFT JOIN edu_student
        ON edu_student.id  = edu_trxorder_detail.user_id
        LEFT JOIN edu_course
        ON edu_trxorder_detail.course_id=edu_course.id
        LEFT JOIN edu_course_kpoint
        ON edu_course_kpoint.course_id=edu_course.id
        <where> edu_trxorder_detail.course_id = #{e.courseId}
            AND edu_trxorder_detail.auth_status = 2
            AND edu_course_studyhistory.id IS NULL
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
        GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="selectPackageNoStudy" resultMap="courseStudyhistoryResultMap">
               SELECT
                 edu_trxorder_detail.user_id,
                 edu_student.mobile,
                 edu_student.user_name,
                 edu_student.show_name,
                 edu_student.realName
                    FROM
                      edu_trxorder_detail
                        LEFT JOIN edu_course_studyhistory
                        ON edu_course_studyhistory.user_id  = edu_trxorder_detail.user_id   AND edu_course_studyhistory.course_id =#{e.courseId}  AND edu_course_studyhistory.kpoint_id=#{e.kpointId} AND edu_course_studyhistory.course_type ='2'  AND edu_course_studyhistory.kpoint_course_id=#{e.kpointCourseId}
                        LEFT JOIN edu_student
                        ON edu_student.id  = edu_trxorder_detail.user_id
                          <where> edu_trxorder_detail.course_id = #{e.kpointCourseId}
                              AND edu_trxorder_detail.auth_status = 2
                              AND edu_course_studyhistory.id IS NULL
                              AND trxorder_type='PACKAGE'
                              <if test="e.mobile!=null and e.mobile!=''">
                                  and edu_student.mobile like concat('%',#{e.mobile},'%')
                              </if>
                              <if test="e.userName!=null and e.userName!=''">
                                  and edu_student.user_name like concat('%',#{e.userName},'%')
                              </if>
                              <if test="e.showName!=null and e.showName!=''">
                                  and edu_student.show_name like concat('%',#{e.showName},'%')
                              </if>
                              <if test="e.realName!=null and e.realName!=''">
                                  and edu_student.realName like concat('%',#{e.realName},'%')
                              </if>
                          </where>
                              GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="selectPackageNoStudyNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_trxorder_detail.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName
        FROM
        edu_trxorder_detail
        LEFT JOIN edu_course_studyhistory
        ON edu_course_studyhistory.user_id  = edu_trxorder_detail.user_id   AND edu_course_studyhistory.course_id =#{e.courseId}  AND edu_course_studyhistory.kpoint_id=#{e.kpointId} AND edu_course_studyhistory.course_type ='2'  AND edu_course_studyhistory.kpoint_course_id=#{e.kpointCourseId}
        LEFT JOIN edu_student
        ON edu_student.id  = edu_trxorder_detail.user_id
        <where> edu_trxorder_detail.course_id = #{e.kpointCourseId}
            AND edu_trxorder_detail.auth_status = 2
            AND edu_course_studyhistory.id IS NULL
            AND trxorder_type='PACKAGE'
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
        GROUP BY edu_trxorder_detail.user_id
    </select>

    <select id="getPackageUserList" resultMap="courseStudyhistoryResultMap">
        SELECT
         edu_trxorder_detail.user_id,
         edu_student.mobile,
         edu_student.user_name,
         edu_student.show_name,
         edu_student.realName
        FROM
        edu_trxorder_detail
         LEFT JOIN edu_student
         ON edu_student.id  = edu_trxorder_detail.user_id
        <where>
            edu_trxorder_detail.course_id=#{e.kpointCourseId}
            AND edu_trxorder_detail.auth_status=2
            AND edu_trxorder_detail.trxorder_type='PACKAGE'
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
        GROUP BY user_id
    </select>

    <select id="getPackageUserListNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_trxorder_detail.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName
        FROM
        edu_trxorder_detail
        LEFT JOIN edu_student
        ON edu_student.id  = edu_trxorder_detail.user_id
        <where>
            edu_trxorder_detail.course_id=#{e.kpointCourseId}
            AND edu_trxorder_detail.auth_status=2
            AND edu_trxorder_detail.trxorder_type='PACKAGE'
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
        GROUP BY user_id
    </select>

    <select id="getKpointAndCourse" resultMap="courseStudyhistoryResultMap">
       SELECT
         edu_course.id as course_id,
         edu_course.course_name,
         edu_course_kpoint.id as kpoint_id,
         edu_course_kpoint.name as kpoint_name,
         edu_course_kpoint.videoTime,
         edu_course_kpoint.recognize_face_time
          FROM edu_course
            LEFT JOIN
            edu_course_kpoint ON edu_course.id=edu_course_kpoint.course_id
            WHERE edu_course_kpoint.course_id=#{courseId} AND edu_course_kpoint.id=#{kpointId}
    </select>

    <select id="selectPackageStudentShouldAndComplete" resultMap="courseStudyhistoryResultMap">
                SELECT
                 edu_course_studyhistory.user_id,
                 edu_student.mobile,
                 edu_student.user_name,
                 edu_student.show_name,
                 edu_student.realName,
                 edu_course.id AS course_id,
                 edu_course.course_name,
                 edu_course_kpoint.id AS kpoint_id,
                 edu_course_kpoint.name AS kpoint_name,
                 edu_course_kpoint.videoTime
                     FROM edu_course_studyhistory
                        LEFT JOIN edu_student
                        ON edu_student.id  = edu_course_studyhistory.user_id
                        LEFT JOIN edu_course
                        ON edu_course_studyhistory.course_id=edu_course.id
                        LEFT JOIN edu_course_kpoint
                        ON edu_course_kpoint.id=edu_course_studyhistory.kpoint_id
                             <where>
                                     edu_course_studyhistory.course_type='2'
                                     AND edu_course_studyhistory.course_id=#{e.courseId}
                                     AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
                                     AND edu_course_studyhistory.kpoint_course_id=#{e.kpointCourseId}
                                 <if test="e.popeType=='COMPLETE'">
                                     AND edu_course_studyhistory.complete=2
                                 </if>
                                 <if test="e.mobile!=null and e.mobile!=''">
                                     and edu_student.mobile like concat('%',#{e.mobile},'%')
                                 </if>
                                 <if test="e.userName!=null and e.userName!=''">
                                     and edu_student.user_name like concat('%',#{e.userName},'%')
                                 </if>
                                 <if test="e.showName!=null and e.showName!=''">
                                     and edu_student.show_name like concat('%',#{e.showName},'%')
                                 </if>
                                 <if test="e.realName!=null and e.realName!=''">
                                     and edu_student.realName like concat('%',#{e.realName},'%')
                                 </if>
                             </where>
    </select>

    <select id="selectPackageStudentShouldAndCompleteNoPage" resultMap="courseStudyhistoryResultMap">
        SELECT
        edu_course_studyhistory.user_id,
        edu_student.mobile,
        edu_student.user_name,
        edu_student.show_name,
        edu_student.realName,
        edu_course.id AS course_id,
        edu_course.course_name,
        edu_course_kpoint.id AS kpoint_id,
        edu_course_kpoint.name AS kpoint_name,
        edu_course_kpoint.videoTime
        FROM edu_course_studyhistory
        LEFT JOIN edu_student
        ON edu_student.id  = edu_course_studyhistory.user_id
        LEFT JOIN edu_course
        ON edu_course_studyhistory.course_id=edu_course.id
        LEFT JOIN edu_course_kpoint
        ON edu_course_kpoint.id=edu_course_studyhistory.kpoint_id
        <where>
            edu_course_studyhistory.course_type='2'
            AND edu_course_studyhistory.course_id=#{e.courseId}
            AND edu_course_studyhistory.kpoint_id=#{e.kpointId}
            AND edu_course_studyhistory.kpoint_course_id=#{e.kpointCourseId}
            <if test="e.popeType=='COMPLETE'">
                AND edu_course_studyhistory.complete=2
            </if>
            <if test="e.mobile!=null and e.mobile!=''">
                and edu_student.mobile like concat('%',#{e.mobile},'%')
            </if>
            <if test="e.userName!=null and e.userName!=''">
                and edu_student.user_name like concat('%',#{e.userName},'%')
            </if>
            <if test="e.showName!=null and e.showName!=''">
                and edu_student.show_name like concat('%',#{e.showName},'%')
            </if>
            <if test="e.realName!=null and e.realName!=''">
                and edu_student.realName like concat('%',#{e.realName},'%')
            </if>
        </where>
    </select>

    <select id="getCourseUserList" resultMap="courseStudyhistoryResultMap">
        SELECT
         edu_trxorder_detail.user_id,
         edu_trxorder_detail.auth_time,
         edu_trxorder_detail.pay_time,
         edu_student.mobile,
         edu_student.user_name,
         edu_student.show_name,
         edu_trxorder_detail.course_id,
         edu_student.realName
        FROM
        edu_trxorder_detail
         LEFT JOIN edu_student
         ON edu_student.id  = edu_trxorder_detail.user_id
        WHERE edu_trxorder_detail.course_id=#{e.courseId}
        AND edu_trxorder_detail.auth_status=2
        <if test="e.mobile !=null and e.mobile !='' ">
            and edu_student.mobile like  concat('%',#{e.mobile},'%')
        </if>
        <if test="e.userName !=null and e.userName !='' ">
            and edu_student.user_name like  concat('%',#{e.userName},'%')
        </if>
        <if test="e.showName !=null and e.showName !='' ">
            and edu_student.show_name like  concat('%',#{e.showName},'%')
        </if>
        <if test="e.realName !=null and e.realName !='' ">
            and edu_student.realName like  concat('%',#{e.realName},'%')
        </if>
        GROUP BY edu_trxorder_detail.user_id
        order by edu_trxorder_detail.id desc
    </select>

    <delete id="removeByKpointId" parameterType="int">
        delete from edu_course_studyhistory where kpoint_id = #{id}
    </delete>

    <select id="getStudentDetailPicture" resultMap="studentDetailPictureMap">
        SELECT b.start_time,
               b.end_time,
               c.url pictures,
               c.address,
               c.ip
        FROM edu_course_studyhistory a
                 LEFT JOIN edu_course_history_detail b ON a.id = b.studyhistory_id
                 LEFT join edu_course_history_detail_picture c on c.history_detail_id = b.id
        WHERE a.id = #{historyId}
          AND b.end_time IS NOT NULL
        ORDER BY b.start_time
    </select>

    <select id="getStudentDetailPictureAll" resultMap="studentDetailPictureMap">
        SELECT CONCAT(
                       DATE_FORMAT(start_time, '%Y年%m月%d日%H:%i:%s'),
                       '~',
                       DATE_FORMAT(end_time, '%H:%i:%s')
               ) AS  formatted_datetime,
               c.url pictures
        FROM edu_course_studyhistory a
                 LEFT JOIN edu_course_history_detail b ON a.id = b.studyhistory_id
                 LEFT JOIN edu_course_history_detail_picture c ON c.history_detail_id = b.id
        WHERE a.trxorder_detail_id = #{trxorderDetailId}
          AND b.end_time IS NOT NULL
        ORDER BY b.start_time
    </select>

    <select id="getStudentAddressInfos" resultMap="studentAddressInfosMap">
        select distinct d.ip, d.address
        from edu_student a
                 left join edu_course_studyhistory b on b.user_id = a.id
                 left join edu_course_history_detail c on b.id = c.studyhistory_id
                 left join edu_course_history_detail_picture d on d.history_detail_id = c.id
        where a.id = #{userId}
          and d.ip is not null
    </select>
</mapper>
