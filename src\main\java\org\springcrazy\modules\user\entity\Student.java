
package org.springcrazy.modules.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学员表实体类
 *
 * <AUTHOR>
 * @since 2020-04-28
 */
@Data
@TableName("edu_student")
@ApiModel(value = "Student对象", description = "学员表")
public class Student implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 学员id
     */
    @ApiModelProperty(value = "学员id")
    @TableId(value = "id", type = IdType.AUTO)
    protected Integer id;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    protected String mobile;
    /**
     * 邮箱号
     */
    @ApiModelProperty(value = "邮箱号")
    protected String email;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    protected String password;
    /**
     * 账户名
     */
    @ApiModelProperty(value = "账户名")
    protected String userName;
    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    protected String showName;
    /**
     * 性别  1男  2女
     */
    @ApiModelProperty(value = "性别  1男  2女")
    protected Integer sex;

    /**
     * 性别  1男  2女
     */
    @ApiModelProperty(value = "名族")
    protected Integer nation;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    protected Integer age;
    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间")
    @TableField(fill = FieldFill.INSERT)
    protected LocalDateTime createTime;
    /**
     * 是否可用 2正常  1冻结
     */
    @ApiModelProperty(value = "是否可用 2正常  1冻结")
    protected Integer isAvalible;
    /**
     * 用户头像
     */
    @ApiModelProperty(value = "用户头像")
    protected String headImg;
    /**
     * 个人中心用户背景图片
     */
    @ApiModelProperty(value = "个人中心用户背景图片")
    protected String bannerUrl;
    /**
     * 站内信未读消息数
     */
    @ApiModelProperty(value = "站内信未读消息数")
    protected Integer msgNum;
    /**
     * 系统自动消息未读消息数
     */
    @ApiModelProperty(value = "系统自动消息未读消息数")
    protected Integer sysMsgNum;
    /**
     * 上传统计系统消息时间
     */
    @ApiModelProperty(value = "上传统计系统消息时间")
    protected LocalDateTime lastSystemTime;
    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号")
    protected String loginAccount;
    /**
     * 注册来源 register_from	1(pc网站) /2(h5注册) /3(app) /4(后台管理单独) 5( 5后台管理批量) 6 (代理商开通)
     */
    @ApiModelProperty(value = "注册来源 register_from 1(pc网站) /2(h5注册) /3(app) /4(后台管理单独开通) 5( 后台管理批量开通) 6 (代理商开通)")
    protected String registerFrom;
    /**
     * 邀请码
     */
    @ApiModelProperty(value = "邀请码")
    @TableField("invitationCode")
    protected String invitationCode;
    /**
     * 真实姓名
     */
    @ApiModelProperty(value = "真实姓名")
    @TableField("realName")
    protected String realName;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @TableField("idCardNo")
    protected String idCardNo;
    /**
     * 省
     */
    @ApiModelProperty(value = "省")
    protected Integer province;
    /**
     * 市
     */
    @ApiModelProperty(value = "市")
    protected Integer city;
    /**
     * 区
     */
    @ApiModelProperty(value = "区")
    protected Integer area;
    /**
     * 学校
     */
    @ApiModelProperty(value = "学校")
    protected String school;
    /**
     * 专业
     */
    @ApiModelProperty(value = "专业")
    protected String subject;
    /**
     * 签名简介
     */
    @ApiModelProperty(value = "签名简介")
    @TableField("userInfo")
    protected String userInfo;
    /**
     * 代理商id
     */
    @ApiModelProperty(value = "代理商id")
    @TableField("agent_id")
    protected Integer agentId;

    /**
     * 用户身份证号码
     */
    @ApiModelProperty(value = "用户身份证号码")
    @TableField("id_card_photo")
    protected String idCardPhoto;


    private String weibo;

    private String wechat;

    private String qq;

    private String weiboname;

    private String wechatname;

    private String qqname;

    private String openId;
    private String wechatName;

    @TableLogic
    @ApiModelProperty("是否已删除")
    private Integer isDeleted;

    /**
     * 赠送课程list
     */
    @ApiModelProperty(value = "赠送课程list")
    @TableField(exist = false)
    private List<Integer> courseIdList;

    /**
     * 是否实名制（上传身份证）
     * 0-未实名
     * 1-实名
     * 2-注册时通过人脸身份证比对实名，未上传身份证
     * 3-注册时通过人脸身份证比对实名，已上传身份证
     */
    @ApiModelProperty(value = "是否实名制")
    @TableField("is_real")
    private Integer isReal;


    /**
     * 证件照地址
     */
    @ApiModelProperty(value = "证件照地址")
    @TableField("profile_photo")
    private String profilePhoto;

    @ApiModelProperty(value = "证书信息")
    @TableField("zhengshu_info")
    private String zhengshuInfo;

    @ApiModelProperty(value = "冻结原因")
    @TableField("is_avalible_msg")
    private String isAvalibleMsg;

    @ApiModelProperty(value = "是否同步照片 0-未同步 1-已同步")
    @TableField("sync_picture_status")
    private Integer syncPictureStatus;

    /**
     * 企业名称
     */
    @TableField("company_name")
    private String companyName;

    @TableField(exist = false)
    private String[] timeArr;

    @TableField(exist = false)
    private String userFaceImage;

    /**
     * 人员类别
     */
    @TableField(exist = false)
    private String rylb;

    /**
     * 企业名称
     */
    @TableField(exist = false)
    private String qymc;

    /**
     * 证书编号
     */
    @TableField(exist = false)
    private String newzsbh;

    /**
     * 发证日期
     */
    @TableField(exist = false)
    private String fzrq;

    /**
     * 证书有效期
     */
    @TableField(exist = false)
    private String zsyxq;

}
