<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.user.mapper.UserAccountHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userAccountHistoryResultMap" type="org.springcrazy.modules.user.vo.UserAccountHistoryVO">
        <id column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="user_id" property="userId"/>
        <result column="order_no" property="orderNo"/>
        <result column="other_id" property="otherId"/>
        <result column="balance" property="balance"/>
        <result column="cash_amount" property="cashAmount"/>
        <result column="vm_amount" property="vmAmount"/>
        <result column="back_amount" property="backAmount"/>
        <result column="trx_amount" property="trxAmount"/>
        <result column="description" property="description"/>
        <result column="act_history_type" property="actHistoryType"/>
        <result column="biz_type" property="bizType"/>
        <result column="payee" property="payee"/>
        <result column="bank" property="bank"/>
        <result column="card" property="card"/>

        <result column="mobile" property="mobile"/>
        <result column="email" property="email"/>
        <result column="user_name" property="userName"/>
        <result column="login_account" property="loginAccount"/>
    </resultMap>


    <select id="selectUserAccountHistoryPage" resultMap="userAccountHistoryResultMap">
        select * from edu_user_account_history
        left join edu_student on edu_user_account_history.user_id = edu_student.id
        where 1=1
        <if test="userAccountHistory.userName !=null and userAccountHistory.userName !='' ">
            and edu_student.user_name like concat('%',#{userAccountHistory.userName},'%')
        </if>
        <if test="userAccountHistory.mobile !=null and userAccountHistory.mobile !='' ">
            and edu_student.mobile like concat('%',#{userAccountHistory.mobile},'%')
        </if>
        <if test="userAccountHistory.email !=null and userAccountHistory.email !='' ">
            and edu_student.email like concat('%',#{userAccountHistory.email},'%')
        </if>
        <if test="userAccountHistory.userId !=null and userAccountHistory.userId !='' ">
            and edu_user_account_history.user_id = #{userAccountHistory.userId}
        </if>
        <if test="userAccountHistory.actHistoryType !=null and userAccountHistory.actHistoryType !='' ">
            and edu_user_account_history.act_history_type = #{userAccountHistory.actHistoryType}
        </if>
        <if test="userAccountHistory.bizType !=null and userAccountHistory.bizType !='' ">
            and edu_user_account_history.biz_type = #{userAccountHistory.bizType}
        </if>
        <if test="userAccountHistory.createTime !=null ">
            and DATE_FORMAT(edu_user_account_history.create_time, '%Y-%m-%d') = DATE_FORMAT(#{userAccountHistory.createTime}, '%Y-%m-%d')
        </if>
        order by edu_user_account_history.create_time desc
    </select>

    <select id="exportUserAccountHistory" resultType="org.springcrazy.modules.system.excel.UserAccountHistoryExcel">
        select * from edu_user_account_history
        left join edu_student on edu_user_account_history.user_id = edu_student.id
        <where>
            <if test="ew.userId !=null and ew.userId > 0 ">
                and edu_student.id = #{ew.userId}
            </if>
            <if test="ew.userName !=null and ew.userName !='' ">
                and edu_student.user_name like concat('%',#{ew.userName},'%')
            </if>
            <if test="ew.mobile !=null and ew.mobile !='' ">
                and edu_student.mobile like concat('%',#{ew.mobile},'%')
            </if>
            <if test="ew.email !=null and ew.email !='' ">
                and edu_student.email like concat('%',#{ew.email},'%')
            </if>
            <if test="ew.actHistoryType !=null and ew.actHistoryType !='' ">
                and edu_user_account_history.act_history_type = #{ew.actHistoryType}
            </if>
            <if test="ew.bizType !=null and ew.bizType !='' ">
                and edu_user_account_history.biz_type = #{ew.bizType}
            </if>
            <if test="ew.createTime !=null ">
                and DATE_FORMAT(edu_user_account_history.create_time, '%Y-%m-%d') = DATE_FORMAT(#{ew.createTime}, '%Y-%m-%d')
            </if>
        </where>
        order by edu_user_account_history.create_time desc
    </select>

</mapper>
