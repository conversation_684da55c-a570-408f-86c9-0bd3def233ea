package org.springcrazy.modules.agent.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springcrazy.core.mp.support.Condition;
import org.springcrazy.core.mp.support.Query;
import org.springcrazy.core.secure.utils.SecureUtil;
import org.springcrazy.core.tool.api.R;
import org.springcrazy.core.tool.utils.Func;
import org.springcrazy.modules.agent.entity.AgentMonitor;
import org.springcrazy.modules.agent.service.IAgentMonitorService;
import org.springcrazy.modules.agent.vo.AgentMonitorVO;
import org.springcrazy.modules.agent.wrapper.AgentMonitorWrapper;
import org.springframework.web.bind.annotation.*;
import org.springcrazy.core.boot.ctrl.CrazyController;

import javax.validation.Valid;

/**
 * <p>
 * 监控设备 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/agent/agentmonitor")
@Api(value = "考勤机设备，记录代理商所拥有的考勤机设备", tags = "考勤机设备，记录代理商所拥有的考勤机设备")
public class AgentMonitorController extends CrazyController {

    private IAgentMonitorService agentMonitorService;

    /**
     * 分页 考勤机设备
     */
    @GetMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入Classroom")
    public R<IPage<AgentMonitorVO>> list(AgentMonitor agentMonitor, Query query) {
        //培训机构登录时只能查自己
        if (!SecureUtil.isAdministrator()){
            agentMonitor.setAgentId(SecureUtil.getUserId());
        }
        String name = agentMonitor.getName();
        String codeNum = agentMonitor.getCodeNum();
        agentMonitor.setCodeNum(null);
        agentMonitor.setName(null);
        QueryWrapper<AgentMonitor> queryWrapper =  Condition.getQueryWrapper(agentMonitor);
        queryWrapper.lambda().like(Func.isNotBlank(name),AgentMonitor::getName,name)
                .like(Func.isNotEmpty(codeNum),AgentMonitor::getCodeNum,codeNum);
        queryWrapper.lambda().orderByDesc(AgentMonitor::getCreateTime);
        IPage<AgentMonitor> pages = agentMonitorService.page(Condition.getPage(query), queryWrapper);
        return R.data(AgentMonitorWrapper.build().pageVO(pages));
    }


    /**
     * 查询单条 考勤机设备
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "查看详情", notes = "传入id")
    @GetMapping("/detail")
    public R<AgentMonitorVO> detail(AgentMonitor agentMonitor) {
        AgentMonitor detail = agentMonitorService.getOne(Condition.getQueryWrapper(agentMonitor));
        return R.data(AgentMonitorWrapper.build().entityVO(detail));
    }


    /**
     * 新增或修改 考勤机设备
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入AgentAttendance")
    public R submit(@Valid @RequestBody AgentMonitor agentMonitor) {
        return R.status(agentMonitorService.saveOrUpdate(agentMonitor));
    }

    /**
     * 删除 考勤机设备
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(agentMonitorService.deleteLogic(Func.toIntList(ids)));
    }

}

