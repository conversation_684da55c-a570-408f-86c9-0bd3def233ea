<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.slry.mapper.StudentZhengshuMapper">
  <resultMap id="BaseResultMap" type="org.springcrazy.modules.slry.entity.StudentZhengshu">
    <!--@mbg.generated-->
    <!--@Table edu_student_zhengshu-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="RYXM" jdbcType="VARCHAR" property="ryxm" />
    <result column="RYID" jdbcType="VARCHAR" property="ryid" />
    <result column="ZCZT" jdbcType="VARCHAR" property="zczt" />
    <result column="FZRQ" jdbcType="VARCHAR" property="fzrq" />
    <result column="ZSYXQ" jdbcType="VARCHAR" property="zsyxq" />
    <result column="NEWZSBH" jdbcType="VARCHAR" property="newzsbh" />
    <result column="QYMC" jdbcType="VARCHAR" property="qymc" />
    <result column="RYXB" jdbcType="VARCHAR" property="ryxb" />
    <result column="RYLB" jdbcType="VARCHAR" property="rylb" />
    <result column="SSQYTYSHXYDM" jdbcType="VARCHAR" property="ssqytyshxydm" />
    <result column="ZHIWU" jdbcType="VARCHAR" property="zhiwu" />
    <result column="ZHICHENG" jdbcType="VARCHAR" property="zhicheng" />
    <result column="FILEGUID" jdbcType="VARCHAR" property="fileguid" />
    <result column="ZSXX" jdbcType="VARCHAR" property="zsxx" />
    <result column="ZXTYPE" jdbcType="VARCHAR" property="zxtype" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="STATUS" jdbcType="INTEGER" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, RYXM, RYID, ZCZT, FZRQ, ZSYXQ, NEWZSBH, QYMC, RYXB, RYLB, SSQYTYSHXYDM, ZHIWU, 
    ZHICHENG, FILEGUID, ZSXX, ZXTYPE, CREATETIME, UPDATETIME, `STATUS`
  </sql>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="countByQymc" resultType="java.lang.Integer">
    select count(1)
    from edu_student_zhengshu
    where QYMC=#{qymc,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="countByRyid" resultType="java.lang.Integer">
    select count(1)
    from edu_student_zhengshu
    where RYID=#{ryid,jdbcType=VARCHAR}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-08-02-->
  <select id="findRyidByQymc" resultType="java.lang.String">
        select distinct RYID
        from edu_student_zhengshu
        where QYMC=#{qymc,jdbcType=VARCHAR} and STATUS = 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-03-->
  <select id="findQymcByNewzsbh" resultType="java.lang.String">
        select QYMC
        from edu_student_zhengshu
        where NEWZSBH=#{newzsbh,jdbcType=VARCHAR} limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-03-->
  <select id="findByRyid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from edu_student_zhengshu
        where RYID=#{ryid,jdbcType=VARCHAR} limit 1
    </select>

<!--auto generated by MybatisCodeHelper on 2023-08-03-->
  <select id="countDisqymc" resultType="java.lang.Integer">
        select count(distinct SSQYTYSHXYDM)
        from edu_student_zhengshu
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-14-->
  <select id="getOneByNewzsbh" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from edu_student_zhengshu
        where NEWZSBH=#{newzsbh,jdbcType=VARCHAR} limit 1
    </select>

  <select id="findSsqytyshxydmByQymcLike" resultMap="BaseResultMap">
        select distinct SSQYTYSHXYDM, QYMC
        from edu_student_zhengshu
        <where>
            <if test="likeQymc != null and likeQymc.length() >= minLength">
                QYMC like concat('%', #{likeQymc,jdbcType=VARCHAR}, '%')
            </if>
            <if test="likeQymc == null or likeQymc.length() &lt; minLength">
                1 = 0
            </if>
        </where>
        limit #{maxLimit}
    </select>
</mapper>