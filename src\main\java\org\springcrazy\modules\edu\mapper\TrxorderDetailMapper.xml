<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springcrazy.modules.edu.mapper.TrxorderDetailMapper">

    <resultMap id="trxorderDetailResultMap" type="org.springcrazy.modules.edu.vo.TrxorderDetailVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="trxorder_id" property="trxorderId"/>
        <result column="order_no" property="orderNo"/>
        <result column="losetype" property="losetype"/>
        <result column="lose_abs_time" property="loseAbsTime"/>
        <result column="lose_time" property="loseTime"/>
        <result column="begin_time" property="beginTime"/>
        <result column="auth_time" property="authTime"/>
        <result column="create_time" property="createTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="source_price" property="sourcePrice"/>
        <result column="coupon_price" property="couponPrice"/>
        <result column="current_price" property="currentPrice"/>
        <result column="course_name" property="courseName"/>
        <result column="auth_status" property="authStatus"/>
        <result column="description" property="description"/>
        <result column="last_update_time" property="lastUpdateTime"/>
        <result column="remind_status" property="remindStatus"/>
        <result column="del_status" property="delStatus"/>
        <result column="trxorder_type" property="trxorderType"/>
        <result column="logo" property="logo"/>

        <result column="clear_time" property="clearTime"/>
        <result column="clear_type" property="clearType"/>
        <result column="clear_user_id" property="clearUserId"/>
        <result column="cleared_type" property="clearedType"/>
        <result column="company_name" property="companyName"/>
        <result column="exam_score" property="examScore"/>
        <result column="exam_succes" property="examSucces"/>
        <result column="exam_success_time" property="examSuccessTime"/>
        <result column="examine_msg" property="examineMsg"/>
        <result column="examine_time" property="examineTime"/>
        <result column="examine_type" property="examineType"/>
        <result column="examine_user_id" property="examineUserId"/>
        <result column="id_card_no" property="idCardNo"/>
        <result column="mark_type" property="markType"/>
        <result column="rylb" property="rylb"/>
        <result column="sex" property="sex"/>
        <result column="study_success" property="studySuccess"/>
        <result column="study_success_time" property="studySuccessTime"/>
        <result column="subject_name" property="subjectName"/>
        <result column="user_name" property="userName"/>
        <result column="zhengshu_code" property="zhengshuCode"/>
        <result column="zhengshu_fzrq" property="zhengshuFzrq"/>
        <result column="zhengshu_yxq" property="zhengshuYxq"/>

    </resultMap>


    <select id="selectTrxorderDetailPage" resultMap="trxorderDetailResultMap">
        select
        edu_trxorder_detail.id ,
        edu_trxorder_detail.user_id ,
        edu_trxorder_detail.course_id,
        edu_trxorder_detail.trxorder_id,
        edu_trxorder_detail.order_no,
        edu_trxorder_detail.losetype,
        edu_trxorder_detail.lose_abs_time,
        edu_trxorder_detail.lose_time,
        edu_trxorder_detail.begin_time,
        edu_trxorder_detail.auth_time,
        edu_trxorder_detail.create_time,
        edu_trxorder_detail.pay_time,
        edu_trxorder_detail.source_price,
        edu_trxorder_detail.coupon_price,
        edu_trxorder_detail.current_price,
        edu_trxorder_detail.course_name,
        edu_trxorder_detail.auth_status,
        edu_trxorder_detail.description,
        edu_trxorder_detail.last_update_time,
        edu_trxorder_detail.remind_status,
        edu_trxorder_detail.del_status,
        edu_trxorder_detail.trxorder_type,
        edu_course.logo,
        edu_student.mobile mobile,
        edu_student.user_name userName,
        edu_student.show_name showName,
        edu_student.realName
        from edu_trxorder_detail
        left join edu_student on edu_trxorder_detail.user_id = edu_student.id
        left join edu_course on edu_trxorder_detail.course_id = edu_course.id
        left JOIN edu_student_zhengshu on edu_trxorder_detail.zhengshu_code = edu_student_zhengshu.NEWZSBH and
                                          edu_trxorder_detail.zhengshu_yxq = edu_student_zhengshu.ZSYXQ and
                                          edu_student_zhengshu.RYID = edu_student.idCardNo
        where edu_trxorder_detail.auth_time  <![CDATA[ >= ]]> now()
        and edu_student_zhengshu.STATUS = 1
        and edu_trxorder_detail.clear_type = 0
        <if test="trxorderDetail.trxorderId != null ">
            and edu_trxorder_detail.trxorder_id = #{trxorderDetail.trxorderId}
        </if>
        <if test="trxorderDetail.orderNo != null ">
            and edu_trxorder_detail.order_no = #{trxorderDetail.orderNo}
        </if>
        <if test="trxorderDetail.courseId != null ">
            and edu_trxorder_detail.course_id = #{trxorderDetail.courseId}
        </if>
        <if test="trxorderDetail.userId != null ">
            and edu_trxorder_detail.user_id = #{trxorderDetail.userId}
        </if>
        <if test="trxorderDetail.authStatus != null and trxorderDetail.authStatus != ''">
            and edu_trxorder_detail.auth_status = #{trxorderDetail.authStatus}
        </if>
        <if test="trxorderDetail.trxorderType != null and trxorderDetail.trxorderType != ''">
            and edu_trxorder_detail.trxorder_type = #{trxorderDetail.trxorderType}
        </if>
        order by edu_trxorder_detail.create_time desc
    </select>


    <select id="getTrxorderDetailEndPage" resultMap="trxorderDetailResultMap">
        select
        edu_trxorder_detail.user_id ,
        edu_trxorder_detail.course_id,
        edu_trxorder_detail.trxorder_id,
        edu_trxorder_detail.order_no,
        edu_trxorder_detail.losetype,
        edu_trxorder_detail.lose_abs_time,
        edu_trxorder_detail.lose_time,
        edu_trxorder_detail.begin_time,
        MAX(edu_trxorder_detail.auth_time) as auth_time,
        edu_trxorder_detail.create_time,
        edu_trxorder_detail.pay_time,
        edu_trxorder_detail.source_price,
        edu_trxorder_detail.coupon_price,
        edu_trxorder_detail.current_price,
        edu_trxorder_detail.course_name,
        edu_trxorder_detail.auth_status,
        edu_trxorder_detail.description,
        edu_trxorder_detail.last_update_time,
        edu_trxorder_detail.remind_status,
        edu_trxorder_detail.del_status,
        edu_trxorder_detail.trxorder_type,
        edu_course.logo,
        edu_student.mobile mobile,
        edu_student.user_name userName,
        edu_student.show_name showName,
        edu_student.realName
        from edu_trxorder_detail
        left join edu_student on edu_trxorder_detail.user_id = edu_student.id
        left join edu_course on edu_trxorder_detail.course_id = edu_course.id
        where edu_trxorder_detail.auth_time  <![CDATA[ < ]]> now()
        and edu_course.is_avaliable =1
        and edu_course.is_deleted =0
        <if test="trxorderDetail.trxorderId != null ">
            and edu_trxorder_detail.trxorder_id = #{trxorderDetail.trxorderId}
        </if>
        <if test="trxorderDetail.orderNo != null ">
            and edu_trxorder_detail.order_no = #{trxorderDetail.orderNo}
        </if>
        <if test="trxorderDetail.courseId != null ">
            and edu_trxorder_detail.course_id = #{trxorderDetail.courseId}
        </if>
        <if test="trxorderDetail.userId != null ">
            and edu_trxorder_detail.user_id = #{trxorderDetail.userId}
        </if>
        <if test="trxorderDetail.authStatus != null and trxorderDetail.authStatus != ''">
            and edu_trxorder_detail.auth_status = #{trxorderDetail.authStatus}
        </if>
        <if test="trxorderDetail.trxorderType != null and trxorderDetail.trxorderType != ''">
            and edu_trxorder_detail.trxorder_type = #{trxorderDetail.trxorderType}
        </if>
        group by edu_trxorder_detail.course_id
        order by edu_trxorder_detail.create_time desc
    </select>

    <select id="selectTrxorderDetailList" resultMap="trxorderDetailResultMap">
        select * from edu_trxorder_detail left join edu_course on edu_trxorder_detail.course_id = edu_course.id
        where edu_course.is_avaliable = 1
        and edu_trxorder_detail.trxorder_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by edu_trxorder_detail.create_time desc
    </select>

    <select id="selectByAll" resultMap="trxorderDetailResultMap">
        select
            a.id,a.user_id,a.course_id,a.trxorder_id,a.order_no,a.losetype,a.lose_abs_time,a.lose_time,a.begin_time,a.auth_time,
            a.create_time,a.pay_time,a.source_price,a.coupon_price,a.current_price,a.course_name,a.auth_status,a.description,
            a.last_update_time,a.remind_status,a.del_status,a.trxorder_type,a.examine_type,a.examine_user_id,a.examine_time,
            a.examine_msg,a.clear_type,a.cleared_type,a.clear_user_id,a.clear_time,a.study_success,a.study_success_time,
            a.exam_succes,a.exam_score,a.exam_success_time,a.zhengshu_code,a.zhengshu_fzrq,a.zhengshu_yxq,a.mark_type,a.user_name,
            a.id_card_no,a.company_name,a.sex,a.subject_name,
            b.rylb
        from edu_trxorder_detail a left join edu_student_zhengshu b on a.id_card_no = b.ryid and a.zhengshu_code = b.NEWZSBH
        ${ew.customSqlSegment}
    </select>

</mapper>
